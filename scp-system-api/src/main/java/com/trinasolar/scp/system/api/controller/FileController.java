package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.BaseController;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.FileDTO;
import com.trinasolar.scp.system.domain.query.FileQuery;
import com.trinasolar.scp.system.domain.save.FileSaveDTO;
import com.trinasolar.scp.system.service.service.FileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 附件表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-08 18:35:02
 */
@RestController
@RequestMapping("/file")
@Api(value = "file", tags = "附件表操作")
public class FileController extends BaseController {
    @Autowired
    FileService fileService;

    @Authorization
    @PostMapping("/page")
    @ApiOperation(value = "附件表分页列表", notes = "获得附件表分页列表")
    public ResponseEntity<Results<Page<FileDTO>>> queryByPage(@RequestBody FileQuery query) {
        return Results.createSuccessRes(fileService.queryByPage(query));
    }

    @Authorization
    @PostMapping("/list")
    @ApiOperation(value = "附件表分页列表list", notes = "获得附件表分页列表")
    public ResponseEntity<Results<List<FileDTO>>> queryByList(@RequestBody FileQuery query) {
        Page<FileDTO> fileDTOS = fileService.queryByPage(query);
        return Results.createSuccessRes(fileDTOS.getContent());
    }

    @Authorization
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<FileDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(fileService.queryById(Long.parseLong(idDTO.getId())));
    }

    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<FileDTO>> save(@Valid @RequestBody FileSaveDTO saveDTO) {
        return Results.createSuccessRes(fileService.save(saveDTO));
    }

    @Authorization
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        fileService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @Authorization
    @SneakyThrows
    @GetMapping()
    @ApiOperation("获取文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bizKey", value = "业务主键"),
            @ApiImplicitParam(name = "bizType", value = "业务类型,一般大写对应的业务表名", required = true),
    })
    public ResponseEntity<Results<List<FileDTO>>> getFilesByBizKeyAndBizType(@RequestParam(value = "bizKey", required = false) String bizKey,
                                                                             @RequestParam("bizType") String bizType) {
        return Results.createSuccessRes(fileService.getFilesByBizKeyAndBizType(bizKey, bizType));
    }
}
