package com.trinasolar.scp.system.api.controller;


import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.LovHeaderDTO;
import com.trinasolar.scp.system.domain.entity.LovHeader;
import com.trinasolar.scp.system.domain.feign.ItemAttrLovDTO;
import com.trinasolar.scp.system.domain.query.LovHeaderQuery;
import com.trinasolar.scp.system.domain.query.LovLineQuery;
import com.trinasolar.scp.system.domain.save.LovHeaderSaveDTO;
import com.trinasolar.scp.system.service.service.LovHeaderService;
import com.trinasolar.scp.system.service.service.LovLineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * lov头表(LovHeader)表控制层
 *
 * <AUTHOR>
 * @since 2022-04-24 10:42:23
 */
@Api(value = "lovHeader")
@RestController
@RequestMapping("lovHeader")
@Slf4j
public class LovHeaderController {
    /**
     * 服务对象
     */
    @Autowired
    private LovHeaderService lovHeaderService;

    @Autowired
    private LovLineService lovLineService;

    /**
     * 分页查询
     *
     * @param lovHeaderQuery 筛选条件
     * @return 查询结果
     */
    @Authorization
    @PostMapping("/list")
    @ApiOperation(value = "分页查询")
    public ResponseEntity<Results<Page<LovHeader>>> queryByPage(
            @RequestBody LovHeaderQuery lovHeaderQuery) {

        return Results.createSuccessRes(lovHeaderService.queryByPage(lovHeaderQuery));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @Authorization
    @PostMapping("/get")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<LovHeaderDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                this.lovHeaderService.queryById(Long.parseLong(idDTO.getId()))
        );
    }

    /**
     * 新增数据
     *
     * @param lovHeaderSaveDTO 实体
     * @return 新增结果
     */
    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "保存数据")
    public ResponseEntity<Results<LovHeaderDTO>> save(@Valid @RequestBody LovHeaderSaveDTO lovHeaderSaveDTO) throws BizException {
        return Results.createSuccessRes(
                this.lovHeaderService.save(lovHeaderSaveDTO)
        );
    }

    /**
     * 删除数据
     *
     * @param idDTO 主键
     * @return 删除是否成功
     */
    @Authorization
    @PostMapping("/delete")
    @ApiOperation(value = "删除数据")
    public ResponseEntity<Results<Object>> deleteById(@RequestBody IdDTO idDTO) {
        this.lovHeaderService.delete(Long.parseLong(idDTO.getId()));
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/queryByCode")
    @ApiOperation(value = "通过lovQuery查询头表数据")
    public ResponseEntity<Results<LovHeaderDTO>> queryByCode(
            @RequestBody LovLineQuery lovLineQuery
    ) {
        return Results.createSuccessRes(
                this.lovHeaderService.queryByLovQuery(lovLineQuery)
        );
    }

    @Authorization
    @PostMapping("/clearCache")
    @ApiOperation(value = "清除缓存")
    public ResponseEntity<Results<Object>> clearCache(@RequestBody LovHeaderQuery lovHeaderQuery) {
        LovUtils.clearCache();
        lovHeaderService.clearCache(lovHeaderQuery.getCode());
        return Results.createSuccessRes();
    }

    @PostMapping("/syncItemAttrLov")
    @ApiOperation(value = "同步ItemAttrLov")
    public ResponseEntity<Results<Object>> syncItemAttrLov(@RequestBody ItemAttrLovDTO itemAttrLovDTO) {
        lovHeaderService.syncItemAttrLov(itemAttrLovDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/syncItemAttrLovByBattery")
    @ApiOperation(value = "同步ItemAttrLov Battery")
    public ResponseEntity<Results<Object>> syncItemAttrLovByBattery(@RequestBody ItemAttrLovDTO itemAttrLovDTO) {
        lovHeaderService.syncItemAttrLovByBattery(itemAttrLovDTO);
        return Results.createSuccessRes();
    }
}

