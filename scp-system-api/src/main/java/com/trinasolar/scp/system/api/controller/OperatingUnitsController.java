package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.BaseController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.OperatingUnitsDTO;
import com.trinasolar.scp.system.domain.query.OperatingUnitsQuery;
import com.trinasolar.scp.system.domain.save.OperatingUnitsSaveDTO;
import com.trinasolar.scp.system.service.service.OperatingUnitsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务实体 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-07 17:43:44
 */
@RestController
@RequestMapping("/operating-units")
@Api(value = "operating-units", tags = "业务实体操作")
public class OperatingUnitsController extends BaseController {
    @Autowired
    OperatingUnitsService operatingUnitsService;

    @Authorization
    @PostMapping("/page")
    @ApiOperation(value = "业务实体分页列表", notes = "获得业务实体分页列表")
    public ResponseEntity<Results<Page<OperatingUnitsDTO>>> queryByPage(@RequestBody OperatingUnitsQuery query) {
        return Results.createSuccessRes(operatingUnitsService.queryByPage(query));
    }

    @Authorization
    @PostMapping("/list")
    @ApiOperation(value = "业务实体分页列表", notes = "获得业务实体分页列表")
    public ResponseEntity<Results<List<OperatingUnitsDTO>>> queryByList(@RequestBody OperatingUnitsQuery query) {
        return Results.createSuccessRes(operatingUnitsService.queryByList(query));
    }

    @Authorization
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<OperatingUnitsDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(operatingUnitsService.queryById(Long.parseLong(idDTO.getId())));
    }

    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<OperatingUnitsDTO>> save(@Valid @RequestBody OperatingUnitsSaveDTO saveDTO) {
        return Results.createSuccessRes(operatingUnitsService.save(saveDTO));
    }

    @Authorization
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        operatingUnitsService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody OperatingUnitsQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        operatingUnitsService.export(query, response);
    }

    @Authorization
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<String>> importData(@RequestPart("file") MultipartFile multipartFile,
                                                      @RequestPart("excelPara") ExcelPara excelPara) {
        return Results.createSuccessRes(operatingUnitsService.importData(multipartFile, excelPara));
    }

    @Authorization
    @PostMapping("/sync")
    @ApiOperation(value = "同步Erp数据")
    public ResponseEntity<Results<Object>> sync() {
        operatingUnitsService.sync();
        return Results.createSuccessRes();
    }

}
