package com.trinasolar.scp.system.api.config;

import com.alibaba.excel.EasyExcel;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * @USER: MWZ
 * @DATE: 2022/6/6
 */
public class EasyExcelUtils {


    public static void export(HttpServletResponse response,String fileName, List<List<String>> list, List<List<Object>> dataList) {
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xls");

        String sheetName =  "模板";
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            EasyExcel.write(out).head(list).sheet(sheetName).doWrite(dataList);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

}
