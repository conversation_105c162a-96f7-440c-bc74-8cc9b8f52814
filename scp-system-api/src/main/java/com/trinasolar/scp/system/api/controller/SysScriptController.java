package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.SysScriptDTO;
import com.trinasolar.scp.system.domain.query.SysScriptQuery;
import com.trinasolar.scp.system.domain.save.SysScriptSaveDTO;
import com.trinasolar.scp.system.service.service.SysScriptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 脚本维护
 *
 * <AUTHOR>
 * @date 2022-10-13
 */
@RestController
@RequestMapping("/script")
@Api(value = "/script", tags = "脚本维护")
public class SysScriptController {
    @Autowired
    private SysScriptService sysScriptService;

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @Authorization
    @PostMapping("/page")
    @ApiOperation(value = "脚本维护分页列表", notes = "脚本维护分页列表")
    public ResponseEntity<Results<Page<SysScriptDTO>>> queryByPage(@RequestBody SysScriptQuery query) {
        return Results.createSuccessRes(sysScriptService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @Authorization
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<SysScriptDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(sysScriptService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<SysScriptDTO>> save(@Valid @RequestBody SysScriptSaveDTO saveDTO) {
        return Results.createSuccessRes(sysScriptService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @Authorization
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        sysScriptService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody SysScriptQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        sysScriptService.export(query, response);
    }

    @Authorization
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<String>> importData(@RequestPart("file") MultipartFile multipartFile,
                                                      @RequestPart("excelPara") ExcelPara excelPara) {
        return Results.createSuccessRes(sysScriptService.importData(multipartFile, excelPara));
    }
}
