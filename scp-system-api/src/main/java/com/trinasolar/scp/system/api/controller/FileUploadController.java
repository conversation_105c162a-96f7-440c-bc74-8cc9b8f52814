package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.FileTokenDTO;
import com.trinasolar.scp.system.service.service.FileUploadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022/7/6
 */
@Api(value = "文件上传接口")
@RestController
@RequestMapping("/file-upload")
@Slf4j
public class FileUploadController {

    @Autowired
    FileUploadService fileUploadService;

    @Authorization
    @PostMapping("/token")
    @ApiOperation(value = "获取上传Token")
    public ResponseEntity<Results<FileTokenDTO>> token() {
        return Results.createSuccessRes(fileUploadService.getToken());
    }

    @Authorization
    @PostMapping("/upload")
    @ApiOperation(value = "获取上传Token")
    public ResponseEntity<Results<String>> upload(@RequestParam MultipartFile file) {
        return Results.createSuccessRes(fileUploadService.upload(file));
    }

}
