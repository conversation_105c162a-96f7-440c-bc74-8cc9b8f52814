package com.trinasolar.scp.system.api.controller;


import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.OrganizationDefinitionsDTO;
import com.trinasolar.scp.system.domain.entity.OrganizationDefinitions;
import com.trinasolar.scp.system.domain.query.OrganizationDefinitionsQuery;
import com.trinasolar.scp.system.domain.save.OrganizationDefinitionsSaveDTO;
import com.trinasolar.scp.system.service.service.OrganizationDefinitionsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-28 11:38:58
 */
@RestController
@RequestMapping("/organization-definitions")
@Api(value = "organization-definitions", tags = "操作")
public class OrganizationDefinitionsController {
    @Autowired
    OrganizationDefinitionsService organizationDefinitionsService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @Authorization
    @PostMapping("/list")
    @ApiOperation(value = "分页列表", notes = "获得分页列表")
    public ResponseEntity<Results<Page<OrganizationDefinitionsDTO>>> queryByPage(@RequestBody OrganizationDefinitionsQuery query) {
        return Results.createSuccessRes(organizationDefinitionsService.queryByPage(query));
    }

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @Authorization
    @PostMapping("/list2")
    @ApiOperation(value = "列表数据", notes = "获得列表数据")
    public ResponseEntity<Results<List<OrganizationDefinitionsDTO>>> queryByList(@RequestBody OrganizationDefinitionsQuery query) {
        return Results.createSuccessRes(organizationDefinitionsService.queryByList(query));
    }


    @Authorization
    @PostMapping("/listForAps")
    @ApiOperation(value = "列表数据", notes = "获得列表数据")
    public ResponseEntity<Results<List<OrganizationDefinitionsDTO>>> listForAps(@RequestBody OrganizationDefinitionsQuery query){
        return Results.createSuccessRes(organizationDefinitionsService.listForAps(query));
    }


    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @Authorization
    @PostMapping("/get")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<OrganizationDefinitionsDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                organizationDefinitionsService.queryById(Long.parseLong(idDTO.getId()))
        );
    }

    @Authorization
    @PostMapping("/getByCode")
    @ApiOperation(value = "通过Code查询单条数据")
    public ResponseEntity<Results<OrganizationDefinitionsDTO>> getByCode(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                organizationDefinitionsService.queryByCode(idDTO.getId())
        );
    }

    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "保存")
    public ResponseEntity<Results<Object>> save(@RequestBody OrganizationDefinitionsSaveDTO saveDTO) {
        organizationDefinitionsService.updateFlag(saveDTO);
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/sync")
    @ApiOperation(value = "同步数据")
    public ResponseEntity<Results<Object>> sync() {
        organizationDefinitionsService.sync();

        return Results.createSuccessRes();
    }

}
