package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.BaseController;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.ErpPeopleDTO;
import com.trinasolar.scp.system.domain.query.ErpPeopleQuery;
import com.trinasolar.scp.system.service.service.ErpPeopleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * erp人员信息 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 15:55:04
 */
@RestController
@RequestMapping("/erp-people")
@Api(value = "erp-people", tags = "erp人员信息操作")
public class ErpPeopleController extends BaseController {
    @Autowired
    ErpPeopleService erpPeopleService;

    @Authorization
    @PostMapping("/page")
    @ApiOperation(value = "erp人员信息分页列表", notes = "获得erp人员信息分页列表")
    public ResponseEntity<Results<Page<ErpPeopleDTO>>> queryByPage(@RequestBody ErpPeopleQuery query) {
        return Results.createSuccessRes(erpPeopleService.queryByPage(query));
    }

    @Authorization
    @PostMapping("/list")
    @ApiOperation(value = "erp人员信息列表", notes = "获得erp人员信息列表")
    public ResponseEntity<Results<List<ErpPeopleDTO>>> queryByList(@RequestBody ErpPeopleQuery query) {
        return Results.createSuccessRes(erpPeopleService.queryByList(query));
    }

    @Authorization
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ErpPeopleDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(erpPeopleService.queryById(Long.parseLong(idDTO.getId())));
    }

    @Authorization
    @PostMapping("/sync")
    @ApiOperation(value = "同步")
    public ResponseEntity<Results<Object>> queryById() {
        erpPeopleService.sync();
        return Results.createSuccessRes();
    }
}
