package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.LanguageDTO;
import com.trinasolar.scp.system.domain.entity.Language;
import com.trinasolar.scp.system.domain.query.LanguageQuery;
import com.trinasolar.scp.system.domain.save.LanguageSaveDTO;
import com.trinasolar.scp.system.service.service.LanguageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 系统语言 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 15:14:52
 */
@RestController
@RequestMapping("/language")
@Api(value = "language", tags = "系统语言操作")
public class LanguageController {
    @Autowired
    LanguageService languageService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @Authorization
    @PostMapping("/list")
    @ApiOperation(value = "系统语言分页列表", notes = "获得系统语言分页列表")
    public ResponseEntity<Results<Page<Language>>> queryByPage(
            @RequestBody LanguageQuery query) {
        return Results.createSuccessRes(languageService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @Authorization
    @PostMapping("/get" )
    @ApiOperation(value = "通过主键查询单条数据" )
    public ResponseEntity<Results<LanguageDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                languageService.queryById(Long.parseLong(idDTO.getId()))
        );
    }

    /**
     * 新增数据
     *
     * @param saveDTO save实体
     * @return 新增结果
     */
    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "保存数据")
    public ResponseEntity<Results<LanguageDTO>> save(@Valid @RequestBody LanguageSaveDTO saveDTO) {
        return Results.createSuccessRes(
                languageService.save(saveDTO)
        );
    }

    /**
     * 删除数据
     *
     * @param idDTO 主键
     * @return 删除是否成功
     */
    @Authorization
    @PostMapping("/delete" )
    @ApiOperation(value = "删除数据" )
    public ResponseEntity<Results<Object>> deleteById(@RequestBody IdDTO idDTO) {
        languageService.deleteById(Long.parseLong(idDTO.getId()));
        return Results.createSuccessRes();
    }
}
