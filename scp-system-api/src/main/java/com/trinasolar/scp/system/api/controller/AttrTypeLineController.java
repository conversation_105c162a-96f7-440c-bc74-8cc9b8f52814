package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.AttrTypeLineDTO;
import com.trinasolar.scp.system.domain.entity.AttrTypeLine;
import com.trinasolar.scp.system.domain.query.AttrTypeLineQuery;
import com.trinasolar.scp.system.service.service.AttrTypeLineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/4/26
 */
@Api(value = "attrTypeHeader", description = "属性类型头表")
@RestController
@RequestMapping("attrTypeLine")
@Slf4j
public class AttrTypeLineController {
    @Autowired
    AttrTypeLineService attrTypeLineService;

    /**
     * 分页查询
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @Authorization
    @PostMapping("/list")
    @ApiOperation(value = "分页查询")
    public ResponseEntity<Results<Page<AttrTypeLine>>> queryByPage(
            @RequestBody AttrTypeLineQuery query) {
        return Results.createSuccessRes(attrTypeLineService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @Authorization
    @PostMapping("/get")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<AttrTypeLineDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                attrTypeLineService.queryById(Long.parseLong(idDTO.getId()))
        );
    }


    /**
     * 通过主键查询单条数据
     *
     * @param query 主键
     * @return 单条数据
     */
    @Authorization
    @PostMapping("/queryByHeaderCode")
    @ApiOperation(value = "通过Header Code查询数据")
    public ResponseEntity<Results<List<AttrTypeLineDTO>>> queryByHeaderCode(@RequestBody AttrTypeLineQuery query) {
        return Results.createSuccessRes(
                attrTypeLineService.queryList(query)
        );
    }

    @Authorization
    @PostMapping("/queryByHeaderCodes")
    @ApiOperation(value = "通过Header Codes查询数据")
    public ResponseEntity<Results<Map<String,List<AttrTypeLineDTO>>>> queryByHeaderCode(@RequestBody List<AttrTypeLineQuery> queries) {
        Map<String,List<AttrTypeLineDTO>> result = new HashMap<>();
        for (AttrTypeLineQuery query : queries) {
            List<AttrTypeLineDTO> attrTypeLineDTOS = attrTypeLineService.queryList(query);
            result.put(query.getCode(),attrTypeLineDTOS);
        }
        return Results.createSuccessRes(result);
    }

    @Authorization
    @PostMapping("/getLineByQuery")
    @ApiOperation(value = "通过query查询信息")
    public ResponseEntity<Results<AttrTypeLineDTO>> getLineByQuery(@RequestBody AttrTypeLineQuery query) {
        AttrTypeLineDTO lineDTO = attrTypeLineService.getLineByQuery(query);
        return Results.createSuccessRes(lineDTO);
    }

    @Authorization
    @PostMapping("/getLinesByQuerys")
    @ApiOperation(value = "通过querys查询信息")
    public ResponseEntity<Results<List<AttrTypeLineDTO>>> getLineByQuery(@RequestBody List<AttrTypeLineQuery> querys) {
        List<AttrTypeLineDTO> results = new LinkedList<>();
        for (AttrTypeLineQuery query : querys) {
            Optional.ofNullable(attrTypeLineService.getLineByQuery(query))
                    .ifPresent(results::add);
        }
        return Results.createSuccessRes(results);
    }


    /**
     * 删除数据
     *
     * @param idDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除数据")
    public ResponseEntity<Results<Object>> deleteById(@RequestBody IdDTO idDTO) {
        attrTypeLineService.deleteById(Long.parseLong(idDTO.getId()));
        return Results.createSuccessRes();
    }

    @PostMapping("/syncAttrTypeLov")
    @ApiOperation(value = "设置MDM物料属性和SCP属性行关联关系")
    public ResponseEntity<Results<Object>> syncAttrTypeLov() {
        attrTypeLineService.syncAttrTypeLov();
        return Results.createSuccessRes();
    }


    /**
     * 根据 AttrLineId  找到所有 attrTypeLine数据
     * @param ids
     * @return
     */
    @PostMapping("/queryByAttrLineId")
    @ApiOperation(value = "根据 AttrLineId  找到所有 attrTypeLine数据")
    public ResponseEntity<Results<List<AttrTypeLineDTO>>> queryByAttrLineId(@RequestBody List<Long> ids) {
        return Results.createSuccessRes(attrTypeLineService.queryByAttrLineId(ids));
    }
}
