package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.BaseController;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.ErpSupplierDTO;
import com.trinasolar.scp.system.domain.dto.ErpSupplierSelectListDTO;
import com.trinasolar.scp.system.domain.query.ErpSupplierQuery;
import com.trinasolar.scp.system.domain.query.ErpSupplierSelectQuery;
import com.trinasolar.scp.system.service.service.ErpSupplierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * erp供应商表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-22 09:10:40
 */
@RestController
@RequestMapping("/erp-supplier")
@Api(value = "erp-supplier", tags = "erp供应商表操作")
public class ErpSupplierController extends BaseController {
    @Autowired
    ErpSupplierService erpSupplierService;

    @Authorization
    @PostMapping("/page")
    @ApiOperation(value = "erp供应商表分页列表", notes = "获得erp供应商表分页列表")
    public ResponseEntity<Results<Page<ErpSupplierDTO>>> queryByPage(@RequestBody ErpSupplierQuery query) {
        return Results.createSuccessRes(erpSupplierService.queryByPage(query));
    }

    @Authorization
    @PostMapping("/list")
    @ApiOperation(value = "erp供应商表列表", notes = "获得erp供应商表分页列表")
    public ResponseEntity<Results<List<ErpSupplierDTO>>> queryByList(@RequestBody ErpSupplierQuery query) {
        return Results.createSuccessRes(erpSupplierService.list(query));
    }

    @Authorization
    @PostMapping("/selectList")
    @ApiOperation(value = "erp供应商表选择列表", notes = "获得erp供应商表分页列表")
    public ResponseEntity<Results<List<ErpSupplierSelectListDTO>>> selectList(@RequestBody ErpSupplierSelectQuery query) {
        return Results.createSuccessRes(erpSupplierService.selectList(query));
    }

    @Authorization
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ErpSupplierDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(erpSupplierService.queryById(Long.parseLong(idDTO.getId())));
    }

    @Authorization
    @PostMapping("/sync")
    @ApiOperation(value = "从 接口 同步数据")
    public ResponseEntity<Results<Object>> queryById() {
        erpSupplierService.sync();
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/dailySync")
    @ApiOperation(value = "每日从 接口 同步数据")
    public ResponseEntity<Results<Object>> dailySync() {
        LocalDateTime toDateTime = LocalDateTime.now();
        LocalDateTime fromDateTime = toDateTime.minusDays(3);
        erpSupplierService.dailySync(fromDateTime, toDateTime);
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/syncFromLov")
    @ApiOperation(value = "从lov供应商同步过来")
    public ResponseEntity<Results<Object>> syncFromLov() {
        erpSupplierService.syncFromLov();
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/findByLegalEntity")
    @ApiOperation(value = "erp供应商表选择列表", notes = "获得erp供应商表分页列表")
    public ResponseEntity<Results<List<ErpSupplierDTO>>> findByLegalEntity(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(erpSupplierService.findByLegalEntity(idDTO.getId()));
    }

    @PostMapping("/findBrandSuppliersIdsBySupplierId")
    @ApiOperation(value = "获取供应商Id的供应商厂牌相同的", notes = "获得erp供应商表分页列表")
    public ResponseEntity<Results<List<Long>>> findBrandSuppliersIdsBySupplierId(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(erpSupplierService.findBrandSuppliersIdsBySupplierId(idDTO.getId()));
    }

    @PostMapping("/findSuppliersByBrand")
    @ApiOperation(value = "通过厂牌获取供应商信息", notes = "通过厂牌获取供应商信息")
    public ResponseEntity<Results<List<ErpSupplierDTO>>> findSuppliersByBrand(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(erpSupplierService.findSuppliersByBrand(idDTO.getId()));
    }

    @PostMapping("/findBrandBySupplierId")
    @ApiOperation(value = "获取供应商厂牌", notes = "获得erp供应商表分页列表")
    public ResponseEntity<Results<List<String>>> findBrandBySupplierId(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(erpSupplierService.findBrandBySupplierId(idDTO.getId()));
    }

    @Authorization
    @PostMapping("/queryByVendorAltName")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ErpSupplierDTO>> queryByVendorAltName(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(erpSupplierService.queryByVendorAltName(idDTO.getId()));
    }

    @PostMapping("/findByVendorName")
    @ApiOperation(value = "通过VendorName单条数据")
    public ResponseEntity<Results<ErpSupplierDTO>> findByVendorName(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(erpSupplierService.getByName(idDTO.getId()));
    }

    @GetMapping("/queryAllSupplierName")
    @ApiOperation(value = "获取所有供应商名称", notes = "获取所有供应商名称")
    public ResponseEntity<Results<List<String>>> queryAllSupplierName() {
        return Results.createSuccessRes(erpSupplierService.queryAllSupplierName());
    }

    @Authorization
    @PostMapping("/list2")
    @ApiOperation(value = "erp供应商表列表（新）", notes = "获得erp供应商表分页列表（新）")
    public ResponseEntity<Results<List<ErpSupplierDTO>>> queryByList2(@RequestBody ErpSupplierQuery query) {
        return Results.createSuccessRes(erpSupplierService.getErpSupplierListBy(query));
    }
}
