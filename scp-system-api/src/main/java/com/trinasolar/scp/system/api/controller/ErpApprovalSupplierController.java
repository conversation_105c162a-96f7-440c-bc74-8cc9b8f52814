package com.trinasolar.scp.system.api.controller;

import com.alibaba.fastjson.JSON;
import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.BaseController;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.ErpApprovalSupplierDTO;
import com.trinasolar.scp.system.domain.dto.ErpApprovalSupplierPageNumDTO;
import com.trinasolar.scp.system.domain.query.ErpApprovalSupplierQuery;
import com.trinasolar.scp.system.domain.save.ErpApprovalSupplierSaveDTO;
import com.trinasolar.scp.system.domain.save.ErpApprovalSupplierSavesDTO;
import com.trinasolar.scp.system.service.service.ErpApprovalSupplierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 批准供应商 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:05:46
 */
@RestController
@RequestMapping("/erp-approval-supplier")
@Api(value = "erp-approval-supplier", tags = "Erp批准供应商操作")
public class ErpApprovalSupplierController extends BaseController {
    @Autowired
    ErpApprovalSupplierService erpApprovalSupplierService;

    @Authorization
    @PostMapping("/page")
    @ApiOperation(value = "批准供应商分页列表", notes = "获得批准供应商分页列表")
    public ResponseEntity<Results<Page<ErpApprovalSupplierDTO>>> queryByPage(@RequestBody ErpApprovalSupplierQuery query) {
        return Results.createSuccessRes(erpApprovalSupplierService.queryByPage(query));
    }

    @Authorization
    @PostMapping("/querySupplierPage")
    @ApiOperation(value = "批准供应商分页列表", notes = "获得批准供应商分页列表")
    public ResponseEntity<Results<ErpApprovalSupplierPageNumDTO>> querySupplierPage(@RequestBody ErpApprovalSupplierQuery query) {
        return Results.createSuccessRes(erpApprovalSupplierService.querySupplierPage(query));
    }

    @Authorization
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ErpApprovalSupplierDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(erpApprovalSupplierService.queryById(Long.parseLong(idDTO.getId())));
    }

    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "更新数据")
    public ResponseEntity<Results<ErpApprovalSupplierDTO>> save(@RequestBody ErpApprovalSupplierSaveDTO saveDTO) {
        return Results.createSuccessRes(erpApprovalSupplierService.save(saveDTO));
    }

    @Authorization
    @PostMapping("/saves")
    @ApiOperation(value = "批量更新数据")
    public ResponseEntity<Results<Object>> save(@RequestBody ErpApprovalSupplierSavesDTO saveDTO) {
        for (ErpApprovalSupplierSaveDTO data : saveDTO.getDatas()) {
            erpApprovalSupplierService.save(data);
        }
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/sync")
    @ApiOperation(value = "同步数据")
    public ResponseEntity<Results<Object>> sync() {
        erpApprovalSupplierService.sync();
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/updateAllVendorName")
    @ApiOperation(value = "更新所有的供应商名称")
    public ResponseEntity<Results<Object>> updateAllVendorName() {
        erpApprovalSupplierService.updateAllVendorName();
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/dailySync")
    @ApiOperation(value = "每日同步数据")
    public ResponseEntity<Results<Object>> dailySync() {
        LocalDateTime toDateTime = LocalDateTime.now();
        LocalDateTime fromDateTime = toDateTime.minusDays(3);
        erpApprovalSupplierService.dailySync(fromDateTime, toDateTime);
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/updateAllCategorySegment5")
    @ApiOperation(value = "同步所有的updateAllCategorySegment5")
    public ResponseEntity<Results<Object>> updateAllCategorySegment5() {
        erpApprovalSupplierService.updateAllCategorySegment5();
        return Results.createSuccessRes();
    }

    /**
     * 导出数据
     *
     * @param response query 条件查询
     */
    @Authorization
    @PostMapping("/export")
    @ApiOperation(value = "导出收集数据")
    public void export(HttpServletResponse response, @RequestBody ErpApprovalSupplierQuery query) throws IOException {
        query.setPageSize(100000);
        erpApprovalSupplierService.export(query, response);

    }

    @Authorization
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<Object>> importData(@RequestParam("file") MultipartFile multipartFile, ErpApprovalSupplierQuery query) throws IOException {
        erpApprovalSupplierService.importData(multipartFile, JSON.parseObject(query.getExcelPara(), ExcelPara.class));
        return Results.createSuccessRes();
    }

    @PostMapping(value = "/getBrandsByItemCode")
    @ApiOperation(value = "getBrandsByItemCode")
    public ResponseEntity<Results<List<String>>> getBrandsByItemCode(@RequestBody IdDTO idDTO) {
        List<String> brands = erpApprovalSupplierService.getBrandsByItemCode(idDTO.getId());
        return Results.createSuccessRes(brands);
    }

    @PostMapping("/getItemCodeAndVendorsSetByItemCodes")
    ResponseEntity<Results<Map<String, Set<String>>>> getItemCodeAndVendorsSetByItemCodes(@RequestBody List<String> itemCodes) {
        Map<String, Set<String>> result = erpApprovalSupplierService.getItemCodeAndVendorsSetByItemCodes(itemCodes);
        return Results.createSuccessRes(result);
    }

    @PostMapping("/getItemCodeAndVendorIdSetByItemCodes")
    ResponseEntity<Results<Map<String, Set<Long>>>> getItemCodeAndVendorIdSetByItemCodes(@RequestBody List<String> itemCodes) {
        Map<String, Set<Long>> result = erpApprovalSupplierService.getItemCodeAndVendorIdSetByItemCodes(itemCodes);
        return Results.createSuccessRes(result);
    }

    @PostMapping("/getItemCodesByVendorId")
    ResponseEntity<Results<List<String>>> getItemCodesByVendorId(@RequestBody String vendorId) {
        List<String> result = erpApprovalSupplierService.getItemCodesByVendorId(vendorId);
        return Results.createSuccessRes(result);
    }

    @PostMapping("/findItemCodesByVendorId")
    ResponseEntity<Results<List<String>>> findItemCodesByVendorId(@RequestBody IdDTO vendorId) {
        List<String> result = erpApprovalSupplierService.getItemCodesByVendorId(vendorId.getId());
        return Results.createSuccessRes(result);
    }

    @PostMapping("/queryApprovalSupplier")
    ResponseEntity<Results<Map<String, List<ErpApprovalSupplierDTO>>>> queryApprovalSupplier(@RequestBody List<String> itemNums) {
        Map<String, List<ErpApprovalSupplierDTO>> result = erpApprovalSupplierService.queryApprovalSupplier(itemNums);
        return Results.createSuccessRes(result);
    }

    @PostMapping("/findApprovalSupplierByCondition")
    ResponseEntity<Results<ErpApprovalSupplierDTO>> findApprovalSupplierByCondition(@RequestBody ErpApprovalSupplierQuery query) {
        ErpApprovalSupplierDTO result = erpApprovalSupplierService.findApprovalSupplierByCondition(query);
        return Results.createSuccessRes(result);
    }
}
