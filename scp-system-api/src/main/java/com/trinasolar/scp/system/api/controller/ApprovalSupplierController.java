package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.BaseController;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.ApprovalSupplierDTO;
import com.trinasolar.scp.system.domain.query.ApprovalSupplierQuery;
import com.trinasolar.scp.system.domain.save.ApprovalSupplierSaveDTO;
import com.trinasolar.scp.system.service.service.ApprovalSupplierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 批准供应商 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-26 20:32:43
 */
@RestController
@RequestMapping("/approval-supplier")
@Api(value = "approval-supplier", tags = "批准供应商操作")
public class ApprovalSupplierController extends BaseController {
    @Autowired
    ApprovalSupplierService approvalSupplierService;

    @Authorization
    @PostMapping("/page")
    @ApiOperation(value = "批准供应商分页列表", notes = "获得批准供应商分页列表")
    public ResponseEntity<Results<Page<ApprovalSupplierDTO>>> queryByPage(@RequestBody ApprovalSupplierQuery query) {
        return Results.createSuccessRes(approvalSupplierService.queryByPage(query));
    }

    @Authorization
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ApprovalSupplierDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(approvalSupplierService.queryById(Long.parseLong(idDTO.getId())));
    }

    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<ApprovalSupplierDTO>> save(@Valid @RequestBody ApprovalSupplierSaveDTO saveDTO) {
        return Results.createSuccessRes(approvalSupplierService.save(saveDTO));
    }

    @Authorization
    @PostMapping("/sync")
    @ApiOperation(value = "从erp供应商同步过来")
    public ResponseEntity<Results<Object>> sync() {
        approvalSupplierService.sync();
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/syncFromLov")
    @ApiOperation(value = "从lov供应商同步过来")
    public ResponseEntity<Results<Object>> syncFromLov() {
        approvalSupplierService.syncFromLov();
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/dailySync")
    @ApiOperation(value = "每日同步")
    public ResponseEntity<Results<Object>> dailySync() {
        approvalSupplierService.dailySync();
        return Results.createSuccessRes();
    }
}
