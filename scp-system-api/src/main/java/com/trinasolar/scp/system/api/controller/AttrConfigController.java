package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.BaseController;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.AttrConfigDTO;
import com.trinasolar.scp.system.domain.query.AttrConfigQuery;
import com.trinasolar.scp.system.domain.save.AttrConfigSaveDTO;
import com.trinasolar.scp.system.service.service.AttrConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统扩展字段配置 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 13:58:46
 */
@RestController
@RequestMapping("/attr-config")
@Api(value = "attr-config", tags = "系统扩展字段配置操作")
public class AttrConfigController extends BaseController {

    @Autowired
    AttrConfigService attrConfigService;

    @Authorization
    @PostMapping("/page")
    @ApiOperation(value = "系统扩展字段配置分页列表", notes = "获得系统扩展字段配置分页列表")
    public ResponseEntity<Results<Page<AttrConfigDTO>>> queryByPage(@RequestBody AttrConfigQuery query) {
        return Results.createSuccessRes(attrConfigService.queryByPage(query));
    }

    @Authorization
    @PostMapping("/queryByHeaderId")
    @ApiOperation(value = "根据头Id查询", notes = "获得系统扩展字段配置根据头查询")
    public ResponseEntity<Results<List<AttrConfigDTO>>> queryByHeaderId(@RequestBody AttrConfigQuery query) {
        List<AttrConfigDTO> list = attrConfigService.listByHeaderId(query);
        return Results.createSuccessRes(list);
    }

    @Authorization
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<AttrConfigDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(attrConfigService.queryById(Long.parseLong(idDTO.getId())));
    }

    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<AttrConfigDTO>> save(@Valid @RequestBody AttrConfigSaveDTO saveDTO) {
        return Results.createSuccessRes(attrConfigService.save(saveDTO));
    }

    @Authorization
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        attrConfigService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

}
