package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.MessageDTO;
import com.trinasolar.scp.system.domain.entity.Message;
import com.trinasolar.scp.system.domain.query.MessageQuery;
import com.trinasolar.scp.system.domain.save.MessageSaveDTO;
import com.trinasolar.scp.system.domain.save.MessagesSaveDTO;
import com.trinasolar.scp.system.service.service.MessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 系统消息 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 15:14:51
 */
@RestController
@RequestMapping("/message")
@Api(value = "message", tags = "系统消息操作")
public class MessageController {
    @Autowired
    MessageService messageService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @Authorization
    @PostMapping("/list")
    @ApiOperation(value = "系统消息分页列表", notes = "获得系统消息分页列表")
    public ResponseEntity<Results<Page<Message>>> queryByPage(
            @RequestBody MessageQuery query) {
        return Results.createSuccessRes(messageService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @Authorization
    @PostMapping("/get")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<MessageDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                messageService.queryById(Long.parseLong(idDTO.getId()))
        );
    }

    /**
     * 新增数据
     *
     * @param saveDTO save实体
     * @return 新增结果
     */
    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "保存数据")
    public ResponseEntity<Results<MessageDTO>> save(@Valid @RequestBody MessageSaveDTO saveDTO) {
        return Results.createSuccessRes(
                messageService.save(saveDTO)
        );
    }

    @Authorization
    @PostMapping("/saves")
    @ApiOperation(value = "批量保存数据")
    public ResponseEntity<Results<Object>> save(@Valid @RequestBody MessagesSaveDTO saveDTOS) {
        for (MessageSaveDTO saveDTO : saveDTOS.getMessageSaveDTOS()) {
            messageService.save(saveDTO);
        }
        return Results.createSuccessRes();
    }

    /**
     * 删除数据
     *
     * @param idDTO 主键
     * @return 删除是否成功
     */
    @Authorization
    @PostMapping("/delete")
    @ApiOperation(value = "删除数据")
    public ResponseEntity<Results<Object>> deleteById(@RequestBody IdDTO idDTO) {
        messageService.deleteById(Long.parseLong(idDTO.getId()));
        return Results.createSuccessRes();
    }
}
