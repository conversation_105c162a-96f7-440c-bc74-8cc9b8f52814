package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.BaseController;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.query.ApiLogQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 缓存管理控制层
 *
 * <AUTHOR>
 * @date 2022年8月18日19:41:28
 */
@RestController
@RequestMapping("/cache")
@Api(value = "cache", tags = "缓存管理")
public class CacheController extends BaseController {
    @Authorization
    @PostMapping("/clear-all")
    @ApiOperation(value = "清理所有缓存", notes = "清理所有缓存")
    public ResponseEntity<Results<Object>> queryByPage(@RequestBody ApiLogQuery query) {
//        UserUtil.clearCache();
        throw new BizException("system.params", "清理缓存成功");
//        return Results.createSuccessRes();
    }
}
