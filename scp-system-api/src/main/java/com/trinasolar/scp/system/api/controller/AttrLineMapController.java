package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.BaseController;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.AttrLineMapDTO;
import com.trinasolar.scp.system.domain.query.AttrLineMapQuery;
import com.trinasolar.scp.system.service.service.AttrLineMapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 属性行来源映射表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-05 19:21:39
 */
@RestController
@RequestMapping("/attrLineMap")
@Api(value = "attrLineMap", tags = "属性行来源映射表操作")
public class AttrLineMapController extends BaseController{
    @Autowired
    AttrLineMapService attrLineMapService;

    @Authorization
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<AttrLineMapDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(attrLineMapService.queryById(Long.parseLong(idDTO.getId())));
    }

    @Authorization
    @PostMapping("/getByAttrLineId")
    @ApiOperation(value = "通过属性行id查询多条数据")
    public ResponseEntity<Results<List<AttrLineMapDTO>>> getByAttrLineId(@Valid @RequestBody AttrLineMapQuery query) {
        return Results.createSuccessRes(attrLineMapService.getByAttrLineId(query));
    }

    @Authorization
    @PostMapping("/batchSave")
    @ApiOperation(value = "批量保存")
    public ResponseEntity<Results<Object>> batchSave(@Valid @RequestBody List<AttrLineMapDTO> attrLineMapDTOList) {
        attrLineMapService.batchSave(attrLineMapDTOList);
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/delete")
    @ApiOperation(value = "单行删除")
    public ResponseEntity<Results<Object>> delete(@Valid @RequestBody IdDTO idDTO) {
        attrLineMapService.deleteById(Long.parseLong(idDTO.getId()));
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/getByAttrLineIds")
    @ApiOperation(value = "通过属性行列表id查询多条数据")
    public ResponseEntity<Results<List<AttrLineMapDTO>>> getByAttrLineIds(@Valid @RequestBody List<Long> attrLineIds) {
        return Results.createSuccessRes(attrLineMapService.getByAttrLineIds(attrLineIds));
    }
}
