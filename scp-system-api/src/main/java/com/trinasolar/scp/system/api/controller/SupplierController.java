package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.NoAuthorization;
import com.trinasolar.scp.common.api.base.BaseController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.SupplierDTO;
import com.trinasolar.scp.system.domain.query.SupplierQuery;
import com.trinasolar.scp.system.domain.save.SupplierSaveDTO;
import com.trinasolar.scp.system.service.service.SupplierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商信息 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-28 15:29:29
 */
@RestController
@RequestMapping("/supplier")
@Api(value = "supplier", tags = "供应商信息操作")
public class SupplierController extends BaseController {
    @Autowired
    SupplierService supplierService;

    @PostMapping("/page")
    @ApiOperation(value = "供应商信息分页列表", notes = "获得供应商信息分页列表")
    public ResponseEntity<Results<Page<SupplierDTO>>> queryByPage(@RequestBody SupplierQuery query) {
        return Results.createSuccessRes(supplierService.queryByPage(query));
    }

    @PostMapping("/list")
    @NoAuthorization
    @ApiOperation(value = "供应商信息列表", notes = "获得供应商信息列表")
    public ResponseEntity<Results<List<SupplierDTO>>> queryByList(@RequestBody SupplierQuery query) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        return Results.createSuccessRes(supplierService.queryByList(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<SupplierDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(supplierService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<SupplierDTO>> save(@Valid @RequestBody SupplierSaveDTO saveDTO) {
        return Results.createSuccessRes(supplierService.save(saveDTO));
    }
}
