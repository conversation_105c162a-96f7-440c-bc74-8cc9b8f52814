package com.trinasolar.scp.system.api.controller;

import com.alibaba.excel.EasyExcel;
import com.google.common.collect.ImmutableList;
import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.ExcelDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.WebUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

@Api(value = "excelDemo", description = "excel导入导出demo")
@RestController
@RequestMapping("excel")
@Slf4j
public class ExcelDemoController {
    @Authorization
    @PostMapping("/export")
    public void export(HttpServletResponse response) throws IOException {
        EasyExcel.write(response.getOutputStream(), ExcelDTO.class)
                .sheet("demo")
                .doWrite(() -> {
                    // 分页查询数据
                    return ImmutableList.of(
                            new ExcelDTO().setCol1("1-1").setCol2("1-2").setCol3("1-3"),
                            new ExcelDTO().setCol1("2-1").setCol2("2-2").setCol3("2-3"),
                            new ExcelDTO().setCol1("3-1").setCol2("3-2").setCol3("3-3")
                    );
                });
    }

    @Authorization
    @PostMapping(value = "/import")
    public ResponseEntity<Results<Object>> importData(@RequestParam("file") MultipartFile multipartFile) throws IOException {
        EasyExcel.read(multipartFile.getInputStream(), new MapDataListener()).sheet().doRead();
        return Results.createSuccessRes();
    }
}
