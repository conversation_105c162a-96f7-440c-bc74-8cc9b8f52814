package com.trinasolar.scp.system.api.controller;


import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.LovLineDTO;
import com.trinasolar.scp.system.domain.dto.LovLineExtDTO;
import com.trinasolar.scp.system.domain.dto.LovLinesDTO;
import com.trinasolar.scp.system.domain.dto.ShipmentMailImportDTO;
import com.trinasolar.scp.system.domain.query.LovCodesQuery;
import com.trinasolar.scp.system.domain.query.LovLineQuery;
import com.trinasolar.scp.system.domain.query.LovLineSynQuery;
import com.trinasolar.scp.system.domain.save.LovLineSaveDTO;
import com.trinasolar.scp.system.domain.save.PutValueToLovLineSaveDTO;
import com.trinasolar.scp.system.service.enums.ImportSheetNameEnum;
import com.trinasolar.scp.system.service.service.LovLineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/25
 */
@Api(value = "lovLine", description = "lov行表")
@RestController
@RequestMapping("lovLine")
@Slf4j
public class LovLineController {
    /**
     * 服务对象
     */
    @Autowired
    private LovLineService lovLineService;

    @Authorization
    @PostMapping("/queryByCode")
    @ApiOperation(value = "通过lovQuery查询行表数据")
    public ResponseEntity<Results<List<LovLineDTO>>> queryByCode(@RequestBody @Valid LovLineQuery lovLineQuery) {
        return Results.createSuccessRes(this.lovLineService.queryByLovQuery(lovLineQuery));
    }

    @Authorization
    @PostMapping("/queryByCodeNew")
    @ApiOperation(value = "通过lovQuery查询行表数据")
    public ResponseEntity<Results<List<LovLineDTO>>> queryByCodeNew(@RequestBody @Valid LovLineQuery lovLineQuery) {
        return Results.createSuccessRes(this.lovLineService.queryByCodeNew(lovLineQuery));
    }

    @Authorization
    @PostMapping("/queryAllByHeaderCode")
    @ApiOperation(value = "通过HeaderCode 查询行表数据")
    public ResponseEntity<Results<List<LovLineDTO>>> queryAllByHeaderCode(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                this.lovLineService.queryAllByHeaderCode(idDTO.getId()));
    }

    @Authorization
    @PostMapping("/queryByCodes")
    @ApiOperation(value = "通过lovQuery查询行表数据")
    public ResponseEntity<Results<ArrayList<LovLinesDTO>>> queryByCode(@RequestBody @Valid LovCodesQuery lovQuerys) {
        ArrayList<LovLinesDTO> lovLinesDTOS = new ArrayList<>();
        for (LovLineQuery lovLineQuery : lovQuerys.getLovQueries()) {
            List<LovLineDTO> lovLineDTOS = this.lovLineService.queryByLovQuery(lovLineQuery);
            LovLinesDTO lovLinesDTO = new LovLinesDTO();
            lovLinesDTO.setCode(lovLineQuery.getCode());
            lovLinesDTO.setLineDTOS(lovLineDTOS);
            lovLinesDTOS.add(lovLinesDTO);
        }

        return Results.createSuccessRes(lovLinesDTOS);
    }

    @Authorization
    @PostMapping("/getByHeaderCodeAndValue")
    @ApiOperation(value = "通过Header code和value查询行表数据")
    public ResponseEntity<Results<LovLineDTO>> getByHeaderCodeAndValue(@RequestBody @Valid LovLineQuery lovLineQuery) {
        LovLineDTO result = null;
        List<LovLineDTO> lovLineDTOS = this.lovLineService.queryByLovQuery(lovLineQuery);
        if (lovLineDTOS.size() == 1) {
            result = lovLineDTOS.get(0);
        }
        return Results.createSuccessRes(result);
    }

    @Authorization
    @PostMapping("/getByHeaderCodesAndValues")
    @ApiOperation(value = "批量通过Header code和value查询行表数据")
    public ResponseEntity<Results<List<LovLineDTO>>> getByHeaderCodesAndValues(@RequestBody @Valid List<LovLineQuery> lovLineQuerys) {
        List<LovLineDTO> result = this.lovLineService.queryByLovQuery(lovLineQuerys);
        return Results.createSuccessRes(result);
    }

    @Authorization
    @PostMapping("/getByHeaderCodesAndName")
    @ApiOperation(value = "通过Header code和name查询行表数据")
    public ResponseEntity<Results<LovLineDTO>> getByHeaderCodesAndName(@RequestBody @Valid LovLineQuery lovLineQuery) {
        LovLineDTO result = this.lovLineService.getByHeaderCodesAndName(lovLineQuery);
        return Results.createSuccessRes(result);
    }

    @Authorization
    @PostMapping("/getByLineIds")
    @ApiOperation(value = "批量通过Header code和value查询行表数据")
    public ResponseEntity<Results<List<LovLineDTO>>> getByLineIds(@RequestBody @Valid List<LovLineQuery> lovLineQuerys) {
        List<LovLineDTO> result = this.lovLineService.getByLineIds(lovLineQuerys);
        return Results.createSuccessRes(result);
    }

    @Authorization
    @PostMapping("/saveOne")
    @ApiOperation(value = "LOV行明细单行保存")
    public ResponseEntity<Results<LovLineDTO>> saveOne(@Valid @RequestBody LovLineSaveDTO lovLineSaveDTO) {
        return Results.createSuccessRes(this.lovLineService.save(lovLineSaveDTO));
    }

    @PostMapping("/putValueToLov")
    @ApiOperation(value = "LOV行明细单行保存")
    public ResponseEntity<Results<Object>> putValueToLov(@RequestBody PutValueToLovLineSaveDTO saveDTO) {
        lovLineService.putValueToLov(saveDTO);
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/delete")
    @ApiOperation(value = "LOV行明细单行删除")
    public ResponseEntity<Results<Object>> delete(@Valid @RequestBody IdDTO idDTO) {
        this.lovLineService.deleteById(Long.valueOf(idDTO.getId()));
        return Results.createSuccessRes(null);
    }

    @PostMapping("/evict")
    @ApiOperation(value = "驱逐LovId")
    public ResponseEntity<Results<Object>> evict(@Valid @RequestBody IdDTO idDTO) {
        lovLineService.evict(Long.valueOf(idDTO.getId()));
        return Results.createSuccessRes(null);
    }


    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @Authorization
    @PostMapping("/get")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<LovLineDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(this.lovLineService.queryById(Long.parseLong(idDTO.getId())));
    }

    @Authorization
    @PostMapping("/queryByPage")
    @ApiOperation(value = "通过lovQuery查询行表分页数据")
    public ResponseEntity<Results<Page<LovLineExtDTO>>> queryByPage(@RequestBody @Valid LovLineQuery lovLineQuery) {
        return Results.createSuccessRes(lovLineService.queryByPage(lovLineQuery));
    }

    @Authorization
    @PostMapping("/listByIds")
    @ApiOperation(value = "通过主键查询多条数据")
    public ResponseEntity<Results<List<LovLineDTO>>> queryByIds(@RequestBody IdsDTO idsQuery) {
        return Results.createSuccessRes(this.lovLineService.queryByIds(idsQuery));
    }

    @Authorization
    @PostMapping("/synlist")
    @ApiOperation(value = "同步数据查询")
    public ResponseEntity<Results<List<LovLineDTO>>> queryByParams(@RequestBody @Valid LovLineSynQuery query) {
        return Results.createSuccessRes(lovLineService.listDtoByDate(query.getCode(), query.getLastUpdatedTime()));
    }

    @Authorization
    @PostMapping("/saveAll")
    @ApiOperation(value = "保存所有Lov数值")
    public ResponseEntity<Results<List<LovLineDTO>>> saveAll(@RequestBody List<LovLineDTO> dtoList) {
        return Results.createSuccessRes(lovLineService.saveAll(dtoList));
    }

    @Authorization
    @PostMapping("/import")
    @ApiOperation(value = "LOV查询-值列表数据EXCEL导入")
    public ResponseEntity<Results<String>> importExcel(@RequestPart("file") MultipartFile file, @RequestPart(value = "excelPara") ExcelPara excelPara, @RequestParam(value = "lovCode", required = false) String lovCode) throws IOException {
        List<LovLineDTO> lovLineDTOS = ExcelUtils.readExcel(file.getInputStream(), null, LovLineDTO.class, excelPara);
        lovLineService.importExcel(lovLineDTOS, lovCode);
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/shipmentImport")
    @ApiOperation(value = "发货通知接收邮箱Lov数据EXCEL导入")
    public ResponseEntity<Results<String>> shipmentImportExcel(@RequestPart("file") MultipartFile file, @RequestPart(value = "excelPara1") ExcelPara excelPara1,@RequestPart(value = "excelPara2") ExcelPara excelPara2,@RequestPart(value = "excelPara3") ExcelPara excelPara3, @RequestParam(value = "lovCode", required = false) String lovCode) throws IOException {
        List<ShipmentMailImportDTO> areaList = ExcelUtils.readExcel(file.getInputStream(), ImportSheetNameEnum.AREA_EMAIL.getValue(), ShipmentMailImportDTO.class, excelPara1);
        List<ShipmentMailImportDTO> tmWorkGroupList = ExcelUtils.readExcel(file.getInputStream(), ImportSheetNameEnum.TM_WORK_GROUP.getValue(), ShipmentMailImportDTO.class, excelPara3);
        List<ShipmentMailImportDTO> orgList = ExcelUtils.readExcel(file.getInputStream(), ImportSheetNameEnum.ORG_CODE.getValue(), ShipmentMailImportDTO.class, excelPara2);

        lovLineService.importShipmentEmailExcel(areaList,tmWorkGroupList,orgList, lovCode);
        return Results.createSuccessRes();
    }
}
