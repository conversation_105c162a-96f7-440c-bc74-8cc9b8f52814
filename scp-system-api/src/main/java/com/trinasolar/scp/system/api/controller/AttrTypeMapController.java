package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.AttrTypeMapDTO;
import com.trinasolar.scp.system.domain.entity.AttrTypeMap;
import com.trinasolar.scp.system.domain.query.AttrTypeMapQuery;
import com.trinasolar.scp.system.domain.save.AttrTypeMapSaveDTO;
import com.trinasolar.scp.system.service.service.AttrTypeMapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2022/4/26
 */
@Api(value = "attrTypeMap", description = "属性类型头表")
@RestController
@RequestMapping("attrTypeMap")
@Slf4j
public class AttrTypeMapController {
    @Autowired
    AttrTypeMapService attrTypeMapService;

    /**
     * 分页查询
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @Authorization
    @PostMapping("/list")
    @ApiOperation(value = "分页查询")
    public ResponseEntity<Results<Page<AttrTypeMap>>> queryByPage(
            @RequestBody AttrTypeMapQuery query) {
        return Results.createSuccessRes(attrTypeMapService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @Authorization
    @PostMapping("/get" )
    @ApiOperation(value = "通过主键查询单条数据" )
    public ResponseEntity<Results<AttrTypeMapDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                attrTypeMapService.queryById(Long.parseLong(idDTO.getId()))
        );
    }

    /**
     * 新增数据
     *
     * @param saveDTO save实体
     * @return 新增结果
     */
    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "保存数据")
    public ResponseEntity<Results<AttrTypeMapDTO>> save(@Valid @RequestBody AttrTypeMapSaveDTO saveDTO) {
        return Results.createSuccessRes(
                attrTypeMapService.save(saveDTO)
        );
    }

    /**
     * 删除数据
     *
     * @param idDTO 主键
     * @return 删除是否成功
     */
    @Authorization
    @PostMapping("/delete" )
    @ApiOperation(value = "删除数据" )
    public ResponseEntity<Results<Object>> deleteById(@RequestBody IdDTO idDTO) {
        attrTypeMapService.deleteById(Long.parseLong(idDTO.getId()));
        return Results.createSuccessRes();
    }

}
