package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.AttrTypeHeaderDTO;
import com.trinasolar.scp.system.domain.dto.AttrTypeLineDTO;
import com.trinasolar.scp.system.domain.entity.AttrTypeHeader;
import com.trinasolar.scp.system.domain.query.AttrTypeHeaderQuery;
import com.trinasolar.scp.system.domain.query.AttrTypeHeaderSynQuery;
import com.trinasolar.scp.system.domain.save.AttrTypeHeaderSaveDTO;
import com.trinasolar.scp.system.service.service.AttrTypeHeaderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26
 */
@Api(value = "attrTypeHeader", tags = "属性类型头表")
@RestController
@RequestMapping("attrTypeHeader")
@Slf4j
public class AttrTypeHeaderController {
    @Autowired
    AttrTypeHeaderService attrTypeHeaderService;

    /**
     * 分页查询
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @Authorization
    @PostMapping("/list")
    @ApiOperation(value = "分页查询")
    public ResponseEntity<Results<Page<AttrTypeHeader>>> queryByPage(
            @RequestBody AttrTypeHeaderQuery query) {
        return Results.createSuccessRes(attrTypeHeaderService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @Authorization
    @PostMapping("/get" )
    @ApiOperation(value = "通过主键查询单条数据" )
    public ResponseEntity<Results<AttrTypeHeaderDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                attrTypeHeaderService.queryById(Long.parseLong(idDTO.getId()))
        );
    }

    @PostMapping("/getByCategorySegment5" )
    @ApiOperation(value = "通过CategorySegment5调用" )
    public ResponseEntity<Results<AttrTypeHeaderDTO>> getByCategorySegment5(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                attrTypeHeaderService.getByCategorySegment5(idDTO.getId())
        );
    }

    @GetMapping("/getByAttrTypeCode")
    @ApiOperation(value = "通过AttrTypeCode调用" )
    public ResponseEntity<Results<List<AttrTypeLineDTO>>> getByAttrTypeCode(@RequestParam String attrTypeCode) {
        return Results.createSuccessRes(
                attrTypeHeaderService.getByAttrTypeCode(attrTypeCode)
        );
    }

    @GetMapping("/getByAttrTypeName")
    @ApiOperation(value = "通过AttrTypeName调用" )
    public ResponseEntity<Results<List<AttrTypeLineDTO>>> getByAttrTypeName(@RequestParam String attrTypeName) {
        return Results.createSuccessRes(
                attrTypeHeaderService.getByAttrTypeName(attrTypeName)
        );
    }

    @PostMapping("/findItemTransToLovAttrLines")
    @ApiOperation(value = "查找Item转换到Lov所需的行,id为categorySegment5")
    public ResponseEntity<Results<List<AttrTypeLineDTO>>> findItemTransToLovAttrLines(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                attrTypeHeaderService.findItemTransToLovAttrLines(idDTO.getId())
        );
    }

    /**
     * 新增数据
     *
     * @param saveDTO save实体
     * @return 新增结果
     */
    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "保存数据")
    public ResponseEntity<Results<AttrTypeHeaderDTO>> save(@Valid @RequestBody AttrTypeHeaderSaveDTO saveDTO) {
        return Results.createSuccessRes(
                attrTypeHeaderService.save(saveDTO)
        );
    }

    /**
     * 删除数据
     *
     * @param idDTO 主键
     * @return 删除是否成功
     */
    @Authorization
    @PostMapping("/delete" )
    @ApiOperation(value = "删除数据" )
    public ResponseEntity<Results<Object>> deleteById(@RequestBody IdDTO idDTO) {
        attrTypeHeaderService.deleteById(Long.parseLong(idDTO.getId()));
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/synlist")
    @ApiOperation(value = "同步数据查询")
    public ResponseEntity<Results<List<AttrTypeHeaderDTO>>> queryByParams(
            @RequestBody @Valid AttrTypeHeaderSynQuery query) {
        return Results.createSuccessRes(attrTypeHeaderService.queryByParams(query.getAttrCategoryId(),query.getLastUpdatedTime()));
    }

    @Authorization
    @PostMapping("/getByAttrTypeHeaderIds")
    @ApiOperation(value = "通过attrTypeHeaderIds调用")
    public ResponseEntity<Results<List<AttrTypeHeaderDTO>>> getByAttrTypeHeaderIds(@Valid @RequestBody List<Long> attrTypeHeaderIds) {
        return Results.createSuccessRes(
                attrTypeHeaderService.getByAttrTypeHeaderIds(attrTypeHeaderIds)
        );
    }
}
