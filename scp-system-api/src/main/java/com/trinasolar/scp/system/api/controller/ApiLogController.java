package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.BaseController;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.ApiLogDTO;
import com.trinasolar.scp.system.domain.query.ApiLogQuery;
import com.trinasolar.scp.system.domain.save.ApiLogSaveDTO;
import com.trinasolar.scp.system.service.service.ApiLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 推送数据至其他系统接口日志表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-18 09:26:45
 */
@RestController
@RequestMapping("/apiLog")
@Api(value = "api-log", tags = "推送数据至其他系统接口日志表操作")
public class ApiLogController extends BaseController{
    @Autowired
    ApiLogService apiLogService;

    @Authorization
    @PostMapping("/page")
    @ApiOperation(value = "推送数据至其他系统接口日志表分页列表", notes = "获得推送数据至其他系统接口日志表分页列表")
    public ResponseEntity<Results<Page<ApiLogDTO>>> queryByPage(@RequestBody ApiLogQuery query) {
        return Results.createSuccessRes(apiLogService.queryByPage(query));
    }

    @Authorization
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ApiLogDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(apiLogService.queryById(Long.parseLong(idDTO.getId())));
    }

    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<ApiLogDTO>> save(@Valid @RequestBody ApiLogSaveDTO saveDTO) {
        return Results.createSuccessRes(apiLogService.save(saveDTO));
    }
}
