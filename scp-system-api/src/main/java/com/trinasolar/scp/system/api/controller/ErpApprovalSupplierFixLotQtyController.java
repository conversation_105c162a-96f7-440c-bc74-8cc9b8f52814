package com.trinasolar.scp.system.api.controller;

import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.ErpApprovalSupplierFixLotQtyDTO;
import com.trinasolar.scp.system.domain.query.ErpApprovalSupplierFixLotQtyQuery;
import com.trinasolar.scp.system.domain.save.ErpApprovalSupplierFixLotQtySaveDTO;
import com.trinasolar.scp.system.service.service.ErpApprovalSupplierFixLotQtyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.stream.Collectors;

/**
 * 合格供应商整车数或整柜数表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-09 07:56:06
 */
@RestController
@RequestMapping("/erp-approval-supplier-fix-lot-qty")
@RequiredArgsConstructor
@Api(value = "erp-approval-supplier-fix-lot-qty", tags = "合格供应商整车数或整柜数表操作")
public class ErpApprovalSupplierFixLotQtyController {
    private final ErpApprovalSupplierFixLotQtyService erpApprovalSupplierFixLotQtyService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "合格供应商整车数或整柜数表分页列表", notes = "获得合格供应商整车数或整柜数表分页列表")
    public ResponseEntity<Results<Page<ErpApprovalSupplierFixLotQtyDTO>>> queryByPage(@RequestBody ErpApprovalSupplierFixLotQtyQuery query) {
        return Results.createSuccessRes(erpApprovalSupplierFixLotQtyService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ErpApprovalSupplierFixLotQtyDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(erpApprovalSupplierFixLotQtyService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/queryLotQty")
    @ApiOperation(value = "通过物料Id+供应商+基地查询整车数")
    public ResponseEntity<Results<BigDecimal>> queryLotQty(@RequestBody ErpApprovalSupplierFixLotQtyQuery query) {
        return Results.createSuccessRes(erpApprovalSupplierFixLotQtyService.queryLotQty(query));
    }

    @PostMapping("/queryMinimumOrderQuantity")
    @ApiOperation(value = "通过物料Id+供应商+基地查询最小起订量")
    public ResponseEntity<Results<BigDecimal>> queryMinimumOrderQuantity(@RequestBody ErpApprovalSupplierFixLotQtyQuery query) {
        return Results.createSuccessRes(erpApprovalSupplierFixLotQtyService.queryMinimumOrderQuantity(query));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<ErpApprovalSupplierFixLotQtyDTO>> save(@Valid @RequestBody ErpApprovalSupplierFixLotQtySaveDTO saveDTO) {
        return Results.createSuccessRes(erpApprovalSupplierFixLotQtyService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        erpApprovalSupplierFixLotQtyService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody ErpApprovalSupplierFixLotQtyQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        erpApprovalSupplierFixLotQtyService.export(query, response);
    }

    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<String>> importData(@RequestPart("file") MultipartFile multipartFile) {
        erpApprovalSupplierFixLotQtyService.importData(multipartFile);
        return Results.createSuccessRes();
    }
}
