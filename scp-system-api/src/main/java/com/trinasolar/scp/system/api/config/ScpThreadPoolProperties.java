package com.trinasolar.scp.system.api.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * RedisProperties
 */
@Component
@ConfigurationProperties(prefix = ScpThreadPoolProperties.PREFIX)
@Data
public class ScpThreadPoolProperties {

    public static final String PREFIX = "scp.thread-pool";

    public static final String THREAD_NAME_PREFIX = "scp-thread-pool-";

    /**
     * 核心线程数 默认 2
     */
    private int corePoolSize = 2;

    /**
     * 最大线程数 默认 10
     */
    private int maxPoolSize = 10;

    /**
     * 线程完成任务后的待机存活时间 默认 60
     */
    private int keepAliveSeconds = 60;


    /**
     * 线程名前缀 默认 scp-thread-pool-
     */
    private String threadNamePrefix = THREAD_NAME_PREFIX;

    /**
     * 等待队列长度 默认 Integer.MAX_VALUE
     */
    private int queueCapacity = Integer.MAX_VALUE;

}
