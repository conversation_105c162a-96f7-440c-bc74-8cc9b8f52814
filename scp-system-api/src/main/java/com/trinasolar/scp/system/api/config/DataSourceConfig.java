package com.trinasolar.scp.system.api.config;

import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/5/25
 */
@Configuration
public class DataSourceConfig {
//    @Bean
//    @ConfigurationProperties(prefix = "spring.datasource.oracle")
//    @Qualifier("oracleDataSource")
//    DataSource oracleDataSource() {
//        return DataSourceBuilder.create().build();
//    }
//
//    @Bean(name = "oracleJdbcTemplate")
//    public JdbcTemplate oracleJdbcTemplate(
//            @Qualifier("oracleDataSource") DataSource dataSource) {
//        return new JdbcTemplate(dataSource);
//    }
}
