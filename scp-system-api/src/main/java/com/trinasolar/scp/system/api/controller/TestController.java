package com.trinasolar.scp.system.api.controller;

import com.trinasolar.scp.common.api.base.BaseDomainController;
import com.trinasolar.scp.common.api.base.BaseQuery;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.entity.LovLine;
import com.trinasolar.scp.system.service.service.LovLineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 推送数据至其他系统接口日志表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-18 09:26:45
 */
@RestController
@RequestMapping("/testController")
@Api(value = "testController", tags = "推送数据至其他系统")
public class TestController extends BaseDomainController<LovLine, Long> {

    @Autowired
    LovLineService lovLineService;
    @PostMapping({"/queryByPageBase2"})
    @ApiOperation(
            value = "分页列表",
            notes = "分页列表"
    )
    public ResponseEntity<Results<Page<LovLine>>> queryByPageBase2(@RequestBody BaseQuery<LovLine> query) {
        Page<LovLine> page = this.baseService.findAll(PageRequest.of(query.getPageNumber(), query.getPageSize()), new Object[]{query.getEntity()});
        System.out.println(baseService.hashCode()==lovLineService.hashCode());
        return Results.createSuccessRes(page);
    }
    public void test()
    {
    }
}
