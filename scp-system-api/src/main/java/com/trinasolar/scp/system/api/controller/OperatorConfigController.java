package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.system.domain.query.OperatorConfigQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.system.domain.save.OperatorConfigSaveDTO;
import com.trinasolar.scp.system.domain.entity.OperatorConfig;
import com.trinasolar.scp.system.domain.dto.OperatorConfigDTO;
import com.trinasolar.scp.system.service.service.OperatorConfigService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 操作配置表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-01 09:45:41
 */
@RestController
@RequestMapping("/operator-config")
@Api(value = "operator-config", tags = "操作配置表操作")
public class OperatorConfigController extends BaseController{
    @Autowired
    OperatorConfigService operatorConfigService;

    @Authorization
    @PostMapping("/page")
    @ApiOperation(value = "操作配置表分页列表", notes = "获得操作配置表分页列表")
    public ResponseEntity<Results<Page<OperatorConfigDTO>>> queryByPage(@RequestBody OperatorConfigQuery query) {
        return Results.createSuccessRes(operatorConfigService.queryByPage(query));
    }

    @Authorization
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<OperatorConfigDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(operatorConfigService.queryById(Long.parseLong(idDTO.getId())));
    }

    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<OperatorConfigDTO>> save(@Valid @RequestBody OperatorConfigSaveDTO saveDTO) {
        return Results.createSuccessRes(operatorConfigService.save(saveDTO));
    }

    @Authorization
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        operatorConfigService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody OperatorConfigQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        setResponseWithExcel(response, null);
        operatorConfigService.export(query, response);
    }

    @Authorization
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<String>> importData(@RequestParam("file") MultipartFile multipartFile) {
        return Results.createSuccessRes(operatorConfigService.importData(multipartFile));
    }

    @Authorization
    @PostMapping(value = "/userMenu")
    @ApiOperation(value = "根据菜单名称和菜单路劲获取对应的数据")
    public ResponseEntity<Results<OperatorConfigDTO>> findByUserMenu(@RequestBody OperatorConfigQuery query) {
        return Results.createSuccessRes(operatorConfigService.findByUserMenu(query));
    }
}
