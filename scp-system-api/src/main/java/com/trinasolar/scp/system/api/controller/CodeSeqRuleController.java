package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.CodeSeqRuleDTO;
import com.trinasolar.scp.system.domain.entity.CodeSeqRule;
import com.trinasolar.scp.system.domain.query.CodeSeqRuleQuery;
import com.trinasolar.scp.system.domain.save.CodeSeqRuleSaveDTO;
import com.trinasolar.scp.system.service.service.CodeSeqRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 单据序列规则定义表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-09 15:05:51
 */
@RestController
@RequestMapping("/code-seq-rule")
@Api(value = "code-seq-rule", tags = "单据序列规则定义表操作")
public class CodeSeqRuleController {
    @Autowired
    CodeSeqRuleService codeSeqRuleService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @Authorization
    @PostMapping("/page")
    @ApiOperation(value = "单据序列规则定义表分页列表", notes = "获得单据序列规则定义表分页列表")
    public ResponseEntity<Results<Page<CodeSeqRule>>> queryByPage(@RequestBody CodeSeqRuleQuery query) {
        return Results.createSuccessRes(codeSeqRuleService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @Authorization
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CodeSeqRuleDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(codeSeqRuleService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CodeSeqRuleDTO>> save(@Valid @RequestBody CodeSeqRuleSaveDTO saveDTO) {
        return Results.createSuccessRes(codeSeqRuleService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @Authorization
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        codeSeqRuleService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }
}
