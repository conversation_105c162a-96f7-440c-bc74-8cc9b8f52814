package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.LovLineTlTableLineDTO;
import com.trinasolar.scp.system.domain.dto.LovTlDTO;
import com.trinasolar.scp.system.domain.entity.LovTl;
import com.trinasolar.scp.system.domain.query.LovTlQuery;
import com.trinasolar.scp.system.domain.save.LovLineTlTableLinesSaveDTO;
import com.trinasolar.scp.system.domain.save.LovTlSaveDTO;
import com.trinasolar.scp.system.service.service.LovTlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 系统LOV多语言表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 15:14:51
 */
@RestController
@RequestMapping("/lov-tl")
@Api(value = "lov-tl", tags = "系统LOV多语言表操作")
public class LovTlController {
    @Autowired
    LovTlService lovTlService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @Authorization
    @PostMapping("/list")
    @ApiOperation(value = "系统LOV多语言表分页列表", notes = "获得系统LOV多语言表分页列表")
    public ResponseEntity<Results<Page<LovTl>>> queryByPage(
            @RequestBody LovTlQuery query) {
        return Results.createSuccessRes(lovTlService.queryByPage(query));
    }

    @Authorization
    @PostMapping("/findAllByPkTypeAndPkIds")
    @ApiOperation(value = "根据PkType和PkIds查询", notes = "获得系统LOV多语言表分页列表")
    public ResponseEntity<Results<List<LovTlDTO>>> findAllByPkTypeAndPkIds(
            @RequestBody LovTlQuery query) {
        return Results.createSuccessRes(lovTlService.findAllByPkTypeAndPkIdIn(query));
    }

    @Authorization
    @PostMapping("/getLovLineTlTableLines")
    @ApiOperation(value = "获取Lov行多语言翻译")
    public ResponseEntity<Results<List<LovLineTlTableLineDTO>>> getLovLineTlTableLines(
            @RequestBody IdDTO idDTO) {
        List<LovLineTlTableLineDTO> lines = lovTlService.getLovLineTlTableLines(idDTO);
        return Results.createSuccessRes(lines);
    }

    @Authorization
    @PostMapping("/saveLovLineTlTableLines")
    @ApiOperation(value = "保存Lov行多语言翻译")
    public ResponseEntity<Results<Object>> saveLovLineTlTableLines(
            @RequestBody LovLineTlTableLinesSaveDTO lines) {
        lovTlService.saveLovLineTlTableLines(lines);
        return Results.createSuccessRes();
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @Authorization
    @PostMapping("/get")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<LovTlDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                lovTlService.queryById(Long.parseLong(idDTO.getId()))
        );
    }

    /**
     * 新增数据
     *
     * @param saveDTO save实体
     * @return 新增结果
     */
    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "保存数据")
    public ResponseEntity<Results<LovTlDTO>> save(@Valid @RequestBody LovTlSaveDTO saveDTO) {
        return Results.createSuccessRes(
                lovTlService.save(saveDTO)
        );
    }

    /**
     * 删除数据
     *
     * @param idDTO 主键
     * @return 删除是否成功
     */
    @Authorization
    @PostMapping("/delete" )
    @ApiOperation(value = "删除数据" )
    public ResponseEntity<Results<Object>> deleteById(@RequestBody IdDTO idDTO) {
        lovTlService.deleteById(Long.parseLong(idDTO.getId()));
        return Results.createSuccessRes();
    }
}
