package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.PromptDTO;
import com.trinasolar.scp.system.domain.entity.Prompt;
import com.trinasolar.scp.system.domain.query.PromptQuery;
import com.trinasolar.scp.system.domain.save.PromptSaveDTO;
import com.trinasolar.scp.system.domain.save.PromptsSaveDTO;
import com.trinasolar.scp.system.service.service.PromptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 前端多语言配置 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 15:14:52
 */
@RestController
@RequestMapping("/prompt")
@Api(value = "prompt", tags = "前端多语言配置操作")
public class PromptController {
    @Autowired
    PromptService promptService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @Authorization
    @PostMapping("/list")
    @ApiOperation(value = "前端多语言配置分页列表", notes = "获得前端多语言配置分页列表")
    public ResponseEntity<Results<Page<Prompt>>> queryByPage(
            @RequestBody PromptQuery query) {
        return Results.createSuccessRes(promptService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @Authorization
    @PostMapping("/get")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<PromptDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                promptService.queryById(Long.parseLong(idDTO.getId()))
        );
    }

    /**
     * 新增数据
     *
     * @param saveDTO save实体
     * @return 新增结果
     */
    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "保存数据")
    public ResponseEntity<Results<PromptDTO>> save(@Valid @RequestBody PromptSaveDTO saveDTO) {
        return Results.createSuccessRes(
                promptService.save(saveDTO)
        );
    }

    @Authorization
    @PostMapping("/saves")
    @ApiOperation(value = "批量保存数据")
    public ResponseEntity<Results<Object>> saves(@Valid @RequestBody PromptsSaveDTO saveDTOS) {
        saveDTOS.getPromptSaveDTOS().forEach(s -> promptService.save(s));
        return Results.createSuccessRes();
    }

    /**
     * 删除数据
     *
     * @param idDTO 主键
     * @return 删除是否成功
     */
    @Authorization
    @PostMapping("/delete")
    @ApiOperation(value = "删除数据")
    public ResponseEntity<Results<Object>> deleteById(@RequestBody IdDTO idDTO) {
        promptService.deleteById(Long.parseLong(idDTO.getId()));
        return Results.createSuccessRes();
    }


    /**
     * 获取多语言标签描述
     *
     * @param query 多语言查询支持lang
     * @return 统一返回结果
     * <AUTHOR> 2018-06-25 11:27
     */
    @Authorization
    @ApiOperation(value = "获取多语言描述")
    @PostMapping("/getDescription")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "lang", value = "语言", required = true),
    })
    public ResponseEntity getDescription(@RequestBody PromptQuery query) {
        return Results.createSuccessRes(promptService.getDescription(query));
    }


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @Authorization
    @PostMapping("/getTransByDescription")
    @ApiOperation(value = "根据描述获取翻译", notes = "根据描述获取翻译")
    public ResponseEntity<Results<List<Prompt>>> getTransByDescription(@RequestBody PromptQuery query) {
        return Results.createSuccessRes(promptService.getTransByDescription(query));
    }
}
