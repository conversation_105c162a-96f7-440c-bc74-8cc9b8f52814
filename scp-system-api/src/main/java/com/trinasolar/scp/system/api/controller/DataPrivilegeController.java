package com.trinasolar.scp.system.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.trinasolar.scp.common.api.base.BaseController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.DataPrivilegeDTO;
import com.trinasolar.scp.system.domain.query.DataPrivilegeQuery;
import com.trinasolar.scp.system.domain.save.DataPrivilegeSaveDTO;
import com.trinasolar.scp.system.service.service.DataPrivilegeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据权限表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-17 15:39:12
 */
@RestController
@RequestMapping("/data-privilege")
@Api(value = "data-privilege", tags = "数据权限表操作")
public class DataPrivilegeController extends BaseController {
    @Autowired
    DataPrivilegeService dataPrivilegeService;

    @Authorization
    @PostMapping("/page")
    @ApiOperation(value = "数据权限表分页列表", notes = "获得数据权限表分页列表")
    public ResponseEntity<Results<Page<DataPrivilegeDTO>>> queryByPage(@RequestBody DataPrivilegeQuery query) {
        return Results.createSuccessRes(dataPrivilegeService.queryByPage(query));
    }

    @Authorization
    @PostMapping("/list")
    @ApiOperation(value = "数据权限表列表", notes = "获得数据权限表列表")
    public ResponseEntity<Results<List<DataPrivilegeDTO>>> queryByList(@RequestBody DataPrivilegeQuery query) {
        return Results.createSuccessRes(dataPrivilegeService.queryByList(query));
    }

    @Authorization
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<DataPrivilegeDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(dataPrivilegeService.queryById(Long.parseLong(idDTO.getId())));
    }

    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<DataPrivilegeDTO>> save(@Valid @RequestBody DataPrivilegeSaveDTO saveDTO) {
        return Results.createSuccessRes(dataPrivilegeService.save(saveDTO));
    }

    @Authorization
    @PostMapping("/batch-save")
    @ApiOperation(value = "批量新增或更新数据")
    public ResponseEntity<Results<DataPrivilegeDTO>> batchSave(@Valid @RequestBody List<DataPrivilegeSaveDTO> dtos) {
        dataPrivilegeService.batchSave(dtos);
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        dataPrivilegeService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @Authorization
    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody DataPrivilegeQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        dataPrivilegeService.export(query, response);
    }

    @Authorization
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<String>> importData(@RequestPart("file") MultipartFile multipartFile,
                                                      @RequestPart("excelPara") ExcelPara excelPara) {
        return Results.createSuccessRes(dataPrivilegeService.importData(multipartFile, excelPara));
    }

    @Authorization
    @PostMapping(value = "/getPrivilegeCodes/{privilegeType}")
    @ApiOperation(value = "查询数据权限code")
    public ResponseEntity<Results<List<String>>> getPrivilegeCodes(@PathVariable(value = "privilegeType") String privilegeType){
        return Results.createSuccessRes(dataPrivilegeService.getPrivilegeCodes(privilegeType));
    }
}
