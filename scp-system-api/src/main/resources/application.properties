#spring.profiles.active=local
spring.datasource.druid.url=******************************************,10.40.38.125:3311,10.40.38.125:3311/scp_system?useUnicode=true&autoReconnect=true&characterEncoding=utf-8&useSSL=false&useOldAliasMetadataBehavior=true&serverTimezone=GMT%2B8
spring.datasource.username=scp_system
spring.datasource.password=ScpSystem&123
app.api.root-path=http://*************:8765/scp-system-api
permission.api.root-path=http://*************:8765/scp-system-api
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
# druid ???????????????????????
# druid ??????????????
spring.datasource.druid.initial-size=8
spring.datasource.druid.min-idle=8
spring.datasource.druid.max-active=30
# druid ?????????????
spring.datasource.druid.max-wait=30000
# druid ???????????????????????????????
spring.datasource.druid.time-between-eviction-runs-millis=60000
# ??????????????????????
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.redis.password=TH_Cache_3689
spring.redis.host=************
spring.redis.port=6379
numShard=1
proxySwitch=true
proxyHost=*************
proxyPort=3128
proxyUsername=squid
proxyPassword=FJ_SQUID_3594
logging.collector.address=***********:8088
trinasolar.env=dev
EUREKA_SERVER=************************************************/eureka/,http://admin:1Ydz2ABk5GRzhSnn@*************:8761/eureka/,************************************************/eureka/
scan.base.package=com.trinasolar.scp.system.api.controller
security.oauth2.client.client-id=scp-system-api
security.oauth2.client.client-secret=6260f600074683000610ea47
bom.service.url=http://*************:8765/scp-bom-api
spring.datasource.oracle.jdbc-url=*************************************
spring.datasource.oracle.username=apps
spring.datasource.oracle.password=apps123
spring.datasource.oracle.driver-class-name=oracle.jdbc.OracleDriver
erp.interface.platform.url=https://tslapigw-uat.trinasolar.com/63002172210b96af78f374b9/erp-catalog
erp.interface.ca004.clientid=47b600e4c3e4f6c6001656e330880af0
erp.interface.ca004.secret=1145b1532bf05074813fc0c0de93f624
erp.interface.la002.clientid=c25cfe3cb6e98e4b779a2b0049bac7f4
erp.interface.la002.secret=630d05da42d97a0aae06fea3f1a45cd2
erp.interface.ca001.clientid=be614c0539e0c561448a46228a3bf93e
erp.interface.ca001.secret=db5fbe0f63a8cc3b4610900cbb0c61c6
erp.interface.la001.clientid=9588e037a064b07e248c96074558b5f8
erp.interface.la001.secret=3a2d3cf2d8dbc644f0aeb923cf7ed893
erp.interface.vendor.url=https://tslapigw-uat.trinasolar.com/63002172210b96af78f374b9/erp-catalog
erp.interface.vendor.clientid=c25cfe3cb6e98e4b779a2b0049bac7f4
erp.interface.vendor.secret=630d05da42d97a0aae06fea3f1a45cd2
## éä»¶æå¡å¨ç¸å³éç½®
trinasolar.uploader.fileURL=https://fileul.trinasolar.com
trinasolar.uploader.clientId=scp-system-api
trinasolar.uploader.clientSecret=6260f600074683000610ea47
spring.messages.basename=i18n/messages,com.trinasolar.scp.common.scp-common-api.common
rocketmq.consumer.group=springboot_consumer_group
rocketmq.consumer.pull-batch-size=10
rocketmq.name-server=10.40.38.122:9876
rocketmq.producer.group=springboot_producer_group
rocketmq.producer.sendMessageTimeout=10000
rocketmq.producer.retryTimesWhenSendFailed=2
rocketmq.producer.retryTimesWhenSendAsyncFailed=2
rocketmq.producer.maxMessageSize=4096
rocketmq.producer.compressMessageBodyThreshold=4096
rocketmq.producer.retryNextServer=false