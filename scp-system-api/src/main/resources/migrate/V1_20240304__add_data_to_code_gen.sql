SET
FOREIGN_KEY_CHECKS = 0;

INSERT INTO `scp_system`.`sys_code_seq` (`id`, `seq_code`, `seq_name`, `seq_desc`, `is_available`, `reset_frequency`,
                                         `reset_start_date`, `reset_end_date`, `seq_value`, `seq_full_Value`,
                                         `reset_date`, `tenant_id`, `opt_counter`, `is_deleted`, `created_by`,
                                         `created_time`, `updated_by`, `updated_time`)
VALUES (507, 'bdm_exterior_demand_code', '电池外部需求规则', '电池外部需求规则', 'y', NULL, NULL, NULL, NULL, NULL,
        NULL, 'TRINA', 1, 0, '-1', '2023-09-13 19:01:38', '-1', '2023-09-13 19:01:38');

INSERT INTO `scp_system`.`sys_code_seq` (`id`, `seq_code`, `seq_name`, `seq_desc`, `is_available`, `reset_frequency`,
                                         `reset_start_date`, `reset_end_date`, `seq_value`, `seq_full_Value`,
                                         `reset_date`, `tenant_id`, `opt_counter`, `is_deleted`, `created_by`,
                                         `created_time`, `updated_by`, `updated_time`)
VALUES (508, 'bdm_sop_version', 'sop版本号生成', 'sop版本号生成', 'Y', NULL, NULL, NULL, NULL, NULL, NULL, 'TRINA', 1,
        0, NULL, '2024-01-08 10:54:57', NULL, '2024-01-08 10:54:57');

INSERT INTO `scp_system`.`sys_code_seq` (`id`, `seq_code`, `seq_name`, `seq_desc`, `is_available`, `reset_frequency`,
                                         `reset_start_date`, `reset_end_date`, `seq_value`, `seq_full_Value`,
                                         `reset_date`, `tenant_id`, `opt_counter`, `is_deleted`, `created_by`,
                                         `created_time`, `updated_by`, `updated_time`)
VALUES (509, 'LONG_CYCLE_MATERIALS_SERIAL', '长周期物料需求计算流水号', '长周期物料需求计算流水号', 'y', NULL, NULL,
        NULL, NULL, NULL, NULL, 'TRINA', 1, 0, NULL, '2023-09-13 19:01:38', NULL, '2023-09-13 19:01:38');

INSERT INTO `scp_system`.`sys_code_seq` (`id`, `seq_code`, `seq_name`, `seq_desc`, `is_available`, `reset_frequency`,
                                         `reset_start_date`, `reset_end_date`, `seq_value`, `seq_full_Value`,
                                         `reset_date`, `tenant_id`, `opt_counter`, `is_deleted`, `created_by`,
                                         `created_time`, `updated_by`, `updated_time`)
VALUES (510, 'material_purchase_plan_version', '物料采购计划计算版本', '物料采购计划计算版本', 'y', NULL, NULL, NULL,
        NULL, NULL, NULL, 'TRINA', 1, 0, NULL, '2024-01-10 14:28:02', NULL, '2024-01-10 14:28:02');

INSERT INTO `scp_system`.`sys_code_seq` (`id`, `seq_code`, `seq_name`, `seq_desc`, `is_available`, `reset_frequency`,
                                         `reset_start_date`, `reset_end_date`, `seq_value`, `seq_full_Value`,
                                         `reset_date`, `tenant_id`, `opt_counter`, `is_deleted`, `created_by`,
                                         `created_time`, `updated_by`, `updated_time`)
VALUES (511, 'APS_DELIVERY_PLAN_VERSION', 'APS到货计划版本号', 'APS到货计划版本号', 'y', NULL, NULL, NULL, NULL, NULL,
        NULL, 'TRINA', 1, 0, NULL, '2024-01-10 14:28:02', NULL, '2024-01-10 14:28:02');

INSERT INTO `scp_system`.`sys_code_seq` (`id`, `seq_code`, `seq_name`, `seq_desc`, `is_available`, `reset_frequency`,
                                         `reset_start_date`, `reset_end_date`, `seq_value`, `seq_full_Value`,
                                         `reset_date`, `tenant_id`, `opt_counter`, `is_deleted`, `created_by`,
                                         `created_time`, `updated_by`, `updated_time`)
VALUES (512, 'material_delivery_plan_version', '物料到货计划计算版本', '物料到货计划计算版本', 'y', NULL, NULL, NULL,
        NULL, NULL, NULL, 'TRINA', 1, 0, NULL, '2024-01-15 16:06:34', NULL, '2024-01-15 16:06:34');

INSERT INTO `scp_system`.`sys_code_seq` (`id`, `seq_code`, `seq_name`, `seq_desc`, `is_available`, `reset_frequency`,
                                         `reset_start_date`, `reset_end_date`, `seq_value`, `seq_full_Value`,
                                         `reset_date`, `tenant_id`, `opt_counter`, `is_deleted`, `created_by`,
                                         `created_time`, `updated_by`, `updated_time`)
VALUES (513, 'BBOM_SCREEN_LIFE', 'bbom网版寿命', 'bbom网版寿命', 'y', NULL, NULL, NULL, NULL, NULL, NULL, 'TRINA', 1, 0,
        NULL, '2024-01-15 16:06:34', NULL, '2024-01-15 16:06:34');

INSERT INTO `scp_system`.`sys_code_seq` (`id`, `seq_code`, `seq_name`, `seq_desc`, `is_available`, `reset_frequency`,
                                         `reset_start_date`, `reset_end_date`, `seq_value`, `seq_full_Value`,
                                         `reset_date`, `tenant_id`, `opt_counter`, `is_deleted`, `created_by`,
                                         `created_time`, `updated_by`, `updated_time`)
VALUES (514, 'silicon_slice_supply_version', '硅片供应能力版本', '硅片供应能力版本', 'y', NULL, NULL, NULL, NULL, NULL,
        NULL, 'TRINA', 1, 0, NULL, '2024-01-10 14:28:02', NULL, '2024-01-10 14:28:02');

UPDATE `scp_system`.`sys_code_seq`
SET `seq_code`         = 'aps_rich_version_code',
    `seq_name`         = 'APS富裕产能版本号',
    `seq_desc`         = 'APS富裕产能版本号',
    `is_available`     = 'y',
    `reset_frequency`  = NULL,
    `reset_start_date` = NULL,
    `reset_end_date`   = NULL,
    `seq_value`        = NULL,
    `seq_full_Value`   = NULL,
    `reset_date`       = NULL,
    `tenant_id`        = 'TRINA',
    `opt_counter`      = 1,
    `is_deleted`       = 0,
    `created_by`       = NULL,
    `created_time`     = '2023-05-08 18:47:05',
    `updated_by`       = NULL,
    `updated_time`     = '2023-05-08 18:47:05'
WHERE `id` = 500;

UPDATE `scp_system`.`sys_code_seq`
SET `seq_code`         = 'aps_rule_line',
    `seq_name`         = '排产规则行编码',
    `seq_desc`         = '排产规则行编码',
    `is_available`     = 'y',
    `reset_frequency`  = NULL,
    `reset_start_date` = NULL,
    `reset_end_date`   = NULL,
    `seq_value`        = NULL,
    `seq_full_Value`   = NULL,
    `reset_date`       = NULL,
    `tenant_id`        = 'TRINA',
    `opt_counter`      = 1,
    `is_deleted`       = 0,
    `created_by`       = NULL,
    `created_time`     = '2023-08-10 09:51:19',
    `updated_by`       = NULL,
    `updated_time`     = '2023-08-10 09:51:19'
WHERE `id` = 501;

UPDATE `scp_system`.`sys_code_seq`
SET `seq_code`         = 'aps_rule_header',
    `seq_name`         = '排产规则头编码',
    `seq_desc`         = '排产规则头编码',
    `is_available`     = 'y',
    `reset_frequency`  = NULL,
    `reset_start_date` = NULL,
    `reset_end_date`   = NULL,
    `seq_value`        = NULL,
    `seq_full_Value`   = NULL,
    `reset_date`       = NULL,
    `tenant_id`        = 'TRINA',
    `opt_counter`      = 1,
    `is_deleted`       = 0,
    `created_by`       = NULL,
    `created_time`     = '2023-08-10 09:51:48',
    `updated_by`       = NULL,
    `updated_time`     = '2023-08-10 09:51:48'
WHERE `id` = 502;

UPDATE `scp_system`.`sys_code_seq`
SET `seq_code`         = 'dp_report_version_code',
    `seq_name`         = '报表版本号',
    `seq_desc`         = '报表版本号',
    `is_available`     = 'y',
    `reset_frequency`  = NULL,
    `reset_start_date` = NULL,
    `reset_end_date`   = NULL,
    `seq_value`        = NULL,
    `seq_full_Value`   = NULL,
    `reset_date`       = NULL,
    `tenant_id`        = 'TRINA',
    `opt_counter`      = 1,
    `is_deleted`       = 0,
    `created_by`       = NULL,
    `created_time`     = '2023-09-09 16:42:08',
    `updated_by`       = NULL,
    `updated_time`     = '2023-09-09 16:42:08'
WHERE `id` = 503;

UPDATE `scp_system`.`sys_code_seq`
SET `seq_code`         = 'aps_history_collect_code',
    `seq_name`         = '历史材料搭配规则',
    `seq_desc`         = '历史材料搭配规则',
    `is_available`     = 'y',
    `reset_frequency`  = NULL,
    `reset_start_date` = NULL,
    `reset_end_date`   = NULL,
    `seq_value`        = NULL,
    `seq_full_Value`   = NULL,
    `reset_date`       = NULL,
    `tenant_id`        = 'TRINA',
    `opt_counter`      = 1,
    `is_deleted`       = 0,
    `created_by`       = NULL,
    `created_time`     = '2023-09-13 19:01:38',
    `updated_by`       = NULL,
    `updated_time`     = '2023-09-13 19:01:38'
WHERE `id` = 506;

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (530, 510, 1, 'fixedChar', '%s_P%s_', NULL, NULL, 1, 1, 'TRINA', 1, 0, NULL, '2024-01-10 14:29:39', NULL,
        '2024-01-10 14:29:39');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (532, 510, 2, 'seq', NULL, NULL, 1, NULL, 1, 'TRINA', 1, 0, NULL, '2024-01-10 14:29:39', NULL,
        '2024-01-10 14:29:39');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (533, 512, 1, 'fixedChar', '%s_D%s_', NULL, NULL, 1, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (535, 512, 2, 'seq', NULL, NULL, 1, 1, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (30351, 507, 1, 'fixedChar', 'C', NULL, NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (30352, 507, 2, 'paramCode', NULL, NULL, NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (30353, 507, 3, 'dateFormat', NULL, 'yyMM', NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (30354, 507, 4, 'seq', NULL, NULL, 3, 1, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40261, 508, 1, 'fixedChar', 'SOP', NULL, NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40263, 508, 2, 'dateFormat', NULL, 'yyMM', NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40264, 508, 3, 'seq', NULL, NULL, 3, 1, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40361, 509, 1, 'dateFormat', NULL, 'yyyyMM', NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40362, 509, 2, 'seq', NULL, NULL, 4, 1, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40371, 511, 1, 'paramCode', NULL, NULL, NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40372, 511, 2, 'fixedChar', '_', NULL, NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40373, 511, 3, 'paramCode', NULL, NULL, NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40374, 511, 4, 'fixedChar', '_', NULL, NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40375, 511, 5, 'paramCode', NULL, NULL, NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40376, 511, 6, 'fixedChar', '_', NULL, NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40377, 511, 7, 'dateFormat', NULL, 'yyyyMMdd', NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40378, 511, 8, 'fixedChar', '_', NULL, NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40379, 511, 9, 'seq', NULL, NULL, 2, 1, 1, 'TRINA', 1, 0, '-1', '2022-06-15 14:06:25', '-1',
        '2022-06-15 14:06:25');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40381, 513, 1, 'fixedChar', 'SC', '', NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-09 15:56:47', '-1',
        '2022-06-09 15:56:47');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40382, 513, 2, 'dateFormat', '', 'yyyyMMdd', NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-09 15:57:39', '-1',
        '2022-06-09 15:57:39');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40383, 513, 3, 'seq', NULL, '', 3, 1, 1, 'TRINA', 1, 0, '-1', '2022-06-09 15:58:19', '-1',
        '2022-06-09 15:58:19');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40384, 514, 1, 'fixedChar', 'V', NULL, NULL, NULL, 1, 'TRINA', 1, 0, NULL, '2024-01-10 14:29:39', NULL,
        '2024-01-10 14:29:39');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40385, 514, 2, 'dateFormat', NULL, 'yyyyMM', NULL, NULL, 1, 'TRINA', 1, 0, NULL, '2024-01-10 14:29:39', NULL,
        '2024-01-10 14:29:39');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40386, 514, 3, 'seq', NULL, NULL, 4, NULL, 1, 'TRINA', 1, 0, NULL, '2024-01-10 14:29:39', NULL,
        '2024-01-10 14:29:39');

SET
FOREIGN_KEY_CHECKS = 1;