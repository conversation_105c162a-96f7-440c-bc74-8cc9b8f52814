DROP TABLE IF EXISTS sys_erp_approval_supplier_fix_lot_qty;
CREATE TABLE sys_erp_approval_supplier_fix_lot_qty
(
    id                BIGINT      NOT NULL COMMENT 'ID主键',
    item_id           BIGINT COMMENT '料号ID',
    item_num          VARCHAR(255) COMMENT '料号',
    item_description  VARCHAR(500) COMMENT '料号描述',
    category_segment5 VARCHAR(64) COMMENT '辅料类型',
    pri_uom           VARCHAR(255) COMMENT '物料单位',
    vendor_id         BIGINT COMMENT '供应商ID',
    vendor_name       VARCHAR(255) COMMENT '供应商',
    is_oversea        VARCHAR(32) COMMENT '国内海外',
    base_place        VARCHAR(32) COMMENT '生产基地',
    fix_lot_qty       DECIMAL(10, 2) COMMENT '整车数或整柜数',
    tenant_id         VARCHAR(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
    opt_counter       INT         NOT NULL DEFAULT 1 COMMENT '乐观锁',
    is_deleted        INT         NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
    created_by        VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
    created_time      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by        VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
    updated_time      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '合格供应商整车数或整柜数表';
