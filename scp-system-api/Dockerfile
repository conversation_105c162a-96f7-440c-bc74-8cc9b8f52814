FROM harbor-test.trinasolar.com/library/skywalking-java-8u111-apline
ARG JAVA_RUN_ENV=dev
ARG LOG_INDEX_NAME=log_test
ARG APP_NAME=app_name
ARG APOLLO_CONFIG_META=http://devopsconfigdev.trinasolar.com
ADD ./target/app.jar /usr/local/
ADD ./start.sh /usr/local/
RUN chmod +x /usr/local/start.sh
ENV ARGS -Xmx4g -Xms4g -Duser.timezone=Asia/shanghai -Dsport=80 -Dapollo.meta=$APOLLO_CONFIG_META -Djava.security.egd=file:/dev/./urandom -Dspring.profiles.active=$JAVA_RUN_ENV -Dskywalking.agent.application_code=$APP_NAME
ENV LOG_INDEX_NAME=$LOG_INDEX_NAME
ENTRYPOINT ["/sbin/tini","--","sh","/usr/local/start.sh"]