package com.trinasolar.scp.system.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 前端多语言配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "Prompt保存参数", description = "保存参数")
public class PromptSaveDTO extends TokenDTO implements Serializable {
    private static final long serialVersionUID = 2252635080315730248L;

    /**
     * prompt_id
     */
    @ApiModelProperty(value = "prompt_id")
    private Long promptId;

    /**
     * 多语言key
     */
    @ApiModelProperty(value = "多语言key")
    private String key;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;

    /**
     * 语言
     */
    @ApiModelProperty(value = "语言")
    private String lang;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "平台")
    private String platform;
}
