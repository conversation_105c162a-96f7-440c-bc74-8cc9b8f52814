package com.trinasolar.scp.system.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.system.domain.entity.ApprovalSupplier;
import com.trinasolar.scp.system.domain.dto.ApprovalSupplierDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 批准供应商 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-26 20:32:43
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ApprovalSupplierDEConvert extends BaseDEConvert<ApprovalSupplierDTO, ApprovalSupplier> {

    ApprovalSupplierDEConvert INSTANCE = Mappers.getMapper(ApprovalSupplierDEConvert.class);

}
