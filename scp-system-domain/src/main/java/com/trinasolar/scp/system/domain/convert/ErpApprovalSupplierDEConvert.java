package com.trinasolar.scp.system.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.system.domain.entity.ErpApprovalSupplier;
import com.trinasolar.scp.system.domain.dto.ErpApprovalSupplierDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 批准供应商 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:05:46
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ErpApprovalSupplierDEConvert extends BaseDEConvert<ErpApprovalSupplierDTO, ErpApprovalSupplier> {

    ErpApprovalSupplierDEConvert INSTANCE = Mappers.getMapper(ErpApprovalSupplierDEConvert.class);

}
