package com.trinasolar.scp.system.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * erp供应商表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-22 09:10:40
 */
@Data
@ApiModel(value = "ErpSupplier查询条件", description = "查询条件")
@Accessors(chain = true)
public class ErpSupplierQuery extends PageDTO implements Serializable {
    @ApiModelProperty(value = "供应商ID集合")
    private List<Long> supplierIds;

    @ApiModelProperty(value = "供应商名称集合")
    private List<String> names;


    /**
     * 导出列映射顺序对象
     */
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;

    @ApiModelProperty(value = "attribute3集合")
    private List<String> attribute3List;

    @ApiModelProperty(value = "供应商ID集合")
    private List<Long> vendorIdList;

}
