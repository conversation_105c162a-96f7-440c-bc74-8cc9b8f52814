package com.trinasolar.scp.system.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.system.domain.entity.File;
import com.trinasolar.scp.system.domain.dto.FileDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 附件表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-08 18:35:02
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface FileDEConvert extends BaseDEConvert<FileDTO, File> {

    FileDEConvert INSTANCE = Mappers.getMapper(FileDEConvert.class);

}
