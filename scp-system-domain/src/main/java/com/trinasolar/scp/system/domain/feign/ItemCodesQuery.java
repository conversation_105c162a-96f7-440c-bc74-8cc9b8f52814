package com.trinasolar.scp.system.domain.feign;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 物料基础数据表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-30 14:19:23
 */
@Data
@ApiModel(value = "Items查询条件", description = "查询条件")
@Accessors(chain = true)
public class ItemCodesQuery extends PageDTO implements Serializable {

    private List<String> itemCodes;

}
