package com.trinasolar.scp.system.domain.query;

import lombok.Data;
import lombok.experimental.Accessors;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;

/**
 * 附件表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-08 18:35:02
 */
@Data
@ApiModel(value = "File查询条件", description = "查询条件")
@Accessors(chain = true)
public class FileQuery extends PageDTO implements Serializable {
    /**
     * 外部系统主键
     */
    @ApiModelProperty(value = "外部系统主键")
    private String fk;
    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String name;
    /**
     * 文件主键名
     */
    @ApiModelProperty(value = "文件主键名")
    private String fileKey;
    /**
     * 文件URL
     */
    @ApiModelProperty(value = "文件URL")
    private String fileUrl;
    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String fileType;
    /**
     * 业务主键
     */
    @ApiModelProperty(value = "业务主键")
    private String bizKey;
    /**
     * 业务类型;一般是业务表名
     */
    @ApiModelProperty(value = "业务类型;一般是业务表名")
    private String bizType;
    /**
     * 导出列映射顺序对象
     */
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
