package com.trinasolar.scp.system.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/4/25
 */
@Data
@ApiModel(value = "AttrTypeLineQuery", description = "查询条件")
public class AttrTypeLineQuery extends PageDTO {
    /**
     * 属性类型编码
     */
    @ApiModelProperty(value = "Header Code,头的code")
    private String code;

    @ApiModelProperty(value = "AttrTypeHeaderId")
    private Long attrTypeHeaderId;

    @ApiModelProperty(value = "属性行id")
    private Long attrLineId;

    @ApiModelProperty(value = "属性中文名")
    private String attrCnName;

    @ApiModelProperty(value = "有效标识: Y 有效  N 无效")
    private String enableFlag;

}
