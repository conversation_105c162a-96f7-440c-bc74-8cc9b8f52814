package com.trinasolar.scp.system.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26
 */
@Data
@ApiModel(value = "PromptsSaveDTO(save)")
public class PromptsSaveDTO extends TokenDTO implements Serializable {
    private static final long serialVersionUID = 312323368131252513L;

    @ApiModelProperty(value = "Prompt数组保存")
    List<PromptSaveDTO> promptSaveDTOS;
}
