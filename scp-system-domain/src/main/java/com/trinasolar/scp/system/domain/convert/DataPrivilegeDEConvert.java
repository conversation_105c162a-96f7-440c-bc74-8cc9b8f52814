package com.trinasolar.scp.system.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.system.domain.dto.DataPrivilegeDTO;
import com.trinasolar.scp.system.domain.entity.DataPrivilege;
import com.trinasolar.scp.system.domain.save.DataPrivilegeSaveDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 数据权限表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-17 15:39:12
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface DataPrivilegeDEConvert extends BaseDEConvert<DataPrivilegeDTO, DataPrivilege> {

    DataPrivilegeDEConvert INSTANCE = Mappers.getMapper(DataPrivilegeDEConvert.class);

    DataPrivilege toDataPrivilege(DataPrivilegeSaveDTO dataPrivilegeSaveDTO);

    List<DataPrivilege> toDataPrivilege(List<DataPrivilegeSaveDTO> dataPrivilegeSaveDTOS);
}
