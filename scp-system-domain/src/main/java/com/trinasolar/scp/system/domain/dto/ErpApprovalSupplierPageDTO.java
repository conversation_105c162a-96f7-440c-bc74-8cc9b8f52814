package com.trinasolar.scp.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName ErpApprovalSupplierPageDTO
 * @Description
 * @Date 2023/12/29 13:20
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ApprovalSupplierDTO对象", description = "DTO对象")
public class ErpApprovalSupplierPageDTO {

    /**
     * 料号ID
     */
    @ApiModelProperty(value = "料号ID")
    private Long itemId;

    /**
     * 料号ID
     */
    @ApiModelProperty(value = "料号Num")
    private String itemNum;

    /**
     * 料号描述
     */
    @ApiModelProperty(value = "料号描述")
    private String itemDescription;

    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    private Long vendorId;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String vendorName;

    /**
     * 供应商简称
     */
    @ApiModelProperty(value = "供应商简称")
    private String vendorNameAlt;

    /**
     * 批准状态
     */
    @ApiModelProperty(value = "批准状态")
    private String aslStatus;

}
