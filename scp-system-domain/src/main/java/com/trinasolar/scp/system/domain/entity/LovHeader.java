package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * lov头表(LovHeader)实体类
 *
 * <AUTHOR>
 * @since 2022-04-24 10:42:25
 */
@Entity
@Table(name = "sys_lov_header")
//@Table(name = "sys_lov_header_uat" )
@ToString
@Data
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "update sys_lov_header set is_deleted = 1 where lov_header_id = ?")
@SQLDeleteAll(sql = "update sys_lov_header set is_deleted = 1 where lov_header_id = ?")
public class LovHeader extends BasePO implements Serializable {
    private static final long serialVersionUID = -28637856826550590L;
    /**
     * lov头id
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig" )
    private Long lovHeaderId;
    /**
     * lov编码
     */
    private String lovCode;
    /**
     * lov名称
     */
    private String lovName;
    /**
     * lov描述
     */
    private String lovDesc;
    /**
     * lov分类id 1-材料 2-组件 3-产品族 4-其它 5-系统
     */
    private Integer lovCategoryId;
    /**
     * lov类型id，1-值列表 2-动态结果集
     */
    private Integer lovTypeId;
    /**
     * lov脚本 lov_type_id为2时需要维护
     */
    private String lovScript;
    /**
     * 有效标识
     */
    private String enableFlag;
    /**
     * 有效日期_起
     */
    private LocalDate effectiveStartDate;
    /**
     * 有效日期_止
     */
    private LocalDate effectiveEndDate;
    /**
     * 来源系统id
     */
    private Integer sourceSystemId;
    /**
     * 扩展字段1
     */
    private String attribute1;
    /**
     * 扩展字段2
     */
    private String attribute2;
    /**
     * 扩展字段3
     */
    private String attribute3;
    /**
     * 扩展字段4
     */
    private String attribute4;
    /**
     * 扩展字段5
     */
    private String attribute5;
    /**
     * 扩展字段6
     */
    private String attribute6;
    /**
     * 扩展字段7
     */
    private String attribute7;
    /**
     * 扩展字段8
     */
    private String attribute8;
    /**
     * 扩展字段9
     */
    private String attribute9;
    /**
     * 扩展字段10
     */
    private String attribute10;
    /**
     * 扩展字段11
     */
    private String attribute11;
    /**
     * 扩展字段12
     */
    private String attribute12;
    /**
     * 扩展字段13
     */
    private String attribute13;
    /**
     * 扩展字段14
     */
    private String attribute14;
    /**
     * 扩展字段15
     */
    private String attribute15;

}

