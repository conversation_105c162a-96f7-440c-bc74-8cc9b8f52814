package com.trinasolar.scp.system.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import java.time.LocalDateTime;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

import org.hibernate.annotations.GenericGenerator;

/**
 * erp人员信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 15:55:04
 */
@Entity
@ToString
@Data
@Table(name = "sys_erp_people")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE sys_erp_people SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE sys_erp_people SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ErpPeople extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "id")
    @Column(name = "id")
    private Long id;

    /**
     * 员工名
     */
    @ApiModelProperty(value = "员工名")
    @Column(name = "full_name")
    private String fullName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @Column(name = "employee_number")
    private String employeeNumber;

    /**
     * 有效日期从
     */
    @ApiModelProperty(value = "有效日期从")
    @Column(name = "effective_dates_from")
    private LocalDateTime effectiveDatesFrom;

    /**
     * 有效日期至
     */
    @ApiModelProperty(value = "有效日期至")
    @Column(name = "effective_dates_to")
    private LocalDateTime effectiveDatesTo;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @Column(name = "email")
    private String email;

    /**
     * 传真
     */
    @ApiModelProperty(value = "传真")
    @Column(name = "faxes")
    private String faxes;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @Column(name = "tcz_dept")
    private String tczDept;

    /**
     * cc
     */
    @ApiModelProperty(value = "cc")
    @Column(name = "tcz_cc")
    private String tczCc;

    /**
     * cc名称
     */
    @ApiModelProperty(value = "cc名称")
    @Column(name = "tcz_cc_name")
    private String tczCcName;

    /**
     * 银行
     */
    @ApiModelProperty(value = "银行")
    @Column(name = "tcz_bank_num")
    private String tczBankNum;

    /**
     * 帐号
     */
    @ApiModelProperty(value = "帐号")
    @Column(name = "tc_branch_num")
    private String tcBranchNum;

    /**
     * erp最后更新日期
     */
    @ApiModelProperty(value = "erp最后更新日期")
    @Column(name = "last_update_date")
    private LocalDateTime lastUpdateDate;


}
