package com.trinasolar.scp.system.domain.save;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class AttrTypeMapSaveDTO implements Serializable {
    /**
     * 属性类型id
     */
    @ApiModelProperty(value = "属性类型id")
    private Long attributeMapId;

    /**
     * 属性头ID
     */
    @ApiModelProperty(value = "属性头ID")
    private Long attributeHeaderId;

    /**
     * 来源系统id
     */
    @ApiModelProperty(value = "来源系统id")
    private Long sourceSystemId;

    /**
     * 来源表
     */
    @ApiModelProperty(value = "来源表")
    private String sourceTableName;

    /**
     * 分类字段1
     */
    @ApiModelProperty(value = "分类字段1")
    private String categorySegment1;

    /**
     * 分类字段2
     */
    @ApiModelProperty(value = "分类字段2")
    private String categorySegment2;

    /**
     * 分类字段3
     */
    @ApiModelProperty(value = "分类字段3")
    private String categorySegment3;

    /**
     * 分类字段4
     */
    @ApiModelProperty(value = "分类字段4")
    private String categorySegment4;

    private static final long serialVersionUID = 1L;
}

