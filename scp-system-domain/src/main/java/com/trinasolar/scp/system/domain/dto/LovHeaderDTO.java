package com.trinasolar.scp.system.domain.dto;


import com.google.common.collect.Lists;
import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.system.domain.entity.LovLine;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * lov头表(LovHeader)实体类
 *
 * <AUTHOR>
 * @since 2022-04-24 10:42:25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "LovHeaderDTO", description = "LovHeaderDTO" )
public class LovHeaderDTO extends PageDTO implements Serializable {
    private static final long serialVersionUID = -28637856826550591L;
    /**
     * lov头id
     */
    @ApiModelProperty(value = "lov头id" )
    private Long lovHeaderId;
    /**
     * lov编码
     */
    @ApiModelProperty(value = "lov编码" )
    private String lovCode;
    /**
     * lov名称
     */
    @ApiModelProperty(value = "lov名称")
    private String lovName;
    /**
     * lov描述
     */
    @ApiModelProperty(value = "lov描述")
    private String lovDesc;
    /**
     * lov分类id 1-材料 2-组件 3-产品族 4-其它 5-系统
     */
    @ApiModelProperty(value = "lov分类id 1-材料 2-组件 3-产品族 4-其它 5-系统")
    private Integer lovCategoryId;
    /**
     * lov类型id，1-值列表 2-动态结果集
     */
    @ApiModelProperty(value = "lov类型id，1-值列表 2-动态结果集")
    private Integer lovTypeId;
    /**
     * lov脚本 lov_type_id为2时需要维护
     */
    @ApiModelProperty(value = "lov脚本 lov_type_id为2时需要维护")
    private String lovScript;
    /**
     * 有效标识
     */
    @NotNull(message = "有效标识 不能为空")
    @ApiModelProperty(value = "有效标识")
    private String enableFlag;
    /**
     * 有效日期_起
     */
    @NotNull(message = "有效日期_起 不能为空")
    @ApiModelProperty(value = "有效日期_起")
    private LocalDate effectiveStartDate;
    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDate effectiveEndDate;
    /**
     * 来源系统id
     */
    @ApiModelProperty(value = "来源系统id")
    private Integer sourceSystemId;
    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1")
    private String attribute1;
    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2")
    private String attribute2;
    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3")
    private String attribute3;
    /**
     * 扩展字段4
     */
    @ApiModelProperty(value = "扩展字段4")
    private String attribute4;
    /**
     * 扩展字段5
     */
    @ApiModelProperty(value = "扩展字段5")
    private String attribute5;
    /**
     * 扩展字段6
     */
    @ApiModelProperty(value = "扩展字段6")
    private String attribute6;
    /**
     * 扩展字段7
     */
    @ApiModelProperty(value = "扩展字段7")
    private String attribute7;
    /**
     * 扩展字段8
     */
    @ApiModelProperty(value = "扩展字段8")
    private String attribute8;
    /**
     * 扩展字段9
     */
    @ApiModelProperty(value = "扩展字段9")
    private String attribute9;
    /**
     * 扩展字段10
     */
    @ApiModelProperty(value = "扩展字段10")
    private String attribute10;
    /**
     * 扩展字段11
     */
    @ApiModelProperty(value = "扩展字段11")
    private String attribute11;
    /**
     * 扩展字段12
     */
    @ApiModelProperty(value = "扩展字段12")
    private String attribute12;
    /**
     * 扩展字段13
     */
    @ApiModelProperty(value = "扩展字段13")
    private String attribute13;
    /**
     * 扩展字段14
     */
    @ApiModelProperty(value = "扩展字段14")
    private String attribute14;
    /**
     * 扩展字段15
     */
    @ApiModelProperty(value = "扩展字段15")
    private String attribute15;

    @ApiModelProperty(value = "行的DTO列表")
    private List<LovLineDTO> lineDTOS;

    @ApiModelProperty(value = "配置的DTO列表")
    private List<AttrConfigDTO> lovConfigDTOS;


    public List<List<Object>> getDataList(List<LovLine> lovLines) {
        List<List<Object>> dataList = Lists.newArrayList();
        for (LovLine line : lovLines) {
            List<Object> list = Lists.newArrayList();
            list.add(line.getLovName());
            list.add(line.getAttribute1());
//            list.add(line.getAttribute2());
//            list.add(line.getAttribute3());
//            list.add(line.getAttribute4());
//            list.add(line.getAttribute5());
//            list.add(line.getAttribute6());
//            list.add(line.getAttribute7());
//            list.add(line.getAttribute8());
//            list.add(line.getAttribute9());
//            list.add(line.getAttribute10());
//            list.add(line.getAttribute11());
//            list.add(line.getAttribute12());
//            list.add(line.getAttribute13());
//            list.add(line.getAttribute14());
//            list.add(line.getAttribute15());
            dataList.add(list);
        }
        return dataList;
    }
}

