package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;


/**
 * 系统LOV多语言表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
@Entity
@ToString
@Data
@Table(name = "sys_lov_tl" )
@Where(clause = " is_deleted=0 " )
@SQLDelete(sql = "UPDATE sys_lov_tl SET is_deleted = 1 WHERE id = ?" )
@SQLDeleteAll(sql = "UPDATE sys_lov_tl SET is_deleted = 1 WHERE id = ?" )
public class LovTl extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig" )
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 目标主键ID
     */
    @ApiModelProperty(value = "目标主键ID")
    private Long pkId;

    /**
     * 目标类型: header, config, line
     */
    @ApiModelProperty(value = "目标类型: header, config, line")
    private String pkType;

    /**
     * 语言
     */
    @ApiModelProperty(value = "语言")
    private String lang;

    @ApiModelProperty(value = "字段")
    private String lovField;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;


}
