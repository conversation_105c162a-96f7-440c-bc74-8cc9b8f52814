package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * lov行表(SysLovLines)实体类
 *
 * <AUTHOR>
 * @since 2022-04-24 14:15:04
 */
@Entity
@Table(name = "sys_lov_lines")
//@Table(name = "sys_lov_lines_uat" )
@ToString
@Data
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "update sys_lov_lines set is_deleted = 1 where lov_line_id = ?")
@SQLDeleteAll(sql = "update sys_lov_lines set is_deleted = 1 where lov_line_id = ?")
public class LovLine extends BasePO implements Serializable {
    private static final long serialVersionUID = 388343368137252563L;
    /**
     * lov头id
     */
    @ApiModelProperty(value = "lov头id" )
    @Basic
    private Long lovHeaderId;

    /**
     * lov行id
     */
    @ApiModelProperty(value = "lov行id")
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig" )
    private Long lovLineId;

    /**
     * lov名称
     */
    @ApiModelProperty(value = "lov名称")
    @Basic
    private String lovName;

    /**
     * lov值
     */
    @ApiModelProperty(value = "lov值")
    @Basic
    private String lovValue;

    /**
     * 父lov行id
     */
    @ApiModelProperty(value = "父lov行id")
    @Basic
    private Long parentLovLineId;

    @ApiModelProperty(value = "列序号")
    @Basic
    private Integer colNo;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
    @Basic
    private String enableFlag;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    @Basic
    private LocalDate effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    @Basic
    private LocalDate effectiveEndDate;

    /**
     * 来源系统id
     */
    @ApiModelProperty(value = "来源系统id")
    @Basic
    private Integer sourceSystemId;

    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1")
    @Basic
    private String attribute1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2")
    @Basic
    private String attribute2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3")
    @Basic
    private String attribute3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty(value = "扩展字段4")
    @Basic
    private String attribute4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty(value = "扩展字段5")
    @Basic
    private String attribute5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty(value = "扩展字段6")
    @Basic
    private String attribute6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty(value = "扩展字段7")
    @Basic
    private String attribute7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty(value = "扩展字段8")
    @Basic
    private String attribute8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty(value = "扩展字段9")
    @Basic
    private String attribute9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty(value = "扩展字段10")
    @Basic
    private String attribute10;

    /**
     * 扩展字段11
     */
    @ApiModelProperty(value = "扩展字段11")
    @Basic
    private String attribute11;

    /**
     * 扩展字段12
     */
    @ApiModelProperty(value = "扩展字段12")
    @Basic
    private String attribute12;

    /**
     * 扩展字段13
     */
    @ApiModelProperty(value = "扩展字段13")
    @Basic
    private String attribute13;

    /**
     * 扩展字段14
     */
    @ApiModelProperty(value = "扩展字段14")
    private String attribute14;

    /**
     * 扩展字段15
     */
    @ApiModelProperty(value = "扩展字段15")
    @Basic
    private String attribute15;
}
