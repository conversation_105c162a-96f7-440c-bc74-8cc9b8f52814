package com.trinasolar.scp.system.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

import org.hibernate.annotations.GenericGenerator;

/**
 * 汇率表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-29 09:01:50
 */
@Entity
@ToString
@Data
@Table(name = "sys_daily_rates")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE sys_daily_rates SET is_deleted = 1 WHERE from_currency = ?")
@SQLDeleteAll(sql = "UPDATE sys_daily_rates SET is_deleted = 1 WHERE from_currency = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class DailyRates extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID")
    @Column(name = "id")
    private Long id;

    /**
     * 来源币种
     */
    @ApiModelProperty(value = "来源币种")
    @Column(name = "from_currency")
    private String fromCurrency;

    /**
     * 转换币种
     */
    @ApiModelProperty(value = "转换币种")
    @Column(name = "to_currency")
    private String toCurrency;

    /**
     * 汇率日期
     */
    @ApiModelProperty(value = "汇率日期")
    @Column(name = "conversion_date")
    private LocalDateTime conversionDate;

    /**
     * 汇率类型
     */
    @ApiModelProperty(value = "汇率类型")
    @Column(name = "conversion_type")
    private String conversionType;

    /**
     * 汇率
     */
    @ApiModelProperty(value = "汇率")
    @Column(name = "conversion_rate")
    private String conversionRate;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @Column(name = "status_code")
    private String statusCode;

    /**
     * rateSourceCode
     */
    @ApiModelProperty(value = "rateSourceCode")
    @Column(name = "rate_source_code")
    private String rateSourceCode;


}
