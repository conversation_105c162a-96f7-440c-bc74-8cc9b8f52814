package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 脚本维护
 *
 * <AUTHOR>
 * @date 2022-10-13
 */
@Entity
@ToString
@Data
@Table(name = "sys_script")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE sys_script SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE sys_script SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class SysScript extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    *主键
    */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID")
    private Long id ;
    /**
    *所属模块
    */
    @ApiModelProperty(value = "所属模块")
    @Column(name = "script_module")
    private String scriptModule ;
    /**
    *脚本编码
    */
    @ApiModelProperty(value = "脚本编码")
    @Column(name = "script_code")
    private String scriptCode ;
    /**
    *脚本名称
    */
    @ApiModelProperty(value = "脚本名称")
    @Column(name = "script_name")
    private String scriptName ;
    /**
    *脚本内容
    */
    @ApiModelProperty(value = "脚本内容")
    @Column(name = "script_content")
    private String scriptContent ;
    /**
    *备注
    */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark ;
}
