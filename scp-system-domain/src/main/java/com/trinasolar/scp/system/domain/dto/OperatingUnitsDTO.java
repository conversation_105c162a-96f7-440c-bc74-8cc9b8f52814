package com.trinasolar.scp.system.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.Builder;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 业务实体
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-07 17:43:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "OperatingUnitsDTO对象", description = "DTO对象")
public class OperatingUnitsDTO {

    /**
     * 业务实体ID
     */
    @ApiModelProperty(value = "业务实体ID")
    private Long orgId;
    /**
     * 业务实体编码
     */
    @ApiModelProperty(value = "业务实体编码")
    private String orgCode;
    /**
     * 业务实体名称
     */
    @ApiModelProperty(value = "业务实体名称")
    private String orgName;
    /**
     * 业务实体描述
     */
    @ApiModelProperty(value = "业务实体描述")
    private String description;
    /**
     * 业务实体简称
     */
    @ApiModelProperty(value = "业务实体简称")
    private String orgShortName;
    /**
     * 本位币币种
     */
    @ApiModelProperty(value = "本位币币种")
    private String currency;
    /**
     * SCP使用标识（Y）
     */
    @ApiModelProperty(value = "SCP使用标识（Y）")
    private String scpFlag;
}
