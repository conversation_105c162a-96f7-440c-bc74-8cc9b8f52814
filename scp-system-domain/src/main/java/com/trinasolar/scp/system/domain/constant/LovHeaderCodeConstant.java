package com.trinasolar.scp.system.domain.constant;

/**
 * lov header code常量
 *
 * <AUTHOR>
 * @date 2022年6月17日14:08:48
 */
public interface LovHeaderCodeConstant {
    /**
     * 属性值：固化，不能修改
     */
    String ATTRIBUTE_LOV_VALUE = "ATTRIBUTE";

    /**
     * 物料属性
     */
    String DP_ATTRIBUTE = "DP attribute code";

    /**
     * 瓶颈物料属性
     */
    String BOTTLENECK_ATTRIBUTE = "aps_accessory_bottleneck_base_dp_column_code";

    /**
     * 功率预测材料属性
     */
    String POWER_ITEM_ATTRIBUTE = "power_item_attribute";

    /**
     * 产品族
     */
    public static final String FAMILY_CODE = "Family_Code";

    /**
     * 产品结构
     */
    public static final String PRODUCT_STRUCTURE = "Product_Structure";

    /**
     * 限制厂牌
     */
    public static final String CONSTRAINT_LABEL = "constraint_label";

    /**
     * 材料规则特例维护-状态
     */
    public static final String POWER_FINANCE_STATUS = "power_finance_status";

    /**
     * 材料规则特例维护-厂商要求
     */
    public static final String BOM_VENDOR_OPERATOR = "bom_vendor_operator";

    /**
     * 是否
     */
    public static final String YES_OR_NO = "yes_or_no";

    /**
     * 是否
     */
    String IS_FLAG = "isflag";

    /**
     * 区域/基地
     */
    public static final String BASE_PLACE = "base_place";

    /**
     * 车间
     */
    public static final String WORK_SHOP = "work_shop";

    /**
     * 项目地
     */
    public static final String COUNTRY = "country";

    /**
     * 超耗物料计划类型
     */
    public static final String APS_PLAN_TYPE = "APS_PLAN_TYPE";

    /**
     * 国内/海外
     */
    public static final String IS_OVERSEA = "Domestic/Oversea";

    /**
     * 供应商
     */
    public static final String SUPPLIER = "SUPPLIER";

    /**
     * 到料方式
     */
    public static final String PICK_UP_WAY = "material_arrived_way";

    /**
     * 运输方式
     */
    public static final String SHIPPING_TYPE = "transport_way";

    /**
     * 海外到货计划执行信息-逾期标记
     */
    public static final String EXPIRE_FLAG = "material_warning";

    /**
     * 横竖装
     */
    public static final String CROSS_VERTICAL = "6A001066";

    /**
     * 单元
     */
    public static final String WORK_UNIT = "work_unit";

    /**
     * IE改造&异常产能表类型
     */
    public static final String APS_MODULE_UNUSUAL_TYPE = "aps_module_unusual_type";

    /**
     * 客户
     */
    public static final String CUSTOMER = "Customer";

    /**
     * PR对应预算号
     */
    public static final String PR_BOOK = "PR Book";

    /**
     * 线缆长度
     */
//    public static final String LENGTH_CABLE = "6A001044";
    public static final String LENGTH_CABLE = "6A00100300121";

    /**
     * 边框双玻_端子
     */
    public static final String PLUG_CONNECTOR = "6A00100300122";

    /**
     * 组件尺寸
     */
    public static final String COMPONENT_SIZE = "6A00100100128";

    /**
     * 瓶颈物料基础信息-DP中对应列名称
     */
    public static final String DP_COLUMN_NAME = "dp_line";

    /**
     * 瓶颈物料基础信息-辅材型号
     */
    public static final String BOTTLENECK_ACCESSORY_SPEC = "material_type";

    /**
     * 瓶颈物料预测需求-区域
     */
    public static final String AREA = "Region";

    /**
     * 组件良率&符合率类型
     */
    public static final String APS_MODULE_FINE_TYPE = "aps_module_fine_type";

    /**
     * 超耗物料申请-BPM状态
     */
    public static final String BPM_STATUS = "approval_sign";

    /**
     * 采购计划-物控确认
     */
    public static final String PMC_SIGN = "PMC_sign";

    /**
     * 调拨清单-调拨原因
     */
    public static final String ALLOCATION_REASON = "allot_reason";

    /**
     * 指定消耗清单-原因类型
     */
    public static final String APPOINT_REASON = "appoint_reason";

    /**
     * 库存组织
     */
    public static final String INVENTORY_ORGANIZATION = "inventory_organization";

    /**
     * 账套类型
     */
    public static final String BOOKS_TYPE = "books_type";

    /**
     * 功率预测预警类型
     */
    public static final String APS_POWER_DETAIL_WARNING_TYPE = "aps_power_detail_warning_type";

    //gap类型
    public static final String APS_GAP_TYPE = "aps_gap_type";


//    /**
//     * 边框组件_EVA属性
//     */
//    String EVA = "6A00100100130";

    /**
     * 电池类型
     */
    String CELL_TYPE = "aps_power_cell_type";

    /**
     * 产品族-电池类型-对照
     */
    String FAMILY_CELL_TYPE = "aps_power_cell_prod_map";

    /**
     * 电池业务类型
     */
    String CELL_BUSINESS_TYPE = "aps_cell_other_plan_type";

    /**
     * 电池分配时供应类型
     */
    String CELL_SUPPLIER_TYPE = "cell_supplier_type";

    /**
     * 电池-供应方
     */
    String CELL_SUPPLIER = "aps_power_efficiency_supplier";

    /**
     * 电池-自产/外购
     */
    String PRODUCT_FROM = "product_from";

    /**
     * 电池-片源分类
     */
    String CELL_SOURCE = "aps_cell_relation_cell_source";

    /**
     * 电池系列
     */
    String POWER_CELL_SERIES = "aps_power_cell_series";

    /**
     * 功率影响属性
     */
    String POWER_CHANGE_RULE_ATTRIBUTE = "aps_power_change_rule_attribute";

    /**
     * 功率预测 类型转换
     */
    String POWER_CHANGE_RULE_TYPE = "aps_power_change_rule_type";

    /**
     * 场景
     */
    String POWER_CHANGE_RULE_BUSINESS_TYPE = "aps_power_change_rule_business_type";

    /**
     * 电池供应商
     */
    String POWER_EFFICIENCY_SUPPLIER = "aps_power_efficiency_supplier";

    /**
     * 功率预测供应类型
     */
    String SUPPLIER_TYPE = "supplier_type";

    /**
     * 电池分类规则类型
     */
    String POWER_CELL_RULE_TYPE = "aps_power_cell_rule_type";

    /**
     * 电池分类规则 DO字段
     */
    String POWER_CELL_RULE_FIELD = "aps_power_cell_rule_field";

    /**
     * 瓶颈物料基础信息-规则符
     */
    String RULE_OPERATOR = "aps_accessory_bottleneck_base_operator";

    /**
     * 财务最优 类型
     */
    String POWER_TYPE = "power_type";

    /**
     * 版本号
     */
    String DATA_VERSION = "aps_version";

    /**
     * 销售渠道
     */
    String CHANNEL = "Channel";

    /**
     * 电池每日结存类型
     */
    String CELL_LEFT_TYPE = "aps_cell_supply_demand_report_type";

    /**
     * AOP 产品大类
     */
    String AOP_PRODUCT_TYPE = "AOP_PRODUCT_TYPE";

    /**
     * 气候性条件
     */
    String CLIMATE_CONDITIONS = "6A00100100129";

    /**
     * 片源种类
     */
    String CONTENT_TYPES = "6A00100100124";

    /**
     * 片源匹配
     */
    String APS_POWER_CELL = "aps_power_cell";

    /**
     * 长期功率预测功率版本
     */
    String POWER_VERSION = "POWER VERSION";

    /**
     * 排产/入库需要发邮件的用户
     */
    String APS_SCHEDULE_USER_EMAIL = "APS_SCHEDULE_USER_EMAIL";

    String APS_CALENDER_DISCOUNT_EMAIL = "APS_CALENDER_DISCOUNT_EMAIL";

    String INVENTORY_LIMITS = "inventory Limits";

    String TYPE_OF_CERTIFICATION = "Type of certification";

    String LOV_CATEGORY_TYPE = "CategorySegment4";
}
