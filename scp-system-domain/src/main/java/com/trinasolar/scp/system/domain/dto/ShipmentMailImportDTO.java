package com.trinasolar.scp.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ApiLogDTO对象", description = "DTO对象")
public class ShipmentMailImportDTO {
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    private String area;

    /**
     * 账套
     */
    @ApiModelProperty(value = "账套")
    private String orgCode;

    /**
     * 物流工作组
     */
    @ApiModelProperty(value = "物流工作组")
    private String tmWorkGroup;

}
