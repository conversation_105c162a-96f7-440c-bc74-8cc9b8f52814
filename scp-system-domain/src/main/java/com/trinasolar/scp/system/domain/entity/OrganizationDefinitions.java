package com.trinasolar.scp.system.domain.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-28 11:38:58
 */
@Entity
@ToString
@Data
@Table(name = "sys_organization_definitions" )
@Where(clause = " is_deleted=0 " )
@SQLDelete(sql = "UPDATE sys_organization_definitions SET is_deleted = 1 WHERE organization_id = ?" )
@SQLDeleteAll(sql = "UPDATE sys_organization_definitions SET is_deleted = 1 WHERE organization_id = ?" )
public class OrganizationDefinitions extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 组织ID
     */
    @Id
//    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
//            strategy = GenerationType.SEQUENCE)
//    @GenericGenerator(
//            name = "SnowflakeIdGeneratorConfig",
//            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig" )
    @ApiModelProperty(value = "组织ID")
    @JSONField(name = "organizationId")
    private Long organizationId;

    /**
     * businessGroupId
     */
    @ApiModelProperty(value = "businessGroupId")
    @JSONField(name = "businessGroupId")
    private Long businessGroupId;

    /**
     * userDefinitionEnableDate
     */
    @ApiModelProperty(value = "userDefinitionEnableDate")
    @JSONField(name = "b", deserialize = false)
    @JsonIgnore
    private LocalDate userDefinitionEnableDate;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    @JSONField(name = "a", deserialize = false)
    @JsonIgnore
    private LocalDate disableDate;

    /**
     * 组织代码
     */
    @ApiModelProperty(value = "组织代码")
    @JSONField(name = "organizationCode")
    private String organizationCode;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    @JSONField(name = "organizationName")
    private String organizationName;

    /**
     * 账套ID
     */
    @ApiModelProperty(value = "账套ID")
    @JSONField(name = "setOfBooksId")
    private Long setOfBooksId;

    /**
     * chartOfAccountsId
     */
    @ApiModelProperty(value = "chartOfAccountsId")
    @JSONField(name = "chartOfAccountsId")
    private Long chartOfAccountsId;

    /**
     * inventoryEnabledFlag
     */
    @ApiModelProperty(value = "inventoryEnabledFlag")
    @JSONField(name = "inventoryEnabledFlag")
    private String inventoryEnabledFlag;

    /**
     * 业务实体ID
     */
    @ApiModelProperty(value = "业务实体ID")
    @JSONField(name = "operatingUnit")
    private Integer operatingUnit;

    /**
     * legalEntity
     */
    @ApiModelProperty(value = "legalEntity")
    @JSONField(name = "legalEntity")
    private Integer legalEntity;

    @ApiModelProperty(value = "SCP使用标识（Y）")
    @Column(name = "scp_flag")
    private String scpFlag;
    @ApiModelProperty(value = "电池BOM同步ERP(二期)")
    @Column(name = "cells_scp_flag")
    private String cellsScpFlag;
}
