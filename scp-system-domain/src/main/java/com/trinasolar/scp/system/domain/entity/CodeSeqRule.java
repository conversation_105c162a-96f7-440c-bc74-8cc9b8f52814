package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 单据序列规则定义表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-09 15:05:51
 */
@Entity
@ToString
@Data
@Table(name = "sys_code_seq_rule")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE sys_code_seq_rule SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE sys_code_seq_rule SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class CodeSeqRule extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 规则主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "规则主键")
    @Column(name = "id")
    private Long id;

    /**
     * seqId
     */
    @ApiModelProperty(value = "seqId")
    @Column(name = "seq_id")
    private Long seqId;

    /**
     * 规则顺序号
     */
    @ApiModelProperty(value = "规则顺序号")
    @Column(name = "rule_order")
    private Integer ruleOrder;

    /**
     * 规则中段的类型，具体值存储在集值中fixedChar，paramCode，date，seq
     */
    @ApiModelProperty(value = "规则中段的类型，具体值存储在集值中fixedChar，paramCode，date，seq")
    @Column(name = "rule_field")
    private String ruleField;

    /**
     * 段值
     */
    @ApiModelProperty(value = "段值")
    @Column(name = "rule_field_value")
    private String ruleFieldValue;

    /**
     * 日期格式
     */
    @ApiModelProperty(value = "日期格式")
    @Column(name = "date_pattern")
    private String datePattern;

    /**
     * 序列长度
     */
    @ApiModelProperty(value = "序列长度")
    @Column(name = "seq_length")
    private Integer seqLength;

    /**
     * 序列增量
     */
    @ApiModelProperty(value = "序列增量")
    @Column(name = "seq_increment")
    private Integer seqIncrement;

    /**
     * 序列开始值
     */
    @ApiModelProperty(value = "序列开始值")
    @Column(name = "seq_start_value")
    private Integer seqStartValue;


}
