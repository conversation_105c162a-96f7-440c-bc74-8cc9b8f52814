package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

@Entity
@ToString
@Data
@Table(name = "sys_lov_config" )
@Where(clause = " is_deleted=0 " )
@SQLDelete(sql = "update sys_lov_config set is_deleted = 1 where lov_attr_col_con_id = ?" )
@SQLDeleteAll(sql = "update sys_lov_config set is_deleted = 1 where lov_attr_col_con_id = ?" )
public class LovConfig extends BasePO implements Serializable {
    /**
     * lov头id
     */
    @Basic
    @ApiModelProperty(value = "lov头id" )
    private Long lovHeaderId;

    /**
     * lov扩展字段列配置id
     */
    @ApiModelProperty(value = "lov扩展字段列配置id")
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig" )
    private Long lovAttrColConId;

    /**
     * 扩展字段名
     */
    @Basic
    @ApiModelProperty(value = "扩展字段名")
    private String attrColName;

    /**
     * 扩展字段别名
     */
    @Basic
    @ApiModelProperty(value = "扩展字段别名")
    private String attrColAlias;

    /**
     * 扩展字段展示名
     */
    @Basic
    @ApiModelProperty(value = "扩展字段展示名")
    private String attrColTitle;

    /**
     * 扩展字段类型 1-文本 2-数字  3-值列表 4-日期
     */
    @Basic
    @ApiModelProperty(value = "扩展字段类型 1-文本 2-数字  3-值列表 4-日期")
    private Integer attrColTypeId;

    /**
     * 扩展字段数据来源
     */
    @Basic
    @ApiModelProperty(value = "扩展字段数据来源")
    private String attrColDataSource;

    /**
     * 扩展字段展示效果
     */
    @Basic
    @ApiModelProperty(value = "扩展字段展示效果")
    private String attrColUiType;
    @ApiModelProperty(value = "Lov名称")
    private String attrLovName;
    /**
     * lov筛选条件
     */
    @ApiModelProperty(value = "lov筛选条件")
    private String attrLovFilter;
    /**
     * UI类型
     */
    @ApiModelProperty(value = "UI类型")
    private String attrUiType;
    /**
     * 校验规则
     */
    @ApiModelProperty(value = "校验规则")
    private String attrRule;
    /**
     * 属性默认值
     */
    @ApiModelProperty(value = "属性默认值")
    private String attrDefaultValue;
    /**
     * 扩展字段长度
     */
    @Basic
    @ApiModelProperty(value = "扩展字段长度")
    private Integer attrColLength;

    /**
     * 扩展字段宽度
     */
    @Basic
    @ApiModelProperty(value = "扩展字段宽度")
    private Integer attrColWidth;

    /**
     * 扩展字段排序号
     */
    @Basic
    @ApiModelProperty(value = "扩展字段排序号")
    private Integer attrColSeqNum;

    /**
     * 有效标识
     */
    @Basic
    @ApiModelProperty(value = "有效标识")
    private String enableFlag;

    /**
     * 有效日期_起
     */
    @Basic
    @ApiModelProperty(value = "有效日期_起")
    private LocalDate effectiveStartDate;

    /**
     * 有效日期_止
     */
    @Basic
    @ApiModelProperty(value = "有效日期_止")
    private LocalDate effectiveEndDate;

    /**
     * 扩展字段1
     */
    @Basic
    @ApiModelProperty(value = "扩展字段1")
    private String attribute1;

    /**
     * 扩展字段2
     */
    @Basic
    @ApiModelProperty(value = "扩展字段2")
    private String attribute2;

    /**
     * 扩展字段3
     */
    @Basic
    @ApiModelProperty(value = "扩展字段3")
    private String attribute3;

    /**
     * 扩展字段4
     */
    @Basic
    @ApiModelProperty(value = "扩展字段4")
    private String attribute4;

    /**
     * 扩展字段5
     */
    @Basic
    @ApiModelProperty(value = "扩展字段5")
    private String attribute5;

    /**
     * 扩展字段6
     */
    @Basic
    @ApiModelProperty(value = "扩展字段6")
    private String attribute6;

    /**
     * 扩展字段7
     */
    @Basic
    @ApiModelProperty(value = "扩展字段7")
    private String attribute7;

    /**
     * 扩展字段8
     */
    @Basic
    @ApiModelProperty(value = "扩展字段8")
    private String attribute8;

    /**
     * 扩展字段9
     */
    @Basic
    @ApiModelProperty(value = "扩展字段9")
    private String attribute9;

    /**
     * 扩展字段10
     */
    @Basic
    @ApiModelProperty(value = "扩展字段10")
    private String attribute10;

    /**
     * 扩展字段11
     */
    @Basic
    @ApiModelProperty(value = "扩展字段11")
    private String attribute11;

    /**
     * 扩展字段12
     */
    @Basic
    @ApiModelProperty(value = "扩展字段12")
    private String attribute12;

    /**
     * 扩展字段13
     */
    @Basic
    @ApiModelProperty(value = "扩展字段13")
    private String attribute13;

    /**
     * 扩展字段14
     */
    @Basic
    @ApiModelProperty(value = "扩展字段14")
    private String attribute14;

    /**
     * 扩展字段15
     */
    @Basic
    @ApiModelProperty(value = "扩展字段15")
    private String attribute15;
}
