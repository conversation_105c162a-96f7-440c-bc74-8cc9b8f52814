package com.trinasolar.scp.system.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 前端多语言配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
@Data
@ApiModel(value = "Prompt查询条件", description = "查询条件")
public class PromptQuery extends PageDTO implements Serializable {

    private static final long serialVersionUID = -4238041157925944678L;

    /**
     * 多语言key
     */
    @ApiModelProperty(value = "多语言key")
    private String key;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "语言")
    private String lang;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "平台")
    private String platform;
}
