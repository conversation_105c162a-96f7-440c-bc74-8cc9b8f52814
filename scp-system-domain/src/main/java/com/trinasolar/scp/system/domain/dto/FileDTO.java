package com.trinasolar.scp.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;


/**
 * 附件表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-08 18:35:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "FileDTO对象", description = "DTO对象")
public class FileDTO implements Serializable {
    private static final long serialVersionUID = -1060924834262316465L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 外部系统主键
     */
    @ApiModelProperty(value = "外部系统主键")
    private String fk;

    /**
     * 员工号
     */
    @ApiModelProperty(value = "员工号")
    private String empNo;

    /**
     * 客户端ID
     */
    @ApiModelProperty(value = "客户端ID")
    private String clientId;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String name;

    /**
     * 文件主键名
     */
    @ApiModelProperty(value = "文件主键名")
    private String fileKey;

    /**
     * 文件夹名
     */
    @ApiModelProperty(value = "文件夹名")
    private String bucketName;

    /**
     * 文件URL
     */
    @ApiModelProperty(value = "文件URL")
    private String fileUrl;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String fileType;

    /**
     * 原始信息
     */
    @ApiModelProperty(value = "原始信息")
    private String origin;

    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小")
    private Long fileOriginSize;

    /**
     * 业务主键
     */
    @ApiModelProperty(value = "业务主键")
    private String bizKey;

    /**
     * 业务类型;一般是业务表名
     */
    @ApiModelProperty(value = "业务类型;一般是业务表名")
    private String bizType;
}
