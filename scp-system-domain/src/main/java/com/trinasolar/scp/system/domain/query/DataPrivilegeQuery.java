package com.trinasolar.scp.system.domain.query;

import lombok.Data;
import lombok.experimental.Accessors;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;

/**
 * 数据权限表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-17 15:39:12
 */
@Data
@ApiModel(value = "DataPrivilege查询条件", description = "查询条件")
@Accessors(chain = true)
public class DataPrivilegeQuery extends PageDTO implements Serializable {
    /**
     * 权限类型，一般大写为表名
     */
    @ApiModelProperty(value = "权限类型，一般大写为表名")
    private String privilegeType;
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;
    /**
     * 数据ID
     */
    @ApiModelProperty(value = "数据ID")
    private Long dataId;

    /**
     * 导出列映射顺序对象
     */
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
