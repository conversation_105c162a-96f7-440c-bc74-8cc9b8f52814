package com.trinasolar.scp.system.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.system.domain.entity.DailyRates;
import com.trinasolar.scp.system.domain.dto.DailyRatesDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 汇率表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-29 09:01:50
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface DailyRatesDEConvert extends BaseDEConvert<DailyRatesDTO, DailyRates> {

    DailyRatesDEConvert INSTANCE = Mappers.getMapper(DailyRatesDEConvert.class);

}
