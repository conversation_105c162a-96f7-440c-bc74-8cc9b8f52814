package com.trinasolar.scp.system.domain.save;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.time.LocalDate;

import com.trinasolar.scp.common.api.base.TokenDTO;


/**
 * 系统Lov脚本
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-03 13:46:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "LovScript保存参数", description = "保存参数")
public class LovScriptSaveDTO extends TokenDTO implements Serializable {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;
    /**
     * 脚本
     */
    @ApiModelProperty(value = "脚本")
    private String script;
    /**
     * 脚本语言
     */
    @ApiModelProperty(value = "脚本语言")
    private String lang;
}
