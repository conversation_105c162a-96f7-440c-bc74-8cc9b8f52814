package com.trinasolar.scp.system.domain.feign;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * 物料属性字段别名
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-02 10:28:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ItemAttrDTO对象", description = "DTO对象")
public class ItemAttrDTO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 源系统属性ID
     */
    @ApiModelProperty(value = "源系统属性ID")
    private String srcAttrId;
    /**
     * 源系统属性别名
     */
    @ApiModelProperty(value = "源系统属性别名")
    private String srcAttrAlias;
    /**
     * 源系统属性四级分类ID
     */
    @ApiModelProperty(value = "源系统属性四级分类ID")
    private String srcCategorySegment4Id;
    /**
     * 源系统属性四级分类
     */
    @ApiModelProperty(value = "源系统属性四级分类")
    private String srcCategorySegment4;
    /**
     * 源系统属性类型
     */
    @ApiModelProperty(value = "源系统属性类型")
    private String srcAttrType;
    /**
     * 源系统属性填写选项
     */
    @ApiModelProperty(value = "源系统属性填写选项")
    private String srcOptionFlag;
    /**
     * 对应源系统属性字段名
     */
    @ApiModelProperty(value = "对应源系统属性字段名")
    private String srcAttrColumn;
    /**
     * 语言
     */
    @ApiModelProperty(value = "语言")
    private String language;

    @ApiModelProperty(value = "attrLovs")
    private List<ItemAttrLovDTO> itemAttrLovDTOS;
}
