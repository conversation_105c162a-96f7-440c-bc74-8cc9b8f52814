package com.trinasolar.scp.system.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 系统消息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "MessageDTO对象", description = "DTO对象")
public class MessageDTO extends BaseDTO {

    /**
     * message_id
     */
    @ApiModelProperty(value = "message_id")
    private Long messageId;
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;
    /**
     * 语言
     */
    @ApiModelProperty(value = "语言")
    private String lang;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
}
