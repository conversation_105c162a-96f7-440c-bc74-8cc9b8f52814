package com.trinasolar.scp.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 供应商信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-28 15:29:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "SupplierDTO对象", description = "DTO对象")
public class SupplierDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;
    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String code;
    /**
     * 供应商ID（外部ID）
     */
    @ApiModelProperty(value = "供应商ID（外部ID）")
    private String supplierId;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String name;
    /**
     * 供应商英文名称
     */
    @ApiModelProperty(value = "供应商英文名称")
    private String enName;
    /**
     * 供应商别名
     */
    @ApiModelProperty(value = "供应商别名")
    private String alias;
    /**
     * 隶属集团
     */
    @ApiModelProperty(value = "隶属集团")
    private String affiliatedGroup;
    /**
     * 供应商状态
     */
    @ApiModelProperty(value = "供应商状态")
    private String status;
    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    private LocalDate expiryDate;
    /**
     * 是否内部供应商
     */
    @ApiModelProperty(value = "是否内部供应商")
    private String isInternalSupplier;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    private LocalDateTime lastUpdatedTime;
    /**
     * 业务实体
     */
    @ApiModelProperty(value = "业务实体")
    private String businessEntity;
    /**
     * 收货地点
     */
    @ApiModelProperty(value = "收货地点")
    private String receiptPlace;
}
