package com.trinasolar.scp.system.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 数据权限表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-17 15:39:12
 */
@Entity
@ToString
@Data
@Table(name = "sys_data_privilege")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE sys_data_privilege SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE sys_data_privilege SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class DataPrivilege extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 权限类型，一般大写为表名
     */
    @ApiModelProperty(value = "权限类型，一般大写为表名")
    @Column(name = "privilege_type")
    private String privilegeType;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @Column(name = "user_id")
    private String userId;

    /**
     * 数据ID
     */
    @ApiModelProperty(value = "数据ID")
    @Column(name = "data_id")
    private Long dataId;

    /**
     * 数据编码
     */
    @ApiModelProperty(value = "数据编码")
    @Column(name = "data_code")
    private String dataCode;


}
