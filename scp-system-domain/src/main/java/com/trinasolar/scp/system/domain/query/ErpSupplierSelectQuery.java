package com.trinasolar.scp.system.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * erp供应商表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-22 09:10:40
 */
@Data
@ApiModel(value = "ErpSupplierSelect查询条件", description = "查询条件")
@Accessors(chain = true)
public class ErpSupplierSelectQuery implements Serializable {
    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String vendorName;
}
