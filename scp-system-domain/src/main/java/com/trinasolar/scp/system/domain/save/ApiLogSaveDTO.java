package com.trinasolar.scp.system.domain.save;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.TokenDTO;


/**
 * 推送数据至其他系统接口日志表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-18 09:26:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ApiLog保存参数", description = "保存参数")
public class ApiLogSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;
    /**
     * 外部系统名称
     */
    @ApiModelProperty(value = "外部系统名称")
    private String extSystemName;
    /**
     * 业务数据单号
     */
    @ApiModelProperty(value = "业务数据单号")
    private String businessNo;
    /**
     * 接口地址
     */
    @ApiModelProperty(value = "接口地址")
    private String action;
    /**
     * 推送其他系统的参数
     */
    @ApiModelProperty(value = "推送其他系统的参数")
    private String inParams;
    /**
     * 其他系统的返回结果
     */
    @ApiModelProperty(value = "其他系统的返回结果")
    private String outParams;
    /**
     * 接口请求开始时间
     */
    @ApiModelProperty(value = "接口请求开始时间")
    private LocalDateTime startTime;
    /**
     * 接口请求结束时间
     */
    @ApiModelProperty(value = "接口请求结束时间")
    private LocalDateTime endTime;
    /**
     * 是否成功
     */
    @ApiModelProperty(value = "是否成功")
    private String success;
}
