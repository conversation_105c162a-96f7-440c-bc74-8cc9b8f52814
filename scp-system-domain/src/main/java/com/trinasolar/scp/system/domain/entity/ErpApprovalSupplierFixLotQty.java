package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 合格供应商整车数或整柜数表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-09 07:56:06
 */
@Entity
@ToString
@Data
@Table(name = "sys_erp_approval_supplier_fix_lot_qty")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE sys_erp_approval_supplier_fix_lot_qty SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE sys_erp_approval_supplier_fix_lot_qty SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class ErpApprovalSupplierFixLotQty extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 料号ID
     */
    @ApiModelProperty(value = "料号ID")
    @Column(name = "item_id")
    private Long itemId;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    @Column(name = "item_num")
    private String itemNum;

    /**
     * 料号描述
     */
    @ApiModelProperty(value = "料号描述")
    @Column(name = "item_description")
    private String itemDescription;

    /**
     * 辅料类型
     */
    @ApiModelProperty(value = "辅料类型")
    @Column(name = "category_segment5")
    private String categorySegment5;

    /**
     * 物料单位
     */
    @ApiModelProperty(value = "物料单位")
    @Column(name = "pri_uom")
    private String priUom;

    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    @Column(name = "vendor_id")
    private Long vendorId;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    @Column(name = "vendor_name")
    private String vendorName;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 整车数或整柜数
     */
    @ApiModelProperty(value = "整车数或整柜数")
    @Column(name = "fix_lot_qty")
    private BigDecimal fixLotQty;

    /**
     * 起订量
     */
    @ApiModelProperty(value = "起订量")
    @Column(name = "minimum_order_quantity")
    private BigDecimal minimumOrderQuantity;
}
