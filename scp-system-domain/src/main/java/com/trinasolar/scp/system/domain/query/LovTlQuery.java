package com.trinasolar.scp.system.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 系统LOV多语言表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
@Data
@ApiModel(value = "LovTl查询条件", description = "查询条件")
public class LovTlQuery extends PageDTO implements Serializable {
    @ApiModelProperty(value = "目标类型: header, config, line")
    private String pkType;

    @ApiModelProperty(value = "目标主键ID")
    private Long pkId;

    @ApiModelProperty(value = "目标主键ID")
    private List<Long> pkIds;
}
