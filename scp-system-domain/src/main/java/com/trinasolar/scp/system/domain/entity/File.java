package com.trinasolar.scp.system.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 附件表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-08 18:35:02
 */
@Entity
@ToString
@Data
@Table(name = "sys_file")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE sys_file SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE sys_file SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class File extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 外部系统主键
     */
    @ApiModelProperty(value = "外部系统主键")
    @Column(name = "fk")
    private String fk;

    /**
     * 员工号
     */
    @ApiModelProperty(value = "员工号")
    @Column(name = "emp_no")
    private String empNo;

    /**
     * 客户端ID
     */
    @ApiModelProperty(value = "客户端ID")
    @Column(name = "client_id")
    private String clientId;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    @Column(name = "name")
    private String name;

    /**
     * 文件主键名
     */
    @ApiModelProperty(value = "文件主键名")
    @Column(name = "file_key")
    private String fileKey;

    /**
     * 文件夹名
     */
    @ApiModelProperty(value = "文件夹名")
    @Column(name = "bucket_name")
    private String bucketName;

    /**
     * 文件URL
     */
    @ApiModelProperty(value = "文件URL")
    @Column(name = "file_url")
    private String fileUrl;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    @Column(name = "file_type")
    private String fileType;

    /**
     * 原始信息
     */
    @ApiModelProperty(value = "原始信息")
    @Column(name = "origin")
    private String origin;

    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小")
    @Column(name = "file_origin_size")
    private Long fileOriginSize;

    /**
     * 业务主键
     */
    @ApiModelProperty(value = "业务主键")
    @Column(name = "biz_key")
    private String bizKey;

    /**
     * 业务类型;一般是业务表名
     */
    @ApiModelProperty(value = "业务类型;一般是业务表名")
    @Column(name = "biz_type")
    private String bizType;


}
