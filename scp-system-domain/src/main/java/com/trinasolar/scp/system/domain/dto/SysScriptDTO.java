package com.trinasolar.scp.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 脚本维护
 * <AUTHOR>
 * @date 2022-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "SysScriptDTO对象", description = "DTO对象")
public class SysScriptDTO {
    /**
    *主键
    */
    @ApiModelProperty(value = "主键")
    private Long id ;

    /**
    *所属模块
    */
    @ApiModelProperty(value = "所属模块")
    private String scriptModule ;

    /**
    *脚本编码
    */
    @ApiModelProperty(value = "脚本编码")
    private String scriptCode ;

    /**
    *脚本名称
    */
    @ApiModelProperty(value = "脚本名称")
    private String scriptName ;

    /**
    *脚本内容
    */
    @ApiModelProperty(value = "脚本内容")
    private String scriptContent ;

    /**
    *备注
    */
    @ApiModelProperty(value = "备注")
    private String remark ;

}
