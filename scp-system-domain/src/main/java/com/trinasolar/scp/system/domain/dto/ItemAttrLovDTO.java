package com.trinasolar.scp.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;


/**
 * bom属性LOV
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-06 14:21:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ItemAttrLovDTO对象", description = "DTO对象")
public class ItemAttrLovDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;
    /**
     * 源系统LovID
     */
    @ApiModelProperty(value = "源系统LovID")
    private String lovId;
    /**
     * 源系统Lov Name
     */
    @ApiModelProperty(value = "源系统Lov Name")
    private String lovName;
    /**
     * 源系统属性ID
     */
    @ApiModelProperty(value = "源系统属性ID")
    private String srcAttrId;
    /**
     * 源系统属性名
     */
    @ApiModelProperty(value = "源系统属性名")
    private String srcAttrName;
    /**
     * LOV 行ID
     */
    @ApiModelProperty(value = "LOV 行ID")
    private String lovLineId;
    /**
     * LOV 行值
     */
    @ApiModelProperty(value = "LOV 行值")
    private String lovLineValue;
    /**
     * 语言
     */
    @ApiModelProperty(value = "语言")
    private String language;
    /**
     * 是否必填项
     */
    @ApiModelProperty(value = "是否必填项")
    private String isRequired;
}
