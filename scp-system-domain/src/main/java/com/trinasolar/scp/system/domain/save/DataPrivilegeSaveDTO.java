package com.trinasolar.scp.system.domain.save;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.TokenDTO;


/**
 * 数据权限表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-17 15:39:12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "DataPrivilege保存参数", description = "保存参数")
public class DataPrivilegeSaveDTO extends TokenDTO implements Serializable {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 权限类型，一般大写为表名
     */
    @ApiModelProperty(value = "权限类型，一般大写为表名")
    private String privilegeType;
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;
    /**
     * 数据ID
     */
    @ApiModelProperty(value = "数据ID")
    private Long dataId;
    /**
     * 数据编码
     */
    @ApiModelProperty(value = "数据编码")
    private String dataCode;
}
