package com.trinasolar.scp.system.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 单据序列规则定义表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-09 15:05:51
 */
@Data
@ApiModel(value = "CodeSeqRule查询条件", description = "查询条件")
@Accessors(chain = true)
public class CodeSeqRuleQuery extends PageDTO implements Serializable {

}
