package com.trinasolar.scp.system.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 供应商信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-28 15:29:29
 */
@Data
@ApiModel(value = "Supplier查询条件", description = "查询条件")
@Accessors(chain = true)
public class SupplierQuery extends PageDTO implements Serializable {
    @ApiModelProperty(value = "供应商ID集合")
    private List<String> supplierIds;

    @ApiModelProperty(value = "供应商名称集合")
    private List<String> names;
}
