package com.trinasolar.scp.system.domain.save;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * lov行表(SysLovLines)实体类
 *
 * <AUTHOR>
 * @since 2022-04-24 14:15:04
 */
@Data
@ToString
@ApiModel(value = "PutValueToLovLineSaveDTO", description = "新增或修改LovLine")
public class PutValueToLovLineSaveDTO implements Serializable {
    private static final long serialVersionUID = 388343368122252513L;

    @ApiModelProperty(value = "lov头id" )
    private String lovHeaderCode;

    /**
     * lov值
     */
    @ApiModelProperty(value = "lov值")
    @NotNull(message = "lov值 不能为空")
    private String lovValue;
}
