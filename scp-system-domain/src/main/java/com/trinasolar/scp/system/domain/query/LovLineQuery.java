package com.trinasolar.scp.system.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/4/25
 */
@Data
@ApiModel(value = "LovLineQuery", description = "LovLine查询条件")
public class LovLineQuery extends PageDTO {
    /**
     * LovHeadCode
     */
    @ApiModelProperty("LovHeadCode")
    String code;

    @ApiModelProperty("lovValue")
    Long lovLineId;

    @ApiModelProperty("lovValue")
    String lovValue;

    @ApiModelProperty("lovName")
    String name;

    @ApiModelProperty("enableFlag")
    String enableFlag;
    /**
     * 自定义where字串
     */
    @ApiModelProperty("where：例如 lov_name='Q0'  SQL 查询子串")
    String where;

    @ApiModelProperty("attributes例如： Y;;;;; 以;分割")
    String attributes;

//    /**
//     * 扩展字段1
//     */
//    @ApiModelProperty("attribute1")
//    private String attribute1;
//    /**
//     * 扩展字段2
//     */
//    @ApiModelProperty("attribute2")
//    private String attribute2;
//    /**
//     * 扩展字段3
//     */
//    @ApiModelProperty("attribute3")
//    private String attribute3;
//    /**
//     * 扩展字段4
//     */
//    @ApiModelProperty("attribute4")
//    private String attribute4;
//    /**
//     * 扩展字段5
//     */
//    @ApiModelProperty("attribute5")
//    private String attribute5;
//    /**
//     * 扩展字段6
//     */
//    @ApiModelProperty("attribute6")
//    private String attribute6;
//    /**
//     * 扩展字段7
//     */
//    @ApiModelProperty("attribute7")
//    private String attribute7;
//    /**
//     * 扩展字段8
//     */
//    @ApiModelProperty("attribute8")
//    private String attribute8;
//    /**
//     * 扩展字段9
//     */
//    @ApiModelProperty("attribute9")
//    private String attribute9;
//    /**
//     * 扩展字段10
//     */
//    @ApiModelProperty("attribute10")
//    private String attribute10;
//    /**
//     * 扩展字段11
//     */
//    @ApiModelProperty("attribute11")
//    private String attribute11;
//    /**
//     * 扩展字段12
//     */
//    @ApiModelProperty("attribute12")
//    private String attribute12;
//    /**
//     * 扩展字段13
//     */
//    @ApiModelProperty("attribute13")
//    private String attribute13;
//    /**
//     * 扩展字段14
//     */
//    @ApiModelProperty("attribute14")
//    private String attribute14;
//    /**
//     * 扩展字段15
//     */
//    @ApiModelProperty("attribute15")
//    private String attribute15;
}
