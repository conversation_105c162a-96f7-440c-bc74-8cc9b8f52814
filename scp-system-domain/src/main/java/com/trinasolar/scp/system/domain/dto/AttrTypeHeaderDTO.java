package com.trinasolar.scp.system.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class AttrTypeHeaderDTO extends BaseDTO implements Serializable {
    /**
     * 属性类型id
     */
    @ApiModelProperty(value = "属性类型id")
    private Long attrTypeHeaderId;

    /**
     * 属性类型编码
     */
    @ApiModelProperty(value = "属性类型编码")
    private String attrTypeCode;

    /**
     * 属性类型中文名
     */
    @ApiModelProperty(value = "属性类型中文名")
    private String attrTypeCnName;

    /**
     * 属性类型英文名
     */
    @ApiModelProperty(value = "属性类型英文名")
    private String attrTypeEnName;

    /**
     * 属性类型描述
     */
    @ApiModelProperty(value = "属性类型描述")
    private String attrTypeDesc;

    /**
     * 属性所属分类 1-产品族 2-材料  3-组件 4-其他
     */
    @ApiModelProperty(value = "属性所属分类 1-产品族 2-材料  3-组件 4-其他")
    private Integer attrCategoryId;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
    private String enableFlag;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDate effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDate effectiveEndDate;



    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1" )
    private String attribute1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2" )
    private String attribute2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3" )
    private String attribute3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty(value = "扩展字段4" )
    private String attribute4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty(value = "扩展字段5" )
    private String attribute5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty(value = "扩展字段6" )
    private String attribute6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty(value = "扩展字段7" )
    private String attribute7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty(value = "扩展字段8" )
    private String attribute8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty(value = "扩展字段9" )
    private String attribute9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty(value = "扩展字段10" )
    private String attribute10;

    /**
     * 扩展字段11
     */
    @ApiModelProperty(value = "扩展字段11" )
    private String attribute11;

    /**
     * 扩展字段12
     */
    @ApiModelProperty(value = "扩展字段12" )
    private String attribute12;

    /**
     * 扩展字段13
     */
    @ApiModelProperty(value = "扩展字段13" )
    private String attribute13;

    /**
     * 扩展字段14
     */
    @ApiModelProperty(value = "扩展字段14" )
    private String attribute14;

    /**
     * 扩展字段15
     */
    @ApiModelProperty(value = "扩展字段15" )
    private String attribute15;

    @ApiModelProperty(value = "行信息")
    private List<AttrTypeLineDTO> lines;

    private static final long serialVersionUID = 1L;
}

