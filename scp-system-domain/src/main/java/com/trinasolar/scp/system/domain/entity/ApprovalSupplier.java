package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 批准供应商
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-26 20:32:43
 */
@Entity
@ToString
@Data
@Table(name = "sys_approval_supplier")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE sys_approval_supplier SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE sys_approval_supplier SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ApprovalSupplier extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "id")
    @Column(name = "id")
    private Long id;

    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    @Column(name = "vendor_id")
    private Long vendorId;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    @Column(name = "vendor_name")
    private String vendorName;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    @Column(name = "brand")
    private String brand;

    /**
     * 料号_第五分类
     */
    @ApiModelProperty(value = "料号_第五分类")
    @Column(name = "category_segment5")
    private String categorySegment5;


}
