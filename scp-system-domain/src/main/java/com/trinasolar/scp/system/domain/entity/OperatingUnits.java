package com.trinasolar.scp.system.domain.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 业务实体
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-07 17:43:44
 */
@Entity
@ToString
@Data
@Table(name = "sys_operating_units")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE sys_operating_units SET is_deleted = 1 WHERE org_id = ?")
@SQLDeleteAll(sql = "UPDATE sys_operating_units SET is_deleted = 1 WHERE org_id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class OperatingUnits extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * 业务实体ID
     */
    @Id
//    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
//            strategy = GenerationType.SEQUENCE)
//    @GenericGenerator(
//            name = "SnowflakeIdGeneratorConfig",
//            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "业务实体ID")
    @Column(name = "org_id")
    private Long orgId;

    /**
     * 业务实体编码
     */
    @ApiModelProperty(value = "业务实体编码")
    @Column(name = "org_code")
    @JSONField(name = "operatingUnitsId")
    private String orgCode;

    /**
     * 业务实体名称
     */
    @ApiModelProperty(value = "业务实体名称")
    @Column(name = "org_name")
    @JSONField(name = "operatingUnitsName")
    private String orgName;

    /**
     * 业务实体描述
     */
    @ApiModelProperty(value = "业务实体描述")
    @Column(name = "description")
    private String description;

    /**
     * 业务实体简称
     */
    @ApiModelProperty(value = "业务实体简称")
    @Column(name = "org_short_name")
    @JSONField(name = "code")
    private String orgShortName;

    /**
     * 本位币币种
     */
    @ApiModelProperty(value = "本位币币种")
    @Column(name = "currency")
    @JSONField(name = "currencyCode")
    private String currency;

    /**
     * SCP使用标识（Y）
     */
    @ApiModelProperty(value = "SCP使用标识（Y）")
    @Column(name = "scp_flag")
    private String scpFlag;


}
