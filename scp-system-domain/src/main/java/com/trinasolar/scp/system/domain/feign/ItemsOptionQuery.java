package com.trinasolar.scp.system.domain.feign;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/2
 */
@Data
@ApiModel(value = "Items查询条件", description = "查询条件")
@Accessors(chain = true)
@Builder
public class ItemsOptionQuery extends PageDTO implements Serializable {
    private static final long serialVersionUID = -4600035505580407701L;

    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    private Long organizationId;

    @ApiModelProperty(value = "模糊查询code或者name, 右匹配")
    private String code;

    @ApiModelProperty(value = "模糊查询描述")
    private String itemDesc;
}
