package com.trinasolar.scp.system.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;


/**
 * 附件表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-08 18:35:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "File保存参数", description = "保存参数")
public class FileSaveDTO extends TokenDTO implements Serializable {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 外部系统主键
     */
    @ApiModelProperty(value = "外部系统主键")
    private String fk;
    /**
     * 员工号
     */
    @ApiModelProperty(value = "员工号")
    private String empNo;
    /**
     * 客户端ID
     */
    @ApiModelProperty(value = "客户端ID")
    private String clientId;
    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String name;
    /**
     * 文件主键名
     */
    @ApiModelProperty(value = "文件主键名")
    private String fileKey;
    /**
     * 文件夹名
     */
    @ApiModelProperty(value = "文件夹名")
    private String bucketName;
    /**
     * 文件URL
     */
    @NotBlank
    @ApiModelProperty(value = "文件URL")
    private String fileUrl;
    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String fileType;
    /**
     * 原始信息
     */
    @ApiModelProperty(value = "原始信息")
    private String origin;
    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小")
    private Long fileOriginSize;
    /**
     * 业务主键
     */
    @ApiModelProperty(value = "业务主键")
    private String bizKey;
    /**
     * 业务类型;一般是业务表名
     */
    @NotBlank
    @ApiModelProperty(value = "业务类型;一般是业务表名")
    private String bizType;
}
