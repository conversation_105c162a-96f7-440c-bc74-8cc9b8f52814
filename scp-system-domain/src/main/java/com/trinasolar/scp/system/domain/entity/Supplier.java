package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 供应商信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-28 15:29:29
 */
@Entity
@ToString
@Data
@Table(name = "sys_supplier")
@SQLDelete(sql = "UPDATE sys_supplier SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE sys_supplier SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class Supplier extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID")
    @Column(name = "id")
    private Long id;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @Column(name = "code")
    private String code;

    /**
     * 供应商ID（外部ID）
     */
    @ApiModelProperty(value = "供应商ID（外部ID）")
    @Column(name = "supplier_id")
    private String supplierId;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @Column(name = "name")
    private String name;

    /**
     * 供应商英文名称
     */
    @ApiModelProperty(value = "供应商英文名称")
    @Column(name = "en_name")
    private String enName;

    /**
     * 供应商别名
     */
    @ApiModelProperty(value = "供应商别名")
    @Column(name = "alias")
    private String alias;

    /**
     * 隶属集团
     */
    @ApiModelProperty(value = "隶属集团")
    @Column(name = "affiliated_group")
    private String affiliatedGroup;

    /**
     * 供应商状态
     */
    @ApiModelProperty(value = "供应商状态")
    @Column(name = "status")
    private String status;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    @Column(name = "expiry_date")
    private LocalDate expiryDate;

    /**
     * 是否内部供应商
     */
    @ApiModelProperty(value = "是否内部供应商")
    @Column(name = "is_internal_supplier")
    private String isInternalSupplier;

    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @Column(name = "last_updated_time")
    private LocalDateTime lastUpdatedTime;

    /**
     * 业务实体
     */
    @ApiModelProperty(value = "业务实体")
    @Column(name = "business_entity")
    private String businessEntity;

    /**
     * 收货地点
     */
    @ApiModelProperty(value = "收货地点")
    @Column(name = "receipt_place")
    private String receiptPlace;


}
