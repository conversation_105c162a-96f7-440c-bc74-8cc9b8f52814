package com.trinasolar.scp.system.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "LovLineQuery", description = "LovLine查询条件")
public class LovLineSynQuery {

    @ApiModelProperty("类别id" )
    @NotNull
    String code;
    @ApiModelProperty(value = "上次更新时间" )
    @NotNull
    String lastUpdatedTime;

}
