package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 批准供应商
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:05:46
 */
@Entity
@ToString
@Data
@Table(name = "sys_erp_approval_supplier")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE sys_approval_supplier SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE sys_approval_supplier SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ErpApprovalSupplier extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "id")
    @Column(name = "id")
    private Long id;

    /**
     * 维护组织ID
     */
    @ApiModelProperty(value = "维护组织ID")
    @Column(name = "organization_id")
    private Long organizationId;

    /**
     * 库存组织名称
     */
    @ApiModelProperty(value = "库存组织名称")
    @Column(name = "ou_name")
    private String ouName;

    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期")
    @Column(name = "creation_date")
    private LocalDateTime creationDate;

    /**
     * 料号ID
     */
    @ApiModelProperty(value = "料号ID")
    @Column(name = "item_id")
    private Long itemId;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    @Column(name = "item_num")
    private String itemNum;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Column(name = "item_description")
    private String itemDescription;

    /**
     * 物料大小类
     */
    @ApiModelProperty(value = "物料大小类")
    @Column(name = "item_category")
    private String itemCategory;

    /**
     * 业务
     */
    @ApiModelProperty(value = "业务")
    @Column(name = "business")
    private String business;

    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    @Column(name = "vendor_id")
    private Long vendorId;

    /**
     * 供应商地址ID
     */
    @ApiModelProperty(value = "供应商地址ID")
    @Column(name = "verdor_site_id")
    private Long verdorSiteId;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    @Column(name = "vendor_name")
    private String vendorName;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    @Column(name = "vendor_site")
    private String vendorSite;

    /**
     * 状态ID
     */
    @ApiModelProperty(value = "状态ID")
    @Column(name = "status_id")
    private Long statusId;

    /**
     * 批准状态
     */
    @ApiModelProperty(value = "批准状态")
    @Column(name = "asl_status")
    private String aslStatus;

    /**
     * 全局标识
     */
    @ApiModelProperty(value = "全局标识")
    @Column(name = "global_flag")
    private String globalFlag;

    /**
     * 非全局用于库存组织ID
     */
    @ApiModelProperty(value = "非全局用于库存组织ID")
    @Column(name = "using_organization_id")
    private Long usingOrganizationId;

    /**
     * 最后更新日期
     */
    @ApiModelProperty(value = "最后更新日期")
    @Column(name = "last_update_date")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "物料单位")
    @Column(name = "pri_uom")
    private String priUom;

    @ApiModelProperty(value = "整车数")
    @Column(name = "fix_lot_qty")
    private BigDecimal fixLotQty;

    @ApiModelProperty(value = "确认状态")
    @Column(name = "confirm_status")
    private String confirmStatus;


    /**
     * 料号_第五分类
     */
    @ApiModelProperty(value = "料号_第五分类")
    @Column(name = "category_segment5")
    private String categorySegment5;

    @ApiModelProperty(value = "提前期")
    @Column(name = "lead_time")
    private Integer leadTime;

}
