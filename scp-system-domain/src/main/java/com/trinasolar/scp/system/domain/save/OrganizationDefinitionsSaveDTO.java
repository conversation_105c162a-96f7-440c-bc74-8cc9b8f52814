package com.trinasolar.scp.system.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-28 11:38:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "OrganizationDefinitions保存参数", description = "保存参数")
public class OrganizationDefinitionsSaveDTO extends TokenDTO implements Serializable {

    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    private Long organizationId;

    @ApiModelProperty(value = "SCP使用标识（Y）")
    private String scpFlag;
    @ApiModelProperty(value = "电池BOM同步ERP(二期)")
    private String cellsScpFlag;
}
