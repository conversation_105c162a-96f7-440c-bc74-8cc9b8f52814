package com.trinasolar.scp.system.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/25
 */
@Data
@ApiModel(value = "AttrTypeHeader", description = "查询条件")
public class AttrTypeHeaderQuery extends PageDTO {
    @ApiModelProperty("code")
    String code;

    @ApiModelProperty("name")
    String name;

    @ApiModelProperty(value = "属性所属分类 1-产品族 2-材料  3-组件 4-其他")
    private Integer attrCategoryId;

    @ApiModelProperty(value = "属性所属分类 1-产品族 2-材料  3-组件 4-其他 8-电池")
    private List<Integer> attrCategoryIds;
}
