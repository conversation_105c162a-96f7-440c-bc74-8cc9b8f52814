package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;


/**
 * 前端多语言配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
@Entity
@ToString
@Data
@Table(name = "sys_prompt")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE sys_prompt SET is_deleted = 1 WHERE prompt_id = ?")
@SQLDeleteAll(sql = "UPDATE sys_prompt SET is_deleted = 1 WHERE prompt_id = ?")
public class Prompt extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * prompt_id
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "prompt_id")
    private Long promptId;

    /**
     * 多语言key
     */
    @ApiModelProperty(value = "多语言key")
    @Column(name = "`key`")
    private String key;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @Column(name = "`code`")
    private String code;

    /**
     * 语言
     */
    @ApiModelProperty(value = "语言")
    private String lang;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 描述
     */
    @ApiModelProperty(value = "平台")
    private String platform;


}
