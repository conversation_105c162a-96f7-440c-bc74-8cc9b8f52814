package com.trinasolar.scp.system.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class AttrTypeLineDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 属性类型id
     */
    @ApiModelProperty(value = "属性类型id")
    private Long attrTypeHeaderId;

    /**
     * 行编号
     */
    @ApiModelProperty(value = "行编号")
    private Integer colNo;

    /**
     * 属性类型Code
     */
    @ApiModelProperty(value = "属性类型Code")
    private String attrTypeHeaderCode;

    /**
     * 属性行id
     */
    @ApiModelProperty(value = "属性行id")
    private Long attrLineId;

    /**
     * 属性中文名
     */
    @ApiModelProperty(value = "属性中文名")
    private String attrCnName;

    /**
     * 属性英文名
     */
    @ApiModelProperty(value = "属性英文名")
    private String attrEnName;

    /**
     * 属性值类型id 1-文本 2-数字  3-值列表 4-日期
     */
    @ApiModelProperty(value = "属性值类型id 1-文本 2-数字  3-值列表 4-日期")
    private Integer attrValueTypeId;

    @ApiModelProperty(value = "属性值类型名称 1-文本 2-数字  3-值列表 4-日期")
    private String attrValueTypeName;

    /**
     * 属性描述
     */
    @ApiModelProperty(value = "属性描述")
    private String attrDesc;

    /**
     * 来源字段名
     */
    @ApiModelProperty(value = "来源字段名")
    private String sourceColumn;

    /**
     * 属性同步标识 来源字段不为空时可选为Y，Y-同步，N-不同步
     */
    @ApiModelProperty(value = "属性同步标识 来源字段不为空时可选为Y，Y-同步，N-不同步")
    private String attrSyncedFlag;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
    private String enableFlag;
    /**
     * Lov名称
     */
    @ApiModelProperty(value = "Lov名称")
    private String attrLovName;
    /**
     * lov筛选条件
     */
    @ApiModelProperty(value = "lov筛选条件")
    private String attrLovFilter;
    /**
     * UI类型
     */
    @ApiModelProperty(value = "UI类型")
    private String attrUiType;
    /**
     * 校验规则
     */
    @ApiModelProperty(value = "转换规则")
    private String attrRule;
    /**
     * 属性默认值
     */
    @ApiModelProperty(value = "属性默认值")
    private String attrDefaultValue;
    /**
     * 原始属性ID
     */
    @ApiModelProperty(value = "原始属性ID")
    private Long attrSourceAttrId;

    @ApiModelProperty(value = "原始属性类型名称")
    private String attrSourceHeaderName;

    @ApiModelProperty(value = "原始属性名称")
    private String attrSourceAttrName;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDate effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDate effectiveEndDate;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attrCode;


    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1")
    private String attribute1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2")
    private String attribute2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3")
    private String attribute3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty(value = "扩展字段4")
    private String attribute4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty(value = "扩展字段5")
    private String attribute5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty(value = "扩展字段6")
    private String attribute6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty(value = "扩展字段7")
    private String attribute7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty(value = "扩展字段8")
    private String attribute8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty(value = "扩展字段9")
    private String attribute9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty(value = "扩展字段10")
    private String attribute10;

    /**
     * 扩展字段11
     */
    @ApiModelProperty(value = "扩展字段11")
    private String attribute11;

    /**
     * 扩展字段12
     */
    @ApiModelProperty(value = "扩展字段12")
    private String attribute12;

    /**
     * 扩展字段13
     */
    @ApiModelProperty(value = "扩展字段13")
    private String attribute13;

    /**
     * 扩展字段14
     */
    @ApiModelProperty(value = "扩展字段14")
    private String attribute14;

    /**
     * 扩展字段15
     */
    @ApiModelProperty(value = "校验规则")
    private String attribute15;
}

