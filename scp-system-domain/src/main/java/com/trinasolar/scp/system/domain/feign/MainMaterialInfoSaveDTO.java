package com.trinasolar.scp.system.domain.feign;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 主物料信息
 *
 * <AUTHOR>
 * @date 2022-9-8
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "MainMaterialInfo保存参数", description = "保存参数")
public class MainMaterialInfoSaveDTO extends TokenDTO implements Serializable {
    private static final long serialVersionUID = -8969950461223269613L;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * MDM;ID
     */
    @ApiModelProperty(value = "MDM")
    private String itemId;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String itemDescription;

    /**
     * 物料小类
     */
    @ApiModelProperty(value = "物料小类")
    private String itemCategoryDesc;

    /**
     * 外部日文名称
     */
    @ApiModelProperty(value = "外部日文名称")
    private String externalJapaneseName;

    /**
     * 外部英文名称
     */
    @ApiModelProperty(value = "外部英文名称")
    private String externalEnglishName;

    /**
     * 外部中文名称
     */
    @ApiModelProperty(value = "外部中文名称")
    private String externalChineseName;

    /**
     * 销售料号
     */
    @ApiModelProperty(value = "销售料号")
    private String salesItem;

    /**
     * 主单位
     */
    @ApiModelProperty(value = "主单位")
    private String uom;

    /**
     * 物料状态
     */
    @ApiModelProperty(value = "物料状态")
    private String itemStatus;

    /**
     * 成本分类
     */
    @ApiModelProperty(value = "成本分类")
    private String costCategory;

    /**
     * 物料审批参考描述
     */
    @ApiModelProperty(value = "物料审批参考描述")
    private String approveRefDes;

    /**
     * BPM单号
     */
    @ApiModelProperty(value = "BPM单号")
    private String bpmNo;

    /**
     * ERP旧料号
     */
    @ApiModelProperty(value = "ERP旧料号")
    private String erpHistoryNum;

    /**
     * 属性个数
     */
    @ApiModelProperty(value = "属性个数")
    private Integer segNum;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg1;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg2;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg3;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg4;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg5;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg6;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg7;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg8;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg9;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg10;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg11;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg12;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg13;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg14;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg15;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg16;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg17;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg18;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg19;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg20;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg21;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg22;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg23;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg24;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg25;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg26;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg27;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg28;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg29;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg30;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg31;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg32;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg33;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg34;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg35;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg36;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg37;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg38;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg39;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg40;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg41;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg42;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg43;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg44;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg45;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg46;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg47;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg48;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg49;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String seg50;

    /**
     * 创建人邮箱
     */
    @ApiModelProperty(value = "创建人邮箱")
    private String createdEmail;

    /**
     * 修改人邮箱
     */
    @ApiModelProperty(value = "修改人邮箱")
    private String updatedEmail;

    /**
     * 一级分类
     */
    @ApiModelProperty(value = "一级分类")
    private String level1;

    /**
     * 一级分类ID
     */
    @ApiModelProperty(value = "一级分类ID")
    private String level1Id;

    /**
     * 二级分类
     */
    @ApiModelProperty(value = "二级分类")
    private String level2;

    /**
     * 二级分类ID
     */
    @ApiModelProperty(value = "二级分类ID")
    private String level2Id;

    /**
     * 三级分类
     */
    @ApiModelProperty(value = "三级分类")
    private String level3;

    /**
     * 三级分类ID
     */
    @ApiModelProperty(value = "三级分类ID")
    private String level3Id;

    /**
     * 四级分类
     */
    @ApiModelProperty(value = "四级分类")
    private String level4;

    /**
     * 四级分类ID
     */
    @ApiModelProperty(value = "四级分类ID")
    private String level4Id;

    /**
     * 第五分类
     */
    @ApiModelProperty(value = "第五分类")
    private String categorySegment5;


    /**
     * 第六分类
     */
    @ApiModelProperty(value = "第六分类")
    private String categorySegment6;

}