package com.trinasolar.scp.system.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.system.domain.dto.ErpApprovalSupplierFixLotQtyDTO;
import com.trinasolar.scp.system.domain.entity.ErpApprovalSupplierFixLotQty;
import com.trinasolar.scp.system.domain.excel.ErpApprovalSupplierFixLotQtyExcelDTO;
import com.trinasolar.scp.system.domain.save.ErpApprovalSupplierFixLotQtySaveDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

import static org.mapstruct.NullValueCheckStrategy.ALWAYS;

/**
 * 合格供应商整车数或整柜数表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-09 07:56:06
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = ALWAYS)
public interface ErpApprovalSupplierFixLotQtyDEConvert extends BaseDEConvert<ErpApprovalSupplierFixLotQtyDTO, ErpApprovalSupplierFixLotQty> {

    ErpApprovalSupplierFixLotQtyDEConvert INSTANCE = Mappers.getMapper(ErpApprovalSupplierFixLotQtyDEConvert.class);

    @Override
    @Mappings({
            @Mapping(target = "isOverseaName",
                    expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getNameByValue("
                            + "com.trinasolar.scp.system.domain.constant.LovHeaderCodeConstant.IS_OVERSEA," +
                            "erpApprovalSupplierFixLotQty.getIsOversea()))")
    })
    ErpApprovalSupplierFixLotQtyDTO toDto(ErpApprovalSupplierFixLotQty erpApprovalSupplierFixLotQty);

    List<ErpApprovalSupplierFixLotQtyExcelDTO> toExcelDTO(List<ErpApprovalSupplierFixLotQtyDTO> dtos);

    ErpApprovalSupplierFixLotQtyExcelDTO toExcelDTO(ErpApprovalSupplierFixLotQtyDTO dto);

    List<ErpApprovalSupplierFixLotQtyDTO> excelToDto(List<ErpApprovalSupplierFixLotQtyExcelDTO> excelDtos);

    ErpApprovalSupplierFixLotQtyDTO excelToDto(ErpApprovalSupplierFixLotQtyExcelDTO excelDto);

    @Mappings({
            @Mapping(target = "isOversea",
                    expression = "java(org.apache.commons.lang3.StringUtils.isBlank(excelDtos.getIsOverseaName())? \"\"  : com.trinasolar.scp.common.api.util.LovUtils.getLovValueByHeaderCodeAndName("
                            + "com.trinasolar.scp.system.domain.constant.LovHeaderCodeConstant.IS_OVERSEA," +
                            "excelDtos.getIsOverseaName()))", nullValueCheckStrategy = ALWAYS)
    })
    ErpApprovalSupplierFixLotQtySaveDTO excelToSaveDto(ErpApprovalSupplierFixLotQtyExcelDTO excelDtos);

    List<ErpApprovalSupplierFixLotQtySaveDTO> excelToSaveDto(List<ErpApprovalSupplierFixLotQtyExcelDTO> excelDtos);
}
