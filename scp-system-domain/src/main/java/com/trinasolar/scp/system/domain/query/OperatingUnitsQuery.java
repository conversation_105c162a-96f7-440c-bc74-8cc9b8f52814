package com.trinasolar.scp.system.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 业务实体
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-07 17:43:44
 */
@Data
@ApiModel(value = "OperatingUnits查询条件", description = "查询条件")
@Accessors(chain = true)
public class OperatingUnitsQuery extends PageDTO implements Serializable {
    /**
     * orgId集合
     */
    @ApiModelProperty(value = "orgId集合")
    private List<Long> orgIds;

    /**
     * 导出列映射顺序对象
     */
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;

    /**
     * 业务实体编码
     */
    @ApiModelProperty(value = "业务实体编码")
    private String orgCode;

    /**
     * 业务实体名称
     */
    @ApiModelProperty(value = "业务实体名称")
    private String orgName;


    /**
     * 是否生效（Y/N）
     */
    @ApiModelProperty(value = "是否生效")
    private String scpFlag;



}
