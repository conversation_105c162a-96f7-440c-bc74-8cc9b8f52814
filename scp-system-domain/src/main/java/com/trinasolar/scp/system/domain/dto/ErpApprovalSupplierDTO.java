package com.trinasolar.scp.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 批准供应商
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:05:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ApprovalSupplierDTO对象", description = "DTO对象")
public class ErpApprovalSupplierDTO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 维护组织ID
     */
    @ApiModelProperty(value = "维护组织ID")
    private Long organizationId;

    /**
     * 库存组织名称
     */
    @ApiModelProperty(value = "库存组织名称")
    private String ouName;

    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime creationDate;

    /**
     * 料号ID
     */
    @ApiModelProperty(value = "料号ID")
    private Long itemId;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String itemNum;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String itemDescription;

    /**
     * 物料大小类
     */
    @ApiModelProperty(value = "物料大小类")
    private String itemCategory;

    /**
     * 业务
     */
    @ApiModelProperty(value = "业务")
    private String business;

    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    private Long vendorId;

    /**
     * 供应商地址ID
     */
    @ApiModelProperty(value = "供应商地址ID")
    private Long verdorSiteId;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String vendorName;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String vendorSite;

    /**
     * 状态ID
     */
    @ApiModelProperty(value = "状态ID")
    private Long statusId;

    /**
     * 批准状态
     */
    @ApiModelProperty(value = "批准状态")
    private String aslStatus;

    /**
     * 全局标识
     */
    @ApiModelProperty(value = "全局标识")
    private String globalFlag;

    /**
     * 非全局用于库存组织ID
     */
    @ApiModelProperty(value = "非全局用于库存组织ID")
    private Long usingOrganizationId;

    /**
     * 最后更新日期
     */
    @ApiModelProperty(value = "最后更新日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "物料单位")
    private String priUom;

    @ApiModelProperty(value = "整车数")
    private BigDecimal fixLotQty;

    @ApiModelProperty(value = "提前期")
    private Integer leadTime;

    @ApiModelProperty(value = "确认状态")
    private String confirmStatus;

    @ApiModelProperty(value = "厂牌")
    private String brands;

    /**
     * 料号_第五分类
     */
    @ApiModelProperty(value = "料号_第五分类")
    private String categorySegment5;


    /**
     * 料号
     */
    @ApiModelProperty(value = "是否是内部供应商  Y 是内部")
    private String attribute2;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String attribute1;

    @ApiModelProperty(value = "是否维护整车数")
    private String hasPurchaseQuantity;

    @ApiModelProperty(value = "整车维护信息")
    private String purchaseQuantityInfo;


}
