package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;


/**
 * 系统语言
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:17
 */
@Entity
@ToString
@Data
@Table(name = "sys_language" )
@Where(clause = " is_deleted=0 " )
@SQLDelete(sql = "UPDATE sys_language SET is_deleted = 1 WHERE language_id = ?" )
@SQLDeleteAll(sql = "UPDATE sys_language SET is_deleted = 1 WHERE language_id = ?" )
public class Language extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * language_id
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig" )
    @ApiModelProperty(value = "language_id")
    private Long languageId;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;


}
