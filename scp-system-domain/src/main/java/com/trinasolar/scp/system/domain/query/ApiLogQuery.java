package com.trinasolar.scp.system.domain.query;

import lombok.Data;
import lombok.experimental.Accessors;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;

/**
 * 推送数据至其他系统接口日志表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-18 09:26:45
 */
@Data
@ApiModel(value = "ApiLog查询条件", description = "查询条件")
@Accessors(chain = true)
public class ApiLogQuery extends PageDTO implements Serializable {
    /**
     * 导出列映射顺序对象
     */
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
