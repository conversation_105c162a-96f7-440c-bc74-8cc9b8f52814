package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * erp供应商表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-22 09:10:40
 */
@Entity
@ToString
@Data
@Table(name = "sys_erp_supplier")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE sys_erp_supplier SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE sys_erp_supplier SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ErpSupplier extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "id")
    @Column(name = "id")
    private Long id;

    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    @Column(name = "vendor_id")
    private Long vendorId;

    /**
     * 客户表ID
     */
    @ApiModelProperty(value = "客户表ID")
    @Column(name = "party_id")
    private Long partyId;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @Column(name = "vendor_name")
    private String vendorName;

    /**
     * 供应商简称
     */
    @ApiModelProperty(value = "供应商简称")
    @Column(name = "vendor_name_alt")
    private String vendorNameAlt;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    @Column(name = "vendor_num")
    private String vendorNum;

    /**
     * 起始日期
     */
    @ApiModelProperty(value = "起始日期")
    @Column(name = "start_date_active")
    private LocalDateTime startDateActive;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    @Column(name = "end_date_active")
    private LocalDateTime endDateActive;

    /**
     * 有效表示
     */
    @ApiModelProperty(value = "有效表示")
    @Column(name = "enabled_flag")
    private String enabledFlag;

    /**
     * 付款币种
     */
    @ApiModelProperty(value = "付款币种")
    @Column(name = "payment_currency_code")
    private String paymentCurrencyCode;

    /**
     * 发票币种
     */
    @ApiModelProperty(value = "发票币种")
    @Column(name = "invoice_currency_code")
    private String invoiceCurrencyCode;

    /**
     * 统一授信代码
     */
    @ApiModelProperty(value = "统一授信代码")
    @Column(name = "rep_registration_number")
    private String repRegistrationNumber;

    /**
     * 付款条件ID
     */
    @ApiModelProperty(value = "付款条件ID")
    @Column(name = "terms_id")
    private Long termsId;

    /**
     * 付款条件
     */
    @ApiModelProperty(value = "付款条件")
    @Column(name = "terms_name")
    private String termsName;

    /**
     * 供应商类型
     */
    @ApiModelProperty(value = "供应商类型")
    @Column(name = "vendor_type_lookup_code")
    private String vendorTypeLookupCode;

    /**
     * 弹性域上下文
     */
    @ApiModelProperty(value = "弹性域上下文")
    @Column(name = "attribute_category")
    private String attributeCategory;

    /**
     * attribute1
     */
    @ApiModelProperty(value = "attribute1")
    @Column(name = "attribute1")
    private String attribute1;

    /**
     * attribute2
     */
    @ApiModelProperty(value = "attribute2")
    @Column(name = "attribute2")
    private String attribute2;

    /**
     * attribute3
     */
    @ApiModelProperty(value = "attribute3")
    @Column(name = "attribute3")
    private String attribute3;

    /**
     * attribute4
     */
    @ApiModelProperty(value = "attribute4")
    @Column(name = "attribute4")
    private String attribute4;

    /**
     * attribute5
     */
    @ApiModelProperty(value = "attribute5")
    @Column(name = "attribute5")
    private String attribute5;

    /**
     * attribute6
     */
    @ApiModelProperty(value = "attribute6")
    @Column(name = "attribute6")
    private String attribute6;

    /**
     * attribute7
     */
    @ApiModelProperty(value = "attribute7")
    @Column(name = "attribute7")
    private String attribute7;

    /**
     * attribute8
     */
    @ApiModelProperty(value = "attribute8")
    @Column(name = "attribute8")
    private String attribute8;

    /**
     * attribute9
     */
    @ApiModelProperty(value = "attribute9")
    @Column(name = "attribute9")
    private String attribute9;

    /**
     * attribute10
     */
    @ApiModelProperty(value = "attribute10")
    @Column(name = "attribute10")
    private String attribute10;

    /**
     * attribute11
     */
    @ApiModelProperty(value = "attribute11")
    @Column(name = "attribute11")
    private String attribute11;

    /**
     * attribute12
     */
    @ApiModelProperty(value = "attribute12")
    @Column(name = "attribute12")
    private String attribute12;

    /**
     * attribute13
     */
    @ApiModelProperty(value = "attribute13")
    @Column(name = "attribute13")
    private String attribute13;

    /**
     * attribute14
     */
    @ApiModelProperty(value = "attribute14")
    @Column(name = "attribute14")
    private String attribute14;

    /**
     * attribute15
     */
    @ApiModelProperty(value = "attribute15")
    @Column(name = "attribute15")
    private String attribute15;


}
