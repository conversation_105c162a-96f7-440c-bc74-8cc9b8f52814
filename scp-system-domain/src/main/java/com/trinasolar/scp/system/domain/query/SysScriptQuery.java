package com.trinasolar.scp.system.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * 脚本维护
 * <AUTHOR>
 * @date 2022-10-13
 */
@Data
@ApiModel(value = "SysScript查询条件", description = "查询条件")
@Accessors(chain = true)
public class SysScriptQuery extends PageDTO implements Serializable {
    /**
     *所属模块
     */
    @ApiModelProperty(value = "所属模块")
    private String scriptModule ;
    /**
     *脚本编码
     */
    @ApiModelProperty(value = "脚本编码")
    private String scriptCode ;
    /**
     *脚本名称
     */
    @ApiModelProperty(value = "脚本名称")
    private String scriptName ;

    private ExcelPara excelPara;
}
