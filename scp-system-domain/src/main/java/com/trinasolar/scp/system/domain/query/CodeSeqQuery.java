package com.trinasolar.scp.system.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 单据序列主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-09 15:05:46
 */
@Data
@ApiModel(value = "CodeSeq查询条件", description = "查询条件")
@Accessors(chain = true)
public class CodeSeqQuery extends PageDTO implements Serializable {
    @ApiModelProperty("要生成的code,需要先配置")
    private String seqCode;

    @ApiModelProperty("paramCode的值 多个时用 , 分割")
    private String codes;

    @ApiModelProperty("生成code中包含的日期，不传取当前时间")
    private LocalDateTime includeDate;
}
