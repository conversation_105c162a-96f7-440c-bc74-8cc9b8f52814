package com.trinasolar.scp.system.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;


/**
 * 属性行来源映射表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-05 19:21:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "AttrLineMapDTO对象", description = "DTO对象")
public class AttrLineMapDTO extends BaseDTO {

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 属性行id
     */
    @NotNull(message = "属性行id不能为空")
    @ApiModelProperty(value = "属性行id")
    private Long attrLineId;
    /**
     * 来源属性ID
     */
    @ApiModelProperty(value = "来源属性ID")
    private String srcAttrId;
    /**
     * 来源属性值
     */
    @ApiModelProperty(value = "来源属性值")
    private String srcAttrValue;

     @ApiModelProperty(value = "源系统物料4级分类ID")
     private String srcCategorySegment4Id;

     @ApiModelProperty(value = "源系统物料4级分类")
     private String srcCategorySegment4;

    @ApiModelProperty(value = "来源字段")
    private String srcAttrColumn;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
    private String enableFlag;
    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDateTime effectiveStartDate;
    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDateTime effectiveEndDate;
    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1")
    private String attribute1;
    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2")
    private String attribute2;
    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3")
    private String attribute3;
    /**
     * 扩展字段4
     */
    @ApiModelProperty(value = "扩展字段4")
    private String attribute4;
    /**
     * 扩展字段5
     */
    @ApiModelProperty(value = "扩展字段5")
    private String attribute5;
    /**
     * 扩展字段6
     */
    @ApiModelProperty(value = "扩展字段6")
    private String attribute6;
    /**
     * 扩展字段7
     */
    @ApiModelProperty(value = "扩展字段7")
    private String attribute7;
    /**
     * 扩展字段8
     */
    @ApiModelProperty(value = "扩展字段8")
    private String attribute8;
    /**
     * 扩展字段9
     */
    @ApiModelProperty(value = "扩展字段9")
    private String attribute9;
    /**
     * 扩展字段10
     */
    @ApiModelProperty(value = "扩展字段10")
    private String attribute10;
    /**
     * 扩展字段11
     */
    @ApiModelProperty(value = "扩展字段11")
    private String attribute11;
    /**
     * 扩展字段12
     */
    @ApiModelProperty(value = "扩展字段12")
    private String attribute12;
    /**
     * 扩展字段13
     */
    @ApiModelProperty(value = "扩展字段13")
    private String attribute13;
    /**
     * 扩展字段14
     */
    @ApiModelProperty(value = "扩展字段14")
    private String attribute14;
    /**
     * 扩展字段15
     */
    @ApiModelProperty(value = "扩展字段15")
    private String attribute15;
    /**
     * 属性排序号
     */
    @ApiModelProperty(value = "属性排序号")
    private Integer seqNum;
}
