package com.trinasolar.scp.system.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 批准供应商
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:05:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ApprovalSupplier保存参数", description = "保存参数")
public class ErpApprovalSupplierSaveDTO extends TokenDTO implements Serializable {

    /**
     * id
     */
    @ApiModelProperty(value = "料号")
    private String itemNum;

    @ApiModelProperty(value = "供应商")
    private String vendorName;

    @ApiModelProperty(value = "整车数")
    private BigDecimal fixLotQty;

    @ApiModelProperty(value = "提前期")
    private Integer leadTime;

    @ApiModelProperty(value = "确认状态")
    private String confirmStatus;

}
