package com.trinasolar.scp.system.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 系统语言
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "Language保存参数", description = "保存参数" )
public class LanguageSaveDTO extends TokenDTO implements Serializable {

    /**
     * language_id
     */
    @ApiModelProperty(value = "language_id" )
    private Long languageId;
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码" )
    private String code;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
}
