package com.trinasolar.scp.system.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.Builder;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * dp电池类型关系
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-05 09:42:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "DpCellDTO对象", description = "DTO对象")
public class DpCellDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * dpId
     */
    @ApiModelProperty(value = "dpId")
    private String dpId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 类型 常规/特殊
     */
    @ApiModelProperty(value = "类型")
    private String type;
}
