package com.trinasolar.scp.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;


/**
 * 汇率表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-29 09:01:50
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "DailyRatesDTO对象", description = "DTO对象")
public class DailyRatesDTO {

    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 来源币种
     */
    @ApiModelProperty(value = "来源币种")
    private String fromCurrency;
    /**
     * 转换币种
     */
    @ApiModelProperty(value = "转换币种")
    private String toCurrency;
    /**
     * 汇率日期
     */
    @ApiModelProperty(value = "汇率日期")
    private LocalDateTime conversionDate;
    /**
     * 汇率类型
     */
    @ApiModelProperty(value = "汇率类型")
    private String conversionType;
    /**
     * 汇率
     */
    @ApiModelProperty(value = "汇率")
    private String conversionRate;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String statusCode;
    /**
     * rateSourceCode
     */
    @ApiModelProperty(value = "rateSourceCode")
    private String rateSourceCode;
}
