package com.trinasolar.scp.system.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 系统LOV多语言表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "LovTl保存参数", description = "保存参数" )
public class LovTlSaveDTO extends TokenDTO implements Serializable {

    /**
     * id
     */
    @ApiModelProperty(value = "id" )
    private Long id;
    /**
     * 目标主键ID
     */
    @ApiModelProperty(value = "目标主键ID" )
    private Long pkId;
    /**
     * 目标类型: header, config, line
     */
    @ApiModelProperty(value = "目标类型: header, config, line")
    private String pkType;
    /**
     * 语言
     */
    @ApiModelProperty(value = "语言")
    private String lang;

    @ApiModelProperty(value = "字段")
    private String lovField;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
}
