package com.trinasolar.scp.system.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 脚本维护
 * <AUTHOR>
 * @date 2022-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "SysScript保存参数", description = "保存参数")
public class SysScriptSaveDTO extends TokenDTO implements Serializable {
    /**
    *主键
    */
    @ApiModelProperty(value = "主键")
    private Long id ;

    /**
    *所属模块
    */
    @ApiModelProperty(value = "所属模块")
    @NotEmpty(message = "脚本所属模块不可为空" )
    private String scriptModule ;

    /**
    *脚本编码
    */
    @ApiModelProperty(value = "脚本编码")
    @NotEmpty(message = "脚本编码不可为空" )
    private String scriptCode ;

    /**
    *脚本名称
    */
    @ApiModelProperty(value = "脚本名称")
    @NotEmpty(message = "脚本名称不可为空" )
    private String scriptName ;

    /**
    *脚本内容
    */
    @ApiModelProperty(value = "脚本内容")
    @NotEmpty(message = "脚本内容不可为空" )
    private String scriptContent ;

    /**
    *备注
    */
    @ApiModelProperty(value = "备注")
    private String remark ;

}
