package com.trinasolar.scp.system.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * 合格供应商整车数或整柜数表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-09 07:56:06
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "合格供应商整车数或整柜数表DTO对象", description = "DTO对象")
public class ErpApprovalSupplierFixLotQtyDTO extends BaseDTO {

    private static final long serialVersionUID = -8400565444438364053L;

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;

    /**
     * 料号ID
     */
    @ApiModelProperty(value = "料号ID")
    private Long itemId;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String itemNum;

    /**
     * 料号描述
     */
    @ApiModelProperty(value = "料号描述")
    private String itemDescription;

    /**
     * 辅料类型
     */
    @ApiModelProperty(value = "辅料类型")
    private String categorySegment5;

    /**
     * 物料单位
     */
    @ApiModelProperty(value = "物料单位")
    private String priUom;

    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    private Long vendorId;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String vendorName;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外名称")
    private String isOverseaName;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    /**
     * 整车数或整柜数
     */
    @ApiModelProperty(value = "整车数或整柜数")
    private BigDecimal fixLotQty;

    /**
     * 起订量
     */
    @ApiModelProperty(value = "起订量")
    private BigDecimal minimumOrderQuantity;
}
