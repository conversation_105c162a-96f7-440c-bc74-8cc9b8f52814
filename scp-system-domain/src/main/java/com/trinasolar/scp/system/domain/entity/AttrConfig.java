package com.trinasolar.scp.system.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.time.LocalDateTime;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 系统扩展字段配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 13:58:46
 */
@Entity
@ToString
@Data
@Table(name = "sys_attr_config")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE sys_attr_config SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE sys_attr_config SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class AttrConfig extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 扩展字段列配置id
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "扩展字段列配置id")
    @Column(name = "id")
    private Long id;

    /**
     * 场景
     */
    @ApiModelProperty(value = "场景")
    @Column(name = "sence")
    private String sence;

    /**
     * 扩展字段头Id
     */
    @ApiModelProperty(value = "扩展字段头Id")
    @Column(name = "attr_header_id")
    private Long attrHeaderId;

    /**
     * 扩展字段名
     */
    @ApiModelProperty(value = "扩展字段名")
    @Column(name = "attr_col_name")
    private String attrColName;

    /**
     * 扩展字段展示名
     */
    @ApiModelProperty(value = "扩展字段展示名")
    @Column(name = "attr_col_title")
    private String attrColTitle;

    /**
     * 扩展字段别名
     */
    @ApiModelProperty(value = "扩展字段别名")
    @Column(name = "attr_col_alias")
    private String attrColAlias;

    /**
     * 扩展字段类型 1-文本 2-数字  3-值列表 4-日期
     */
    @ApiModelProperty(value = "扩展字段类型 1-文本 2-数字  3-值列表 4-日期")
    @Column(name = "attr_col_type_id")
    private Integer attrColTypeId;

    /**
     * 扩展字段数据来源
     */
    @ApiModelProperty(value = "扩展字段数据来源")
    @Column(name = "attr_col_data_source")
    private String attrColDataSource;

    /**
     * 扩展字段展示效果
     */
    @ApiModelProperty(value = "扩展字段展示效果")
    @Column(name = "attr_col_ui_type")
    private String attrColUiType;

    /**
     * LOV名称
     */
    @ApiModelProperty(value = "LOV名称")
    @Column(name = "attr_lov_name")
    private String attrLovName;

    /**
     * LOV筛选条件
     */
    @ApiModelProperty(value = "LOV筛选条件")
    @Column(name = "attr_lov_filter")
    private String attrLovFilter;

    /**
     * UI类型
     */
    @ApiModelProperty(value = "UI类型")
    @Column(name = "attr_ui_type")
    private String attrUiType;

    /**
     * 校验规则
     */
    @ApiModelProperty(value = "校验规则")
    @Column(name = "attr_rule")
    private String attrRule;

    /**
     * 属性默认值
     */
    @ApiModelProperty(value = "属性默认值")
    @Column(name = "attr_default_value")
    private String attrDefaultValue;

    /**
     * 扩展字段长度
     */
    @ApiModelProperty(value = "扩展字段长度")
    @Column(name = "attr_col_length")
    private Integer attrColLength;

    /**
     * 扩展字段宽度
     */
    @ApiModelProperty(value = "扩展字段宽度")
    @Column(name = "attr_col_width")
    private Integer attrColWidth;

    /**
     * 扩展字段排序号
     */
    @ApiModelProperty(value = "扩展字段排序号")
    @Column(name = "attr_col_seq_num")
    private Integer attrColSeqNum;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
    @Column(name = "enable_flag")
    private String enableFlag;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    @Column(name = "effective_start_date")
    private LocalDateTime effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    @Column(name = "effective_end_date")
    private LocalDateTime effectiveEndDate;

    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1")
    @Column(name = "attribute1")
    private String attribute1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2")
    @Column(name = "attribute2")
    private String attribute2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3")
    @Column(name = "attribute3")
    private String attribute3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty(value = "扩展字段4")
    @Column(name = "attribute4")
    private String attribute4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty(value = "扩展字段5")
    @Column(name = "attribute5")
    private String attribute5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty(value = "扩展字段6")
    @Column(name = "attribute6")
    private String attribute6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty(value = "扩展字段7")
    @Column(name = "attribute7")
    private String attribute7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty(value = "扩展字段8")
    @Column(name = "attribute8")
    private String attribute8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty(value = "扩展字段9")
    @Column(name = "attribute9")
    private String attribute9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty(value = "扩展字段10")
    @Column(name = "attribute10")
    private String attribute10;

    /**
     * 扩展字段11
     */
    @ApiModelProperty(value = "扩展字段11")
    @Column(name = "attribute11")
    private String attribute11;

    /**
     * 扩展字段12
     */
    @ApiModelProperty(value = "扩展字段12")
    @Column(name = "attribute12")
    private String attribute12;

    /**
     * 扩展字段13
     */
    @ApiModelProperty(value = "扩展字段13")
    @Column(name = "attribute13")
    private String attribute13;

    /**
     * 扩展字段14
     */
    @ApiModelProperty(value = "扩展字段14")
    @Column(name = "attribute14")
    private String attribute14;

    /**
     * 扩展字段15
     */
    @ApiModelProperty(value = "扩展字段15")
    @Column(name = "attribute15")
    private String attribute15;


}
