package com.trinasolar.scp.system.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 批准供应商
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-26 20:32:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ApprovalSupplier保存参数", description = "保存参数")
public class ApprovalSupplierSaveDTO extends TokenDTO implements Serializable {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 料号ID
     */
    @ApiModelProperty(value = "料号ID")
    private Long itemId;
    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String itemNum;
    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    private Long vendorId;
    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String vendorName;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 料号_第五分类
     */
    @ApiModelProperty(value = "料号_第五分类")
    private String categorySegment5;
}
