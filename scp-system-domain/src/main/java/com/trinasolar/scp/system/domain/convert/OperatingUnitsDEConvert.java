package com.trinasolar.scp.system.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.system.domain.entity.OperatingUnits;
import com.trinasolar.scp.system.domain.dto.OperatingUnitsDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 业务实体 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-07 17:43:44
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface OperatingUnitsDEConvert extends BaseDEConvert<OperatingUnitsDTO, OperatingUnits> {

    OperatingUnitsDEConvert INSTANCE = Mappers.getMapper(OperatingUnitsDEConvert.class);

}
