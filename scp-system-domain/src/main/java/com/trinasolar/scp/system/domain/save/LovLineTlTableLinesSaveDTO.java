package com.trinasolar.scp.system.domain.save;

import com.trinasolar.scp.system.domain.dto.LovLineTlTableLineDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * lov行表(SysLovLines)实体类
 *
 * <AUTHOR>
 * @since 2022-04-24 14:15:04
 */
@Data
@ToString
@ApiModel(value = "LovLineTlTableLinesSaveDTO(save)")
public class LovLineTlTableLinesSaveDTO implements Serializable {
    private static final long serialVersionUID = 388343368131252513L;

    @ApiModelProperty(value = "lovLineTlTableLines集合")
    List<LovLineTlTableLineDTO> lovLineTlTableLines;
}
