package com.trinasolar.scp.system.domain.save;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * lov行表(SysLovLines)实体类
 *
 * <AUTHOR>
 * @since 2022-04-24 14:15:04
 */
@Data
@ToString
@ApiModel(value = "LovLine(save)", description = "新增或修改LovLine")
public class LovLineSaveDTO implements Serializable {
    private static final long serialVersionUID = 388343368131252513L;

    @ApiModelProperty(value = "lov头id" )
    private Long lovHeaderId;

    /**
     * lov行id
     */
    @ApiModelProperty(value = "lov行id")
    private Long lovLineId;

    /**
     * lov名称
     */
    @ApiModelProperty(value = "lov名称")
    @NotNull(message = "lov名称 不能为空")
    private String lovName;

    /**
     * lov值
     */
    @ApiModelProperty(value = "lov值")
    @NotNull(message = "lov值 不能为空")
    private String lovValue;

    @ApiModelProperty(value = "列序号")
    private Integer colNo;
    /**
     * 父lov行id
     */
    @ApiModelProperty(value = "父lov行id")
    private Integer parentLovLineId;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
    @NotNull(message = "有效标识 不能为空")
    private String enableFlag;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDate effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDate effectiveEndDate;

    /**
     * 来源系统id
     */
    @ApiModelProperty(value = "来源系统id")
    private Integer sourceSystemId;

    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1")
    private String attribute1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2")
    private String attribute2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3")
    private String attribute3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty(value = "扩展字段4")
    private String attribute4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty(value = "扩展字段5")
    private String attribute5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty(value = "扩展字段6")
    private String attribute6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty(value = "扩展字段7")
    private String attribute7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty(value = "扩展字段8")
    private String attribute8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty(value = "扩展字段9")
    private String attribute9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty(value = "扩展字段10")
    private String attribute10;

    /**
     * 扩展字段11
     */
    @ApiModelProperty(value = "扩展字段11")
    private String attribute11;

    /**
     * 扩展字段12
     */
    @ApiModelProperty(value = "扩展字段12")
    private String attribute12;

    /**
     * 扩展字段13
     */
    @ApiModelProperty(value = "扩展字段13")
    private String attribute13;

    /**
     * 扩展字段14
     */
    @ApiModelProperty(value = "扩展字段14")
    private String attribute14;

    /**
     * 扩展字段15
     */
    @ApiModelProperty(value = "扩展字段15")
    private String attribute15;

}
