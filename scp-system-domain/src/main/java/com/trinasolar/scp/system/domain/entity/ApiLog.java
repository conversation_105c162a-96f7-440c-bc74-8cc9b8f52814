package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 推送数据至其他系统接口日志表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-18 09:26:45
 */
@Entity
@ToString
@Data
@Table(name = "sys_api_log")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE sys_api_log SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE sys_api_log SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ApiLog extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID")
    @Column(name = "id")
    private Long id;

    /**
     * 外部系统名称
     */
    @ApiModelProperty(value = "外部系统名称")
    @Column(name = "ext_system_name")
    private String extSystemName;

    /**
     * 业务数据单号
     */
    @ApiModelProperty(value = "业务数据单号")
    @Column(name = "business_no")
    private String businessNo;

    /**
     * 接口地址
     */
    @ApiModelProperty(value = "接口地址")
    @Column(name = "action")
    private String action;

    /**
     * 推送其他系统的参数
     */
    @ApiModelProperty(value = "推送其他系统的参数")
    @Column(name = "in_params")
    private String inParams;

    /**
     * 其他系统的返回结果
     */
    @ApiModelProperty(value = "其他系统的返回结果")
    @Column(name = "out_params")
    private String outParams;

    /**
     * 接口请求开始时间
     */
    @ApiModelProperty(value = "接口请求开始时间")
    @Column(name = "start_time")
    private LocalDateTime startTime;

    /**
     * 接口请求结束时间
     */
    @ApiModelProperty(value = "接口请求结束时间")
    @Column(name = "end_time")
    private LocalDateTime endTime;

    /**
     * 是否成功
     */
    @ApiModelProperty(value = "是否成功")
    @Column(name = "success")
    private String success;


}
