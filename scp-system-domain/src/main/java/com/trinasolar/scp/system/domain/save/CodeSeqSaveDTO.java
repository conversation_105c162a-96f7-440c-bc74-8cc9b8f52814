package com.trinasolar.scp.system.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 单据序列主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-09 15:05:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CodeSeq保存参数", description = "保存参数")
public class CodeSeqSaveDTO extends TokenDTO implements Serializable {

    /**
     * 单据序列表
     */
    @ApiModelProperty(value = "单据序列表")
    private Long id;
    /**
     * 序列代码
     */
    @ApiModelProperty(value = "序列代码")
    private String seqCode;
    /**
     * 序列名称
     */
    @ApiModelProperty(value = "序列名称")
    private String seqName;
    /**
     * 序列描述
     */
    @ApiModelProperty(value = "序列描述")
    private String seqDesc;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String isAvailable;
    /**
     * 重置频率，具体值存储在集值中, seq_reset_frequency
     */
    @ApiModelProperty(value = "重置频率，具体值存储在集值中, seq_reset_frequency")
    private String resetFrequency;
    /**
     * resetStartDate
     */
    @ApiModelProperty(value = "resetStartDate")
    private LocalDate resetStartDate;
    /**
     * resetEndDate
     */
    @ApiModelProperty(value = "resetEndDate")
    private LocalDateTime resetEndDate;
    /**
     * 序列值
     */
    @ApiModelProperty(value = "序列值")
    private Integer seqValue;
    /**
     * 编号值
     */
    @ApiModelProperty(value = "编号值")
    private String seqFullValue;
    /**
     * 序列重置日期
     */
    @ApiModelProperty(value = "序列重置日期")
    private LocalDate resetDate;
}
