package com.trinasolar.scp.system.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.system.domain.dto.AttrLineMapDTO;
import com.trinasolar.scp.system.domain.entity.AttrLineMap;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 属性行来源映射表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-05 19:21:39
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface AttrLineMapDEConvert extends BaseDEConvert<AttrLineMapDTO, AttrLineMap> {

    AttrLineMapDEConvert INSTANCE = Mappers.getMapper(AttrLineMapDEConvert.class);
}
