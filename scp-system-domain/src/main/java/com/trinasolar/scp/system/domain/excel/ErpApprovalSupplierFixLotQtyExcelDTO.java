package com.trinasolar.scp.system.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * 合格供应商整车数或整柜数表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-09 07:56:06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ErpApprovalSupplierFixLotQtyExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;

    /**
     * 料号ID
     */
    @ExcelProperty(value = "料号ID")
    @ExcelIgnore
    private Long itemId;

    /**
     * 料号
     */
    @ExcelProperty(value = "料号")
    private String itemNum;

    /**
     * 料号描述
     */
    @ExcelProperty(value = "料号描述")
    private String itemDescription;

    /**
     * 辅料类型
     */
    @ExcelProperty(value = "辅料类型")
    private String categorySegment5;

    /**
     * 物料单位
     */
    @ExcelProperty(value = "物料单位")
    private String priUom;

    /**
     * 供应商ID
     */
    @ExcelProperty(value = "供应商ID")
    @ExcelIgnore
    private Long vendorId;

    /**
     * 供应商
     */
    @ExcelProperty(value = "供应商")
    private String vendorName;

    /**
     * 国内海外
     */
    @ExcelIgnore
    private String isOversea;

    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内海外")
    private String isOverseaName;

    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;

    /**
     * 整车数或整柜数
     */
    @ExcelProperty(value = "整车数或整柜数")
    private BigDecimal fixLotQty;

    /**
     * 起订量
     */
    @ExcelProperty(value = "起订量")
    private BigDecimal minimumOrderQuantity;

}
