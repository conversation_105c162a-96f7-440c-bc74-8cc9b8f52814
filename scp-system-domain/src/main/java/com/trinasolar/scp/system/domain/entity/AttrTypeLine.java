package com.trinasolar.scp.system.domain.entity;


import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;


@Entity
@ToString
@Data
@Table(name = "sys_attr_type_lines")
//@Table(name = "sys_attr_type_lines_uat" )
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "update sys_attr_type_lines set is_deleted = 1 where attr_line_id = ?")
@SQLDeleteAll(sql = "update sys_attr_type_lines set is_deleted = 1 where attr_line_id = ?")
public class AttrTypeLine extends BasePO implements Serializable {
    /**
     * 属性类型id
     */
    @ApiModelProperty(value = "属性类型id" )
    private Long attrTypeHeaderId;

    /**
     * 属性行id
     */
    @ApiModelProperty(value = "属性行id")
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    private Long attrLineId;

    /**
     * 行编号
     */
    @ApiModelProperty(value = "行编号")
    @Column(name = "seq_num")
    private Integer colNo;

    /**
     * 属性中文名
     */
    @ApiModelProperty(value = "属性中文名")
    private String attrCnName;

    /**
     * 属性英文名
     */
    @ApiModelProperty(value = "属性英文名")
    private String attrEnName;

    /**
     * 属性值类型id 1-文本 2-数字  3-值列表 4-日期
     */
    @ApiModelProperty(value = "属性值类型id 1-文本 2-数字  3-值列表 4-日期")
    private Integer attrValueTypeId;

    /**
     * 属性描述
     */
    @ApiModelProperty(value = "属性描述")
    private String attrDesc;

    /**
     * 来源字段名
     */
    @ApiModelProperty(value = "来源字段名")
    private String sourceColumn;

    /**
     * 属性同步标识 来源字段不为空时可选为Y，Y-同步，N-不同步
     */
    @ApiModelProperty(value = "属性同步标识 来源字段不为空时可选为Y，Y-同步，N-不同步")
    private String attrSyncedFlag;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
    private String enableFlag;
    /**
     * Lov名称
     */
    @ApiModelProperty(value = "Lov名称")
    private String attrLovName;
    /**
     * lov筛选条件
     */
    @ApiModelProperty(value = "lov筛选条件")
    private String attrLovFilter;
    /**
     * UI类型
     */
    @ApiModelProperty(value = "UI类型")
    private String attrUiType;
    /**
     * 校验规则
     */
    @ApiModelProperty(value = "转换规则")
    private String attrRule;
    /**
     * 属性默认值
     */
    @ApiModelProperty(value = "属性默认值")
    private String attrDefaultValue;
    /**
     * 原始属性ID
     */
    @ApiModelProperty(value = "原始属性ID")
    private Long attrSourceAttrId;

    @Transient
    @ApiModelProperty(value = "原始属性类型名称")
    private String attrSourceHeaderName;

    @Transient
    @ApiModelProperty(value = "原始属性名称")
    private String attrSourceAttrName;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDate effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止" )
    private LocalDate effectiveEndDate;

    /**
     *
     */
    @ApiModelProperty(value = "" )
    private String attrCode;

    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1" )
    @Basic
    private String attribute1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2" )
    @Basic
    private String attribute2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3" )
    @Basic
    private String attribute3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty(value = "扩展字段4" )
    @Basic
    private String attribute4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty(value = "扩展字段5" )
    @Basic
    private String attribute5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty(value = "扩展字段6" )
    @Basic
    private String attribute6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty(value = "扩展字段7" )
    @Basic
    private String attribute7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty(value = "扩展字段8" )
    @Basic
    private String attribute8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty(value = "扩展字段9" )
    @Basic
    private String attribute9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty(value = "扩展字段10" )
    @Basic
    private String attribute10;

    /**
     * 扩展字段11
     */
    @ApiModelProperty(value = "扩展字段11" )
    @Basic
    private String attribute11;

    /**
     * 扩展字段12
     */
    @ApiModelProperty(value = "扩展字段12" )
    @Basic
    private String attribute12;

    /**
     * 扩展字段13
     */
    @ApiModelProperty(value = "扩展字段13" )
    @Basic
    private String attribute13;

    /**
     * 扩展字段14
     */
    @ApiModelProperty(value = "扩展字段14" )
    private String attribute14;

    /**
     * 扩展字段15
     */
    @ApiModelProperty(value = "校验规则")
    @Basic
    private String attribute15;
    private static final long serialVersionUID = 1L;
}

