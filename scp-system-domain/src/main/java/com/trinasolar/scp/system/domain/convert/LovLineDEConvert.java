package com.trinasolar.scp.system.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.system.domain.dto.LovLineDTO;
import com.trinasolar.scp.system.domain.entity.LovLine;
import com.trinasolar.scp.system.domain.save.LovLineSaveDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * LOV行来源映射表 DTO与实体转换器
 *
 * <AUTHOR>
 * @date 2022-07-07 09:21:39
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface LovLineDEConvert extends BaseDEConvert<LovLineDTO, LovLine> {

    LovLine toEntity(LovLineSaveDTO lovLineSaveDTO);

    List<com.trinasolar.scp.common.api.base.LovLineDTO> toCommonDto(List<LovLineDTO> lovLineDTOS);


    LovLineDEConvert INSTANCE = Mappers.getMapper(LovLineDEConvert.class);

    LovLineDTO toLocalDto(com.trinasolar.scp.common.api.base.LovLineDTO i);

    LovLineDTO deepCopy(LovLineDTO lovLineDTO);
}
