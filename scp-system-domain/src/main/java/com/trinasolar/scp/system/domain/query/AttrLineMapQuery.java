package com.trinasolar.scp.system.domain.query;

import lombok.Data;
import lombok.experimental.Accessors;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import com.trinasolar.scp.common.api.base.PageDTO;

import javax.validation.constraints.NotNull;

/**
 * 属性行来源映射表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-05 19:21:39
 */
@Data
@ApiModel(value = "AttrLineMap查询条件", description = "查询条件")
@Accessors(chain = true)
public class AttrLineMapQuery extends PageDTO implements Serializable {

    @NotNull(message = "属性行id不能为空")
    @ApiModelProperty(value = "属性行id")
    private Long attrLineId;
}
