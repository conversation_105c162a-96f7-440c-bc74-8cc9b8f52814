package com.trinasolar.scp.system.domain.dto;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * lov行表(SysLovLines)实体类
 *
 * <AUTHOR>
 * @since 2022-04-24 14:15:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class LovLinesDTO extends TokenDTO implements Serializable {
    @ApiModelProperty(value = "lov编码" )
    String code;

    @ApiModelProperty(value = "lov行" )
    List<LovLineDTO> lineDTOS;
}
