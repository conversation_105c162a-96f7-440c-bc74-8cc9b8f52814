package com.trinasolar.scp.system.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 系统LOV多语言表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "LovLineTlTableLineDTO对象", description = "DTO对象")
public class LovLineTlTableLineDTO extends BaseDTO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 目标主键ID
     */
    @ApiModelProperty(value = "目标主键ID")
    private Long pkId;
    /**
     * 目标类型: header, config, line
     */
    @ApiModelProperty(value = "目标类型: header, config, line")
    private String pkType;

    @ApiModelProperty(value = "字段")
    private String lovField;

    @ApiModelProperty(value = "lov字段名称")
    private String lovFieldName;

    @ApiModelProperty(value = "Lov字段值")
    private String lovFieldValue;
    /**
     * 语言
     */
    @ApiModelProperty(value = "语言")
    private String lang;
    @ApiModelProperty(value = "语言名称")
    private String langName;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
}
