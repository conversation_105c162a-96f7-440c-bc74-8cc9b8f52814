package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 单据序列主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-09 15:05:46
 */
@Entity
@ToString
@Data
@Table(name = "sys_code_seq")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE sys_code_seq SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE sys_code_seq SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class CodeSeq extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 单据序列表
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "单据序列表")
    @Column(name = "id")
    private Long id;

    /**
     * 序列代码
     */
    @ApiModelProperty(value = "序列代码")
    @Column(name = "seq_code")
    private String seqCode;

    /**
     * 序列名称
     */
    @ApiModelProperty(value = "序列名称")
    @Column(name = "seq_name")
    private String seqName;

    /**
     * 序列描述
     */
    @ApiModelProperty(value = "序列描述")
    @Column(name = "seq_desc")
    private String seqDesc;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    @Column(name = "is_available")
    private String isAvailable;

    /**
     * 重置频率，具体值存储在集值中, seq_reset_frequency
     */
    @ApiModelProperty(value = "重置频率，具体值存储在集值中, seq_reset_frequency")
    @Column(name = "reset_frequency")
    private String resetFrequency;

    /**
     * resetStartDate
     */
    @ApiModelProperty(value = "resetStartDate")
    @Column(name = "reset_start_date")
    private LocalDate resetStartDate;

    /**
     * resetEndDate
     */
    @ApiModelProperty(value = "resetEndDate")
    @Column(name = "reset_end_date")
    private LocalDateTime resetEndDate;

    /**
     * 序列值
     */
    @ApiModelProperty(value = "序列值")
    @Column(name = "seq_value")
    private Integer seqValue;

    /**
     * 编号值
     */
    @ApiModelProperty(value = "编号值")
    @Column(name = "seq_full_Value")
    private String seqFullValue;

    /**
     * 序列重置日期
     */
    @ApiModelProperty(value = "序列重置日期")
    @Column(name = "reset_date")
    private LocalDate resetDate;


}
