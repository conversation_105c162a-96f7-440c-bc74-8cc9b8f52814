package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;


@Entity
@ToString
@Data
@Table(name = "sys_attr_type_header")
//@Table(name = "sys_attr_type_header_uat" )
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "update sys_attr_type_header set is_deleted = 1 where attr_type_header_id = ?")
@SQLDeleteAll(sql = "update sys_attr_type_header set is_deleted = 1 where attr_type_header_id = ?")
public class AttrTypeHeader extends BasePO implements Serializable {
    /**
     * 属性类型id
     */
    @ApiModelProperty(value = "属性类型id" )
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig" )
    private Long attrTypeHeaderId;

    /**
     * 属性类型编码
     */
    @ApiModelProperty(value = "属性类型编码")
    private String attrTypeCode;

    /**
     * 属性类型中文名
     */
    @ApiModelProperty(value = "属性类型中文名")
    private String attrTypeCnName;

    /**
     * 属性类型英文名
     */
    @ApiModelProperty(value = "属性类型英文名")
    private String attrTypeEnName;

    /**
     * 属性类型描述
     */
    @ApiModelProperty(value = "属性类型描述")
    private String attrTypeDesc;

    /**
     * 属性所属分类 1-产品族 2-材料  3-组件 4-其他
     */
    @ApiModelProperty(value = "属性所属分类 1-产品族 2-材料  3-组件 4-其他")
    private Integer attrCategoryId;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
    private String enableFlag;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDate effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDate effectiveEndDate;


    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1" )
    @Basic
    private String attribute1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2" )
    @Basic
    private String attribute2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3" )
    @Basic
    private String attribute3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty(value = "扩展字段4" )
    @Basic
    private String attribute4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty(value = "扩展字段5" )
    @Basic
    private String attribute5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty(value = "扩展字段6" )
    @Basic
    private String attribute6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty(value = "扩展字段7" )
    @Basic
    private String attribute7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty(value = "扩展字段8" )
    @Basic
    private String attribute8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty(value = "扩展字段9" )
    @Basic
    private String attribute9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty(value = "扩展字段10" )
    @Basic
    private String attribute10;

    /**
     * 扩展字段11
     */
    @ApiModelProperty(value = "扩展字段11" )
    @Basic
    private String attribute11;

    /**
     * 扩展字段12
     */
    @ApiModelProperty(value = "扩展字段12" )
    @Basic
    private String attribute12;

    /**
     * 扩展字段13
     */
    @ApiModelProperty(value = "扩展字段13" )
    @Basic
    private String attribute13;

    /**
     * 扩展字段14
     */
    @ApiModelProperty(value = "扩展字段14" )
    private String attribute14;

    /**
     * 扩展字段15
     */
    @ApiModelProperty(value = "扩展字段15" )
    @Basic
    private String attribute15;

    private static final long serialVersionUID = 1L;
}

