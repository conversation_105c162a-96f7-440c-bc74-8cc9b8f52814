package com.trinasolar.scp.system.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class AttrTypeMapDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 属性类型id
     */
    @ApiModelProperty(value = "属性类型id")
    private Long attributeMapId;

    /**
     * 属性头ID
     */
    @ApiModelProperty(value = "属性头ID")
    private Long attributeHeaderId;

    /**
     * 来源系统id
     */
    @ApiModelProperty(value = "来源系统id")
    private Long sourceSystemId;

    /**
     * 来源表
     */
    @ApiModelProperty(value = "来源表")
    private String sourceTableName;

    /**
     * 分类字段1
     */
    @ApiModelProperty(value = "分类字段1")
    private String categorySegment1;

    /**
     * 分类字段2
     */
    @ApiModelProperty(value = "分类字段2")
    private String categorySegment2;

    /**
     * 分类字段3
     */
    @ApiModelProperty(value = "分类字段3")
    private String categorySegment3;

    /**
     * 分类字段4
     */
    @ApiModelProperty(value = "分类字段4")
    private String categorySegment4;

}

