package com.trinasolar.scp.system.domain.query;

import lombok.Data;
import lombok.experimental.Accessors;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 操作配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-01 09:45:41
 */
@Data
@ApiModel(value = "OperatorConfig查询条件", description = "查询条件")
@Accessors(chain = true)
public class OperatorConfigQuery extends PageDTO implements Serializable {
    /**
     * 功能名称
     */
    @ApiModelProperty(value = "功能名称")
    private String menuName;
    /**
     * 内容类型
     */
    @ApiModelProperty(value = "内容类型")
    private String contentType;
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * 控件编码
     */
    @ApiModelProperty(value = "控件编码")
    private String moduleCode;

    /**
     * 功能编码
     */
    @ApiModelProperty(value = "功能编码")
    private String menuCode;

}
