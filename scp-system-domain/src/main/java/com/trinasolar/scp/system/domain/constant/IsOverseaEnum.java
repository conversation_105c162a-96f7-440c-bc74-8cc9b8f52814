package com.trinasolar.scp.system.domain.constant;

import cn.hutool.core.util.EnumUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;

/**
 * 国内海外 inland/oversea
 *
 * <AUTHOR>
 * @date 2022/10/6
 */
@AllArgsConstructor
@Getter
public enum IsOverseaEnum {
    @ApiModelProperty("国内")
    INLAND("INLAND", "国内"),

    @ApiModelProperty("海外")
    OVERSEA("OVERSEA", "海外");

    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String desc;

    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(code).map(c -> EnumUtil.likeValueOf(IsOverseaEnum.class, c))
                .map(IsOverseaEnum::getDesc).orElse(null);
    }

    /**
     * 根据编码获取描述
     *
     * @param desc 描述
     * @return 描述
     */
    public static String getCode(String desc) {
        for (IsOverseaEnum value : values()) {
            if (value.desc.equals(desc)) {
                return value.code;
            }
        }
        return null;
    }
}
