package com.trinasolar.scp.system.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 单据序列规则定义表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-09 15:05:51
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CodeSeqRule保存参数", description = "保存参数")
public class CodeSeqRuleSaveDTO extends TokenDTO implements Serializable {

    /**
     * 规则主键
     */
    @ApiModelProperty(value = "规则主键")
    private Long id;
    /**
     * seqId
     */
    @ApiModelProperty(value = "seqId")
    private Long seqId;
    /**
     * 规则顺序号
     */
    @ApiModelProperty(value = "规则顺序号")
    private Integer ruleOrder;
    /**
     * 规则中段的类型，具体值存储在集值中fixedChar，paramCode，date，seq
     */
    @ApiModelProperty(value = "规则中段的类型，具体值存储在集值中fixedChar，paramCode，date，seq")
    private String ruleField;
    /**
     * 段值
     */
    @ApiModelProperty(value = "段值")
    private String ruleFieldValue;
    /**
     * 日期格式
     */
    @ApiModelProperty(value = "日期格式")
    private String datePattern;
    /**
     * 序列长度
     */
    @ApiModelProperty(value = "序列长度")
    private Integer seqLength;
    /**
     * 序列增量
     */
    @ApiModelProperty(value = "序列增量")
    private Integer seqIncrement;
    /**
     * 序列开始值
     */
    @ApiModelProperty(value = "序列开始值")
    private Integer seqStartValue;
}
