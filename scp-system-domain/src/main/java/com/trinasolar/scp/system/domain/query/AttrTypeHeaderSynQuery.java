package com.trinasolar.scp.system.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "AttrTypeHeaderSynQuery", description = "AttrTypeHeaderSynQuery查询条件")
public class AttrTypeHeaderSynQuery {

    @ApiModelProperty("类别id" )
    @NotNull
    Integer attrCategoryId;
    @ApiModelProperty(value = "上次更新时间" )
    @NotNull
    String lastUpdatedTime;
}
