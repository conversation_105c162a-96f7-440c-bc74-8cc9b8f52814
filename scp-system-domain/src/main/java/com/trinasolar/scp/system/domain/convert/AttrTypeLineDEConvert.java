package com.trinasolar.scp.system.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.system.domain.dto.AttrTypeLineDTO;
import com.trinasolar.scp.system.domain.dto.LovLineDTO;
import com.trinasolar.scp.system.domain.entity.AttrTypeLine;
import com.trinasolar.scp.system.domain.entity.LovLine;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 属性行来源映射表 DTO与实体转换器
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface AttrTypeLineDEConvert extends BaseDEConvert<AttrTypeLineDTO, AttrTypeLine> {

    AttrTypeLineDEConvert INSTANCE = Mappers.getMapper(AttrTypeLineDEConvert.class);
}
