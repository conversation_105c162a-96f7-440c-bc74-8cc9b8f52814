package com.trinasolar.scp.system.domain.save;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;


@Data
public class LovConfigSaveDTO implements Serializable {
    private static final long serialVersionUID = 12412281825113931L;

    /**
     * lov头id
     */
    @ApiModelProperty(value = "lov头id")
    @NotNull(message = "lov头id 不能为空")
    private Long lovHeaderId;

    /**
     * lov扩展字段列配置id
     */
    @ApiModelProperty(value = "lov扩展字段列配置id")
    private Long lovAttrColConId;

    /**
     * 扩展字段名
     */
    @ApiModelProperty(value = "扩展字段名")
    @NotNull(message = "扩展字段名 不能为空")
    private String attrColName;

    @ApiModelProperty(value = "扩展字段展示名")
    private String attrColTitle;
    /**
     * 扩展字段别名
     */
    @ApiModelProperty(value = "扩展字段别名")
    @NotNull(message = "扩展字段别名 不能为空")
    private String attrColAlias;

    /**
     * 扩展字段类型 1-文本 2-数字  3-值列表 4-日期
     */
    @ApiModelProperty(value = "扩展字段类型 1-文本 2-数字  3-值列表 4-日期")
    private Integer attrColTypeId;

    /**
     * 扩展字段数据来源
     */
    @ApiModelProperty(value = "扩展字段数据来源")
    private String attrColDataSource;

    /**
     * 扩展字段展示效果
     */
    @ApiModelProperty(value = "扩展字段展示效果")
    private String attrColUiType;

    @ApiModelProperty(value = "Lov名称")
    private String attrLovName;
    /**
     * lov筛选条件
     */
    @ApiModelProperty(value = "lov筛选条件")
    private String attrLovFilter;
    /**
     * UI类型
     */
    @ApiModelProperty(value = "UI类型")
    private String attrUiType;
    /**
     * 校验规则
     */
    @ApiModelProperty(value = "校验规则")
    private String attrRule;
    /**
     * 属性默认值
     */
    @ApiModelProperty(value = "属性默认值")
    private String attrDefaultValue;

    /**
     * 扩展字段长度
     */
    @ApiModelProperty(value = "扩展字段长度")
    private Integer attrColLength;

    /**
     * 扩展字段宽度
     */
    @ApiModelProperty(value = "扩展字段宽度")
    private Integer attrColWidth;

    /**
     * 扩展字段排序号
     */
    @ApiModelProperty(value = "扩展字段排序号")
    private Integer attrColSeqNum;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
    @NotNull(message = "有效标识 不能为空")
    private String enableFlag;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    @NotNull(message = "有效日期_起 不能为空")
    private LocalDate effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDate effectiveEndDate;

    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1")
    private String attribute1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2")
    private String attribute2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3")
    private String attribute3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty(value = "扩展字段4")
    private String attribute4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty(value = "扩展字段5")
    private String attribute5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty(value = "扩展字段6")
    private String attribute6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty(value = "扩展字段7")
    private String attribute7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty(value = "扩展字段8")
    private String attribute8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty(value = "扩展字段9")
    private String attribute9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty(value = "扩展字段10")
    private String attribute10;

    /**
     * 扩展字段11
     */
    @ApiModelProperty(value = "扩展字段11")
    private String attribute11;

    /**
     * 扩展字段12
     */
    @ApiModelProperty(value = "扩展字段12")
    private String attribute12;

    /**
     * 扩展字段13
     */
    @ApiModelProperty(value = "扩展字段13")
    private String attribute13;

    /**
     * 扩展字段14
     */
    @ApiModelProperty(value = "扩展字段14")
    private String attribute14;

    /**
     * 扩展字段15
     */
    @ApiModelProperty(value = "扩展字段15")
    private String attribute15;
}
