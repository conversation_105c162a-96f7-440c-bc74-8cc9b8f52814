package com.trinasolar.scp.system.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.Builder;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 操作配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-01 09:45:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "OperatorConfigDTO对象", description = "DTO对象")
public class OperatorConfigDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;
    /**
     * 功能编码
     */
    @ApiModelProperty(value = "功能编码")
    private String menuCode;
    /**
     * 功能名称
     */
    @ApiModelProperty(value = "功能名称")
    private String menuName;
    /**
     * 控件编码
     */
    @ApiModelProperty(value = "控件编码")
    private String moduleCode;
    /**
     * 内容类型
     */
    @ApiModelProperty(value = "内容类型")
    private String contentType;
    /**
     * 内容JSON
     */
    @ApiModelProperty(value = "内容JSON")
    private String content;
}
