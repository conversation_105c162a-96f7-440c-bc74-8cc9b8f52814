package com.trinasolar.scp.system.domain.dto;

import com.trinasolar.scp.system.domain.entity.LovLine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.Builder;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 数据权限表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-17 15:39:12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "DataPrivilegeDTO对象", description = "DTO对象")
public class DataPrivilegeDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 权限类型，一般大写为表名
     */
    @ApiModelProperty(value = "权限类型，一般大写为表名")
    private String privilegeType;
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;
    /**
     * 数据ID
     */
    @ApiModelProperty(value = "数据ID")
    private Long dataId;
    /**
     * 数据编码
     */
    @ApiModelProperty(value = "数据编码")
    private String dataCode;

    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1")
    private String attribute1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2")
    private String attribute2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3")
    private String attribute3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty(value = "扩展字段4")
    private String attribute4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty(value = "扩展字段5")
    private String attribute5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty(value = "扩展字段6")
    private String attribute6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty(value = "扩展字段7")
    private String attribute7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty(value = "扩展字段8")
    private String attribute8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty(value = "扩展字段9")
    private String attribute9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty(value = "扩展字段10")
    private String attribute10;

    /**
     * 扩展字段11
     */
    @ApiModelProperty(value = "扩展字段11")
    private String attribute11;

    /**
     * 扩展字段12
     */
    @ApiModelProperty(value = "扩展字段12")
    private String attribute12;

    /**
     * 扩展字段13
     */
    @ApiModelProperty(value = "扩展字段13")
    private String attribute13;

    /**
     * 扩展字段14
     */
    @ApiModelProperty(value = "扩展字段14")
    private String attribute14;

    /**
     * 扩展字段15
     */
    @ApiModelProperty(value = "扩展字段15")
    private String attribute15;

    public void copyData(LovLine lovLine) {
        this.setAttribute1(lovLine.getAttribute1());
        this.setAttribute2(lovLine.getAttribute2());
        this.setAttribute3(lovLine.getAttribute3());
        this.setAttribute4(lovLine.getAttribute4());
        this.setAttribute5(lovLine.getAttribute5());
        this.setAttribute6(lovLine.getAttribute6());
        this.setAttribute7(lovLine.getAttribute7());
        this.setAttribute8(lovLine.getAttribute8());
        this.setAttribute9(lovLine.getAttribute9());
        this.setAttribute10(lovLine.getAttribute10());
        this.setAttribute11(lovLine.getAttribute11());
        this.setAttribute12(lovLine.getAttribute12());
        this.setAttribute13(lovLine.getAttribute13());
        this.setAttribute14(lovLine.getAttribute14());
        this.setAttribute15(lovLine.getAttribute15());
    }
}
