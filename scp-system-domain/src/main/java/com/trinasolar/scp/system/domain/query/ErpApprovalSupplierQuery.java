package com.trinasolar.scp.system.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 批准供应商
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:05:46
 */
@Data
@ApiModel(value = "ApprovalSupplier查询条件", description = "查询条件")
@Accessors(chain = true)
public class ErpApprovalSupplierQuery extends PageDTO implements Serializable {
    @ApiModelProperty(value = "料号ID")
    private Long itemId;
    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String itemNum;

    @ApiModelProperty(value = "供应商ID")
    private Long vendorId;

    @ApiModelProperty(value = "供应商")
    private String vendorName;

    /**
     * 状态ID
     */
    @ApiModelProperty(value = "状态ID")
    private Long statusId;
    /**
     * 批准状态
     */
    @ApiModelProperty(value = "批准状态")
    private String aslStatus;

    /**
     * 料号_第五分类
     */
    @ApiModelProperty(value = "料号_第五分类")
    private List<String> categorySegment5;

    @ApiModelProperty(value = "确认状态")
    private String confirmStatus;

    /**
     * 导出列映射顺序对象
     */
    @ApiModelProperty(value = "导出列映射顺序对象")
    private String excelPara;

    @ApiModelProperty(value = "是否维护整车数: Y/N  null")
    private String hasPurchaseQuantity;
}
