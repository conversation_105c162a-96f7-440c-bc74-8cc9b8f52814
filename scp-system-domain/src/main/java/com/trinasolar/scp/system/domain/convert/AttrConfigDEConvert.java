package com.trinasolar.scp.system.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.system.domain.entity.AttrConfig;
import com.trinasolar.scp.system.domain.dto.AttrConfigDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 系统扩展字段配置 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 13:58:46
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface AttrConfigDEConvert extends BaseDEConvert<AttrConfigDTO, AttrConfig> {

    AttrConfigDEConvert INSTANCE = Mappers.getMapper(AttrConfigDEConvert.class);

}
