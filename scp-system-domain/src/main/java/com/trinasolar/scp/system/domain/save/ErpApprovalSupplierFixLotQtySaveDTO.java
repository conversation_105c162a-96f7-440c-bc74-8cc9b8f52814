package com.trinasolar.scp.system.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 合格供应商整车数或整柜数表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-09 07:56:06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpApprovalSupplierFixLotQty保存参数", description = "保存参数")
public class ErpApprovalSupplierFixLotQtySaveDTO extends TokenDTO implements Serializable {

    private static final long serialVersionUID = 3156079035732803195L;

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String itemNum;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String vendorName;

    @ApiModelProperty(value = "国内海外")
    private String isOversea;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    /**
     * 整车数或整柜数
     */
    @ApiModelProperty(value = "整车数或整柜数")
    private BigDecimal fixLotQty;

    /**
     * 起订量
     */
    @ApiModelProperty(value = "起订量")
    private BigDecimal minimumOrderQuantity;
}
