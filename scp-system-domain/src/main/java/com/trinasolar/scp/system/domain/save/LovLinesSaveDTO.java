package com.trinasolar.scp.system.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26
 */
@Data
@ApiModel(value = "LovLines(save)", description = "LovsLine" )
public class LovLinesSaveDTO extends TokenDTO implements Serializable {
    private static final long serialVersionUID = 312343368131252513L;

    /**
     * lov头id
     */
    @ApiModelProperty(value = "lov头id" )
    @NotNull(message = "lov头id 不可为空" )
    private Long lovHeaderId;

    @ApiModelProperty(value = "lov保存集合" )
    List<LovLineSaveDTO> lovLineSaveDTOS;
}
