package com.trinasolar.scp.system.domain.query;

import lombok.Data;
import lombok.experimental.Accessors;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;

/**
 * 汇率表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-29 09:01:50
 */
@Data
@ApiModel(value = "DailyRates查询条件", description = "查询条件")
@Accessors(chain = true)
public class DailyRatesQuery extends PageDTO implements Serializable {
    /**
     * 导出列映射顺序对象
     */
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
