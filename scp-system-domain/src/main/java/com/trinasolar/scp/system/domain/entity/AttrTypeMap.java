package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;


@Entity
@ToString
@Data
@Table(name = "sys_attr_type_map" )
@Where(clause = " is_deleted=0 " )
@SQLDelete(sql = "update sys_attr_type_map set is_deleted = 1 where attribute_map_id = ?" )
@SQLDeleteAll(sql = "update sys_attr_type_map set is_deleted = 1 where attribute_map_id = ?" )
public class AttrTypeMap extends BasePO implements Serializable {
    /**
     * 属性类型id
     */
    @ApiModelProperty(value = "属性类型id" )
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig" )
    private Long attributeMapId;

    /**
     * 属性头ID
     */
    @ApiModelProperty(value = "属性头ID")
    private Long attributeHeaderId;

    /**
     * 来源系统id
     */
    @ApiModelProperty(value = "来源系统id")
    private Long sourceSystemId;

    /**
     * 来源表
     */
    @ApiModelProperty(value = "来源表")
    private String sourceTableName;

    /**
     * 分类字段1
     */
    @ApiModelProperty(value = "分类字段1")
    private String categorySegment1;

    /**
     * 分类字段2
     */
    @ApiModelProperty(value = "分类字段2")
    private String categorySegment2;

    /**
     * 分类字段3
     */
    @ApiModelProperty(value = "分类字段3")
    private String categorySegment3;

    /**
     * 分类字段4
     */
    @ApiModelProperty(value = "分类字段4")
    private String categorySegment4;

    private static final long serialVersionUID = 1L;
}

