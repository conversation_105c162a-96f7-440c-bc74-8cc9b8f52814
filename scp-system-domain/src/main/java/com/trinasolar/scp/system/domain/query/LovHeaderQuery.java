package com.trinasolar.scp.system.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/4/25
 */
@Data
@ApiModel(value = "LovHeaderQuery", description = "LovHeaderQuery查询条件")
public class LovHeaderQuery extends PageDTO {
    /**
     * LovHeadCode
     */
    @ApiModelProperty("code")
    String code;

    @ApiModelProperty("name;")
    String name;

    @ApiModelProperty("enableFlag")
    String enableFlag;

    @ApiModelProperty("lovCategoryId")
    Integer lovCategoryId;
}
