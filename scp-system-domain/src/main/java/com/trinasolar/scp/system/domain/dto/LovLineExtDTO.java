package com.trinasolar.scp.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * lov行表(SysLovLines)实体类
 *
 * <AUTHOR>
 * @since 2022-04-24 14:15:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "LovLineDTO", description = "LovLineDTO")
public class LovLineExtDTO extends LovLineDTO implements Serializable {
    private static final long serialVersionUID = 388343368137252563L;
    /**
     * 扩展字段名称1
     */
    @ApiModelProperty(value = "扩展字段名称1")
    private String attribute1Name;

    /**
     * 扩展字段名称2
     */
    @ApiModelProperty(value = "扩展字段名称2")
    private String attribute2Name;

    /**
     * 扩展字段名称3
     */
    @ApiModelProperty(value = "扩展字段名称3")
    private String attribute3Name;

    /**
     * 扩展字段名称4
     */
    @ApiModelProperty(value = "扩展字段名称4")
    private String attribute4Name;

    /**
     * 扩展字段名称5
     */
    @ApiModelProperty(value = "扩展字段名称5")
    private String attribute5Name;

    /**
     * 扩展字段名称6
     */
    @ApiModelProperty(value = "扩展字段名称6")
    private String attribute6Name;

    /**
     * 扩展字段名称7
     */
    @ApiModelProperty(value = "扩展字段名称7")
    private String attribute7Name;

    /**
     * 扩展字段名称8
     */
    @ApiModelProperty(value = "扩展字段名称8")
    private String attribute8Name;

    /**
     * 扩展字段名称9
     */
    @ApiModelProperty(value = "扩展字段名称9")
    private String attribute9Name;

    /**
     * 扩展字段名称10
     */
    @ApiModelProperty(value = "扩展字段名称10")
    private String attribute10Name;

    /**
     * 扩展字段名称11
     */
    @ApiModelProperty(value = "扩展字段名称11")
    private String attribute11Name;

    /**
     * 扩展字段名称12
     */
    @ApiModelProperty(value = "扩展字段名称12")
    private String attribute12Name;

    /**
     * 扩展字段名称13
     */
    @ApiModelProperty(value = "扩展字段名称13")
    private String attribute13Name;

    /**
     * 扩展字段名称14
     */
    @ApiModelProperty(value = "扩展字段名称14")
    private String attribute14Name;

    /**
     * 扩展字段名称15
     */
    @ApiModelProperty(value = "扩展字段名称15")
    private String attribute15Name;
}
