package com.trinasolar.scp.system.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 属性行来源映射表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-05 19:21:39
 */
@Entity
@ToString
@Data
@Table(name = "sys_attr_line_map")
//@Table(name = "sys_attr_line_map_uat")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE sys_attr_line_map SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE sys_attr_line_map SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class AttrLineMap extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 属性行id
     */
    @ApiModelProperty(value = "属性行id")
    @Column(name = "attr_line_id")
    private Long attrLineId;

    /**
     * 来源属性ID
     */
    @ApiModelProperty(value = "来源属性ID")
    @Column(name = "src_attr_id")
    private String srcAttrId;

    /**
     * 来源属性值
     */
    @ApiModelProperty(value = "来源属性值")
    @Column(name = "src_attr_value")
    private String srcAttrValue;

    @ApiModelProperty(value = "源系统物料4级分类ID")
    @Column(name = "src_category_segment4_id")
    private String srcCategorySegment4Id;

    @ApiModelProperty(value = "源系统物料4级分类")
    @Column(name = "src_category_segment4")
    private String srcCategorySegment4;

    @ApiModelProperty(value = "来源字段")
    @Column(name = "src_attr_column")
    private String srcAttrColumn;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
    @Column(name = "enable_flag")
    private String enableFlag;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    @Column(name = "effective_start_date")
    private LocalDateTime effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    @Column(name = "effective_end_date")
    private LocalDateTime effectiveEndDate;

    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1")
    @Column(name = "attribute1")
    private String attribute1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2")
    @Column(name = "attribute2")
    private String attribute2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3")
    @Column(name = "attribute3")
    private String attribute3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty(value = "扩展字段4")
    @Column(name = "attribute4")
    private String attribute4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty(value = "扩展字段5")
    @Column(name = "attribute5")
    private String attribute5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty(value = "扩展字段6")
    @Column(name = "attribute6")
    private String attribute6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty(value = "扩展字段7")
    @Column(name = "attribute7")
    private String attribute7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty(value = "扩展字段8")
    @Column(name = "attribute8")
    private String attribute8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty(value = "扩展字段9")
    @Column(name = "attribute9")
    private String attribute9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty(value = "扩展字段10")
    @Column(name = "attribute10")
    private String attribute10;

    /**
     * 扩展字段11
     */
    @ApiModelProperty(value = "扩展字段11")
    @Column(name = "attribute11")
    private String attribute11;

    /**
     * 扩展字段12
     */
    @ApiModelProperty(value = "扩展字段12")
    @Column(name = "attribute12")
    private String attribute12;

    /**
     * 扩展字段13
     */
    @ApiModelProperty(value = "扩展字段13")
    @Column(name = "attribute13")
    private String attribute13;

    /**
     * 扩展字段14
     */
    @ApiModelProperty(value = "扩展字段14")
    @Column(name = "attribute14")
    private String attribute14;

    /**
     * 扩展字段15
     */
    @ApiModelProperty(value = "扩展字段15")
    @Column(name = "attribute15")
    private String attribute15;

    /**
     * 属性排序号
     */
    @ApiModelProperty(value = "属性排序号")
    @Column(name = "seq_num")
    private Integer seqNum;


}
