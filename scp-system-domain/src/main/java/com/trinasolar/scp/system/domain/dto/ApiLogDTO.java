package com.trinasolar.scp.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;


/**
 * 推送数据至其他系统接口日志表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-18 09:26:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ApiLogDTO对象", description = "DTO对象")
public class ApiLogDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;
    /**
     * 外部系统名称
     */
    @ApiModelProperty(value = "外部系统名称")
    private String extSystemName;
    /**
     * 业务数据单号
     */
    @ApiModelProperty(value = "业务数据单号")
    private String businessNo;
    /**
     * 接口地址
     */
    @ApiModelProperty(value = "接口地址")
    private String action;
    /**
     * 推送其他系统的参数
     */
    @ApiModelProperty(value = "推送其他系统的参数")
    private String inParams;
    /**
     * 其他系统的返回结果
     */
    @ApiModelProperty(value = "其他系统的返回结果")
    private String outParams;
    /**
     * 接口请求开始时间
     */
    @ApiModelProperty(value = "接口请求开始时间")
    private LocalDateTime startTime;
    /**
     * 接口请求结束时间
     */
    @ApiModelProperty(value = "接口请求结束时间")
    private LocalDateTime endTime;
    /**
     * 是否成功
     */
    @ApiModelProperty(value = "是否成功")
    private String success;
}
