package com.trinasolar.scp.system.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 合格供应商整车数或整柜数表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-09 07:56:06
 */
@Data
@ApiModel(value = "ErpApprovalSupplierFixLotQty查询条件", description = "查询条件")
@Accessors(chain = true)
public class ErpApprovalSupplierFixLotQtyQuery extends PageDTO implements Serializable {

    private static final long serialVersionUID = 4404261795749226970L;

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;

    /**
     * 料号ID
     */
    @ApiModelProperty(value = "料号ID")
    private Long itemId;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String itemNum;

    /**
     * 料号描述
     */
    @ApiModelProperty(value = "料号描述")
    private String itemDescription;

    /**
     * 辅料类型
     */
    @ApiModelProperty(value = "辅料类型")
    private String categorySegment5;

    /**
     * 物料单位
     */
    @ApiModelProperty(value = "物料单位")
    private String priUom;

    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    private Long vendorId;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String vendorName;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    /**
     * 整车数或整柜数
     */
    @ApiModelProperty(value = "整车数或整柜数")
    private BigDecimal fixLotQty;
}
