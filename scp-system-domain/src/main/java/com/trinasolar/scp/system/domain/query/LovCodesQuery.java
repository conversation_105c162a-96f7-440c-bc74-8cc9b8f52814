package com.trinasolar.scp.system.domain.query;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/25
 */
@Data
@ApiModel(value = "LovQuery", description = "Lov查询条件多个Code" )
public class LovCodesQuery extends TokenDTO {
    @ApiModelProperty("多lov查询条件" )
    List<LovLineQuery> lovQueries;
}
