package com.trinasolar.scp.system.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 操作配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-01 09:45:41
 */
@Entity
@ToString
@Data
@Table(name = "sys_operator_config")
@SQLDelete(sql = "UPDATE sys_operator_config SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE sys_operator_config SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class OperatorConfig extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @Column(name = "user_id")
    private String userId;

    /**
     * 功能编码
     */
    @ApiModelProperty(value = "功能编码")
    @Column(name = "menu_code")
    private String menuCode;

    /**
     * 功能名称
     */
    @ApiModelProperty(value = "功能名称")
    @Column(name = "menu_name")
    private String menuName;

    /**
     * 控件编码
     */
    @ApiModelProperty(value = "控件编码")
    @Column(name = "module_code")
    private String moduleCode;

    /**
     * 内容类型
     */
    @ApiModelProperty(value = "内容类型")
    @Column(name = "content_type")
    private String contentType;

    /**
     * 内容JSON
     */
    @ApiModelProperty(value = "内容JSON")
    @Column(name = "content")
    private String content;


}
