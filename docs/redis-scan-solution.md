# Redis SCAN 方法替代 KEYS 解决方案

## 问题背景

原始代码使用 `KEYS` 命令查找匹配的 Redis 键：
```java
Set<String> keys = stringRedisTemplate.keys(LovUtils.getRedisRealKey(lovHeaderCode) + "*");
```

这种方式存在严重的性能问题，特别是在生产环境中。

## SCAN 方法优势

### 1. 非阻塞性
- **KEYS**: 会阻塞 Redis 服务器，直到扫描完所有键
- **SCAN**: 增量迭代，不会阻塞服务器

### 2. 性能影响
- **KEYS**: O(N) 时间复杂度，N 是数据库中键的总数
- **SCAN**: 每次迭代的时间复杂度是 O(1)，总体仍是 O(N) 但分摊到多次调用

### 3. 生产环境友好
- **KEYS**: 许多生产环境禁用此命令
- **SCAN**: 生产环境推荐使用

## 实现方案

### 核心实现

```java
@Override
public void clearLovHeaderCache(String lovHeaderCode) {
    if (StringUtils.isBlank(lovHeaderCode)) {
        return;
    }
    // 加分布式锁
    RLock lock = redissonClient.getLock(getLovHeaderUpdKey(lovHeaderCode));
    lock.lock();
    try {
        // 使用 SCAN 替代 KEYS 命令，避免阻塞 Redis
        Set<String> keysToDelete = new HashSet<>();
        
        // 扫描所有可能的键模式
        keysToDelete.addAll(scanKeys("*" + lovHeaderCode + "*"));
        keysToDelete.addAll(scanKeys("SCP_LOVLINE:" + lovHeaderCode + ":*"));
        
        // 批量删除找到的键
        if (!keysToDelete.isEmpty()) {
            stringRedisTemplate.delete(keysToDelete);
            log.info("使用SCAN清除LOV缓存: {} 个键被删除, headerCode: {}, 键列表: {}", 
                    keysToDelete.size(), lovHeaderCode, keysToDelete);
        } else {
            log.info("使用SCAN清除LOV缓存: 未找到相关键, headerCode: {}", lovHeaderCode);
        }
    } finally {
        lock.unlock();
    }
}

/**
 * 使用 SCAN 命令查找匹配的键（替代 KEYS 命令）
 * @param pattern 键的匹配模式
 * @return 匹配的键集合
 */
private Set<String> scanKeys(String pattern) {
    Set<String> keys = new HashSet<>();
    try {
        ScanOptions options = ScanOptions.scanOptions()
                .match(pattern)
                .count(100) // 每次扫描的数量，可以根据实际情况调整
                .build();
        
        try (Cursor<String> cursor = stringRedisTemplate.scan(options)) {
            while (cursor.hasNext()) {
                keys.add(cursor.next());
            }
        }
    } catch (Exception e) {
        log.error("SCAN 命令执行失败，pattern: {}, error: {}", pattern, e.getMessage());
    }
    return keys;
}
```

### 扫描模式

对于 HeaderCode = "Channel"，会扫描以下模式：

1. `*Channel*` - 匹配所有包含 "Channel" 的键
2. `SCP_LOVLINE:Channel:*` - 匹配特定格式的多语言键

### 实际匹配的键示例

```
*Channel* 模式匹配：
- lov:id:Channel
- lov:value:Channel  
- lov:whole:Channel
- some_other_Channel_key

SCP_LOVLINE:Channel:* 模式匹配：
- SCP_LOVLINE:Channel:zh_CN:V
- SCP_LOVLINE:Channel:zh_CN:ALL
- SCP_LOVLINE:Channel:en_US:V
- SCP_LOVLINE:Channel:en_US:ALL
- SCP_LOVLINE:Channel:V
- SCP_LOVLINE:Channel:ALL
```

## 配置参数

### ScanOptions 配置

```java
ScanOptions options = ScanOptions.scanOptions()
        .match(pattern)      // 匹配模式
        .count(100)          // 每次迭代返回的键数量建议值
        .build();
```

### count 参数调优

- **小值 (10-50)**: 更频繁的网络往返，但每次迭代更快
- **中值 (100-500)**: 平衡性能和网络开销（推荐）
- **大值 (1000+)**: 减少网络往返，但单次迭代可能较慢

## 性能对比

| 指标 | KEYS 命令 | SCAN 命令 |
|------|-----------|-----------|
| 阻塞性 | ❌ 阻塞服务器 | ✅ 非阻塞 |
| 内存使用 | ❌ 一次性加载所有键 | ✅ 增量加载 |
| 网络开销 | ✅ 单次请求 | ⚠️ 多次请求 |
| 生产环境 | ❌ 不推荐 | ✅ 推荐 |
| 实现复杂度 | ✅ 简单 | ⚠️ 稍复杂 |

## 注意事项

### 1. 重复键处理
SCAN 可能返回重复的键，使用 `Set<String>` 自动去重。

### 2. 键的变化
在 SCAN 过程中，如果键被添加或删除，可能会：
- 遗漏新添加的键
- 返回已删除的键
- 返回重复的键

### 3. 模式匹配
SCAN 的模式匹配在服务器端进行，比客户端过滤更高效。

### 4. 异常处理
实现了完善的异常处理，确保 SCAN 失败时不会影响系统稳定性。

## 测试验证

```java
@Test
void testClearLovHeaderCache_WithScan_ShouldDeleteFoundKeys() {
    // 模拟 SCAN 返回的键
    Set<String> mockKeys = new HashSet<>(Arrays.asList(
            "SCP_LOVLINE:Channel:zh_CN:V",
            "SCP_LOVLINE:Channel:zh_CN:ALL"
    ));

    // 模拟 cursor 行为
    when(cursor.hasNext()).thenReturn(true, true, false);
    when(cursor.next())
            .thenReturn("SCP_LOVLINE:Channel:zh_CN:V")
            .thenReturn("SCP_LOVLINE:Channel:zh_CN:ALL");

    when(stringRedisTemplate.scan(any(ScanOptions.class))).thenReturn(cursor);

    // 执行测试
    lovCacheService.clearLovHeaderCache("Channel");

    // 验证 SCAN 被调用
    verify(stringRedisTemplate, times(2)).scan(any(ScanOptions.class));
    verify(stringRedisTemplate).delete(any(Set.class));
}
```

## 部署建议

1. **监控**: 部署后监控 Redis 性能指标，确认不再有阻塞问题
2. **日志**: 观察日志中删除的键数量，确保功能正常
3. **测试**: 在测试环境充分验证各种场景
4. **回退**: 保留原始实现作为紧急回退方案

## 总结

使用 SCAN 替代 KEYS 是一个重要的性能优化，特别适合生产环境。虽然实现稍微复杂，但带来的性能和稳定性提升是值得的。
