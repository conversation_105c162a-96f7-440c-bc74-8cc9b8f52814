# Redis Keys 方法优化方案

## 问题描述

在 `LovCacheServiceImpl.clearLovHeaderCache()` 方法中，原来使用了 `stringRedisTemplate.keys()` 方法来查找匹配的键：

```java
Set<String> keys = stringRedisTemplate.keys(LovUtils.getRedisRealKey(lovHeaderCode) + "*");
```

这种做法存在以下问题：

1. **性能问题**: `KEYS` 命令会阻塞 Redis 服务器，在生产环境中可能导致严重的性能问题
2. **扩展性问题**: 当 Redis 中键的数量很大时，`KEYS` 命令的执行时间会显著增加
3. **生产环境限制**: 许多生产环境的 Redis 配置都禁用了 `KEYS` 命令

## 解决方案

### 方案1：明确删除已知键类型（已实现）

根据代码分析，LOV 缓存主要包含以下几种类型的键：

1. **ID键**: `LovUtils.getRedisIdKey(type)` - Hash结构，存储ID到JSON的映射
2. **Value键**: `LovUtils.getRedisValueKey(type)` - Hash结构，存储Value到JSON的映射  
3. **整体缓存键**: `LovUtils.getWholeLovCacheKey(headerCode, withTranslateKey)` - List结构
4. **名称缓存键**: `LovUtils.getWholeNameLovCacheKey(headerCode)` - Hash结构

新的实现方式：

```java
@Override
public void clearLovHeaderCache(String lovHeaderCode) {
    if (StringUtils.isBlank(lovHeaderCode)) {
        return;
    }
    // 加分布式锁
    RLock lock = redissonClient.getLock(getLovHeaderUpdKey(lovHeaderCode));
    lock.lock();
    try {
        // 替代keys方法：明确删除已知的键类型，避免使用keys命令
        List<String> keysToDelete = new ArrayList<>();
        
        // 1. 删除ID键 (Hash结构)
        String idKey = LovUtils.getRedisIdKey(lovHeaderCode);
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(idKey))) {
            keysToDelete.add(idKey);
        }
        
        // 2. 删除Value键 (Hash结构)  
        String valueKey = LovUtils.getRedisValueKey(lovHeaderCode);
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(valueKey))) {
            keysToDelete.add(valueKey);
        }
        
        // 3. 删除整体缓存键 (List结构) - 包含翻译和不含翻译两种
        String wholeCacheKey = LovUtils.getWholeLovCacheKey(lovHeaderCode, true);
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(wholeCacheKey))) {
            keysToDelete.add(wholeCacheKey);
        }
        
        String wholeCacheKeyNoTranslate = LovUtils.getWholeLovCacheKey(lovHeaderCode, false);
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(wholeCacheKeyNoTranslate))) {
            keysToDelete.add(wholeCacheKeyNoTranslate);
        }
        
        // 4. 删除名称缓存键 (Hash结构)
        String nameCacheKey = LovUtils.getWholeNameLovCacheKey(lovHeaderCode);
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(nameCacheKey))) {
            keysToDelete.add(nameCacheKey);
        }
        
        // 批量删除所有相关键
        if (!keysToDelete.isEmpty()) {
            stringRedisTemplate.delete(keysToDelete);
        }
    } finally {
        lock.unlock();
    }
}
```

### 方案2：使用 SCAN 命令（备选方案）

如果需要更灵活的键查找，可以使用 `SCAN` 命令替代 `KEYS`：

```java
// 使用 SCAN 命令的示例（未实现）
public Set<String> scanKeys(String pattern) {
    Set<String> keys = new HashSet<>();
    ScanOptions options = ScanOptions.scanOptions().match(pattern).count(100).build();
    
    try (Cursor<String> cursor = stringRedisTemplate.scan(options)) {
        while (cursor.hasNext()) {
            keys.add(cursor.next());
        }
    }
    return keys;
}
```

## 优势对比

| 方案 | 优势 | 劣势 |
|------|------|------|
| 明确删除已知键类型 | 1. 性能最优<br>2. 不会阻塞Redis<br>3. 代码清晰明确 | 1. 需要明确知道所有键类型<br>2. 新增键类型时需要更新代码 |
| SCAN 命令 | 1. 不会阻塞Redis<br>2. 支持模式匹配<br>3. 更灵活 | 1. 性能略低于明确删除<br>2. 代码复杂度较高 |
| KEYS 命令（原方案） | 1. 代码简单<br>2. 支持模式匹配 | 1. 会阻塞Redis<br>2. 性能问题<br>3. 生产环境不推荐 |

## 测试验证

已创建单元测试 `LovCacheServiceImplTest` 来验证新实现的正确性：

- 测试正常情况下的键删除
- 测试空白 headerCode 的处理
- 测试不存在键的情况

## 建议

1. **推荐使用方案1**（明确删除已知键类型），因为它性能最优且符合当前的缓存结构
2. **定期审查缓存键结构**，确保删除逻辑包含所有相关键类型
3. **监控缓存清理效果**，确保没有遗漏的键导致内存泄漏
4. **考虑添加缓存键的命名规范**，便于后续维护

## 注意事项

1. 修改后需要充分测试，确保所有相关的缓存都能正确清理
2. 如果后续新增了其他类型的LOV缓存键，需要同步更新 `clearLovHeaderCache` 方法
3. 建议在生产环境部署前进行压力测试，验证性能改进效果
