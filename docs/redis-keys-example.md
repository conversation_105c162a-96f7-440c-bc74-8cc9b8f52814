# Redis Keys 优化示例

## 实际键名模式分析

根据您提供的实际键名：
```
SCP_LOVLINE:Channel:zh_CN:V
SCP_LOVLINE:Channel:zh_CN:ALL
```

键名格式为：`SCP_LOVLINE:HeaderCode:语言代码:类型`

## 优化前后对比

### 优化前（使用 KEYS 命令）
```java
// 危险的实现 - 会阻塞 Redis
Set<String> keys = stringRedisTemplate.keys(LovUtils.getRedisRealKey(lovHeaderCode) + "*");
if (CollectionUtils.isNotEmpty(keys)) {
    stringRedisTemplate.delete(keys);
}
```

### 优化后（明确删除已知键类型）
```java
// 安全的实现 - 不会阻塞 Redis
List<String> keysToDelete = new ArrayList<>();

// 获取系统支持的所有语言
List<String> supportedLanguages = getSupportedLanguages(); // ["zh_CN", "en_US"]

// 删除多语言键
for (String lang : supportedLanguages) {
    // SCP_LOVLINE:Channel:zh_CN:V
    String multiLangValueKey = "SCP_LOVLINE:" + lovHeaderCode + ":" + lang + ":V";
    if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(multiLangValueKey))) {
        keysToDelete.add(multiLangValueKey);
    }
    
    // SCP_LOVLINE:Channel:zh_CN:ALL
    String multiLangAllKey = "SCP_LOVLINE:" + lovHeaderCode + ":" + lang + ":ALL";
    if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(multiLangAllKey))) {
        keysToDelete.add(multiLangAllKey);
    }
}

// 删除基础键（不带语言代码）
String baseValueKey = "SCP_LOVLINE:" + lovHeaderCode + ":V";
if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(baseValueKey))) {
    keysToDelete.add(baseValueKey);
}

// 批量删除
if (!keysToDelete.isEmpty()) {
    stringRedisTemplate.delete(keysToDelete);
}
```

## 完整的键删除列表

对于 HeaderCode = "Channel"，系统会检查并删除以下键：

### 1. LovUtils 生成的键
- `LovUtils.getRedisIdKey("Channel")` 
- `LovUtils.getRedisValueKey("Channel")`
- `LovUtils.getWholeLovCacheKey("Channel", true)`
- `LovUtils.getWholeLovCacheKey("Channel", false)`
- `LovUtils.getWholeNameLovCacheKey("Channel")`

### 2. 多语言键（基于实际格式）
- `SCP_LOVLINE:Channel:zh_CN:V`
- `SCP_LOVLINE:Channel:zh_CN:ALL`
- `SCP_LOVLINE:Channel:zh_CN:ID`
- `SCP_LOVLINE:Channel:en_US:V`
- `SCP_LOVLINE:Channel:en_US:ALL`
- `SCP_LOVLINE:Channel:en_US:ID`

### 3. 基础键（不带语言代码）
- `SCP_LOVLINE:Channel:V`
- `SCP_LOVLINE:Channel:ALL`
- `SCP_LOVLINE:Channel:ID`

## 性能对比

| 方案 | Redis 阻塞 | 性能 | 安全性 | 维护性 |
|------|------------|------|--------|--------|
| KEYS 命令 | ❌ 会阻塞 | ❌ 差 | ❌ 低 | ✅ 简单 |
| 明确删除 | ✅ 不阻塞 | ✅ 好 | ✅ 高 | ⚠️ 需维护键列表 |

## 测试验证

```java
@Test
void testClearLovHeaderCache_ShouldDeleteAllRelatedKeys() {
    // 模拟所有键都存在
    when(stringRedisTemplate.hasKey(anyString())).thenReturn(true);
    
    // 执行清理
    lovCacheService.clearLovHeaderCache("Channel");
    
    // 验证所有相关键都被检查
    verify(stringRedisTemplate).hasKey("SCP_LOVLINE:Channel:zh_CN:V");
    verify(stringRedisTemplate).hasKey("SCP_LOVLINE:Channel:zh_CN:ALL");
    verify(stringRedisTemplate).hasKey("SCP_LOVLINE:Channel:en_US:V");
    verify(stringRedisTemplate).hasKey("SCP_LOVLINE:Channel:en_US:ALL");
    
    // 验证批量删除被调用
    verify(stringRedisTemplate).delete(any(List.class));
}
```

## 注意事项

1. **新增语言支持**: 当系统新增语言时，会自动从数据库获取语言列表
2. **新增键类型**: 如果发现新的键类型（如 `:ID`），需要在代码中添加相应的删除逻辑
3. **监控**: 建议添加日志记录删除的键数量，便于监控缓存清理效果
4. **回退方案**: 如果发现遗漏的键，可以临时使用 SCAN 命令作为补充

## 部署建议

1. **测试环境验证**: 先在测试环境验证所有相关键都能正确删除
2. **监控部署**: 部署后监控缓存命中率，确保没有遗漏的键导致脏数据
3. **性能监控**: 监控 Redis 性能指标，验证优化效果
4. **逐步部署**: 建议先在低峰期部署，观察效果后再全面推广
