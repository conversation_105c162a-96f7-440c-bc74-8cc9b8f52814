package com.trinasolar.scp.system;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.data.mongo.MongoRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.netflix.hystrix.EnableHystrix;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR>
 * @date 2022/8/24
 */
@SpringBootApplication(
        scanBasePackages = {
                "com.trinasolar.scp.system",
                "com.ibm.dpf.common.config",
                "com.ibm.dpf.gateway",
                "com.ibm.dpf.gateway.service",
                "com.trinasolar.scp.common.api.util",
                "com.trinasolar.scp.common.api.component"
        },
        exclude = {CacheAutoConfiguration.class, MongoAutoConfiguration.class,
                MongoRepositoriesAutoConfiguration.class, MongoDataAutoConfiguration.class,
                FreeMarkerAutoConfiguration.class, RabbitAutoConfiguration.class})
@EnableDiscoveryClient
@EnableHystrix
@EnableSwagger2
@RefreshScope
@EnableFeignClients(basePackages = {"com.trinasolar.scp.common.api.component", "com.trinasolar.scp.system.service.feign"})
@EnableJpaAuditing(auditorAwareRef = "userAuditorAware")
@EnableCaching
public class SystemJobApplication {
    public static void main(String[] args) {
        SpringApplication.run(SystemJobApplication.class, args);
    }
}
