package com.trinasolar.scp.system.jobhandler;

import com.trinasolar.scp.system.service.service.ErpPeopleService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/8/15
 */
@Component
@Slf4j
public class ErpPeopleSyncJobHandler {

    @Autowired
    ErpPeopleService erpPeopleService;

    @XxlJob(value = "erpPeopleSyncJobHandler")
    public void handler() {
        erpPeopleService.sync();
    }
}
