package com.trinasolar.scp.system.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

/**
 * <AUTHOR>
 * @date 2022/7/11
 */
@Configuration
public class ScriptEngineManagerConfig {
    @Bean
    public ScriptEngine scriptEngine() throws ScriptException {
        StringBuffer exp = new StringBuffer();
        exp.append("      function ain(fieldValue, value) {");
        exp.append("          return value != null && fieldValue != null && (';'+value+';').indexOf(';' + fieldValue +';') >= 0;");
        exp.append("      };");

        exp.append("      function eq(fieldValue, value) {");
        exp.append("          return value != null && fieldValue != null && ''+fieldValue == ''+value;");
        exp.append("      };");

        exp.append("      function notin(fieldValue, value) {");
        exp.append("          return value != null && fieldValue != null && (';'+value+';').indexOf(';' + fieldValue +';') < 0;");
        exp.append("      };");

        exp.append("      function numRange(fieldValue, valueFrom,valueTo) {");
        exp.append("          return valueFrom != null && valueTo != null && fieldValue != null && " +
                " fieldValue >= valueFrom && fieldValue<=valueTo ;");
        exp.append("      };");
        //结果
        ScriptEngineManager manager = new ScriptEngineManager(null);
        ScriptEngine engine = manager.getEngineByName("nashorn");
        engine.eval(exp.toString());
        return engine;
    }
}
