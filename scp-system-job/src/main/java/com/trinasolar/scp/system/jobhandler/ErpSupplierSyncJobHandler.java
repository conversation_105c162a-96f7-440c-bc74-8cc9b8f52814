package com.trinasolar.scp.system.jobhandler;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.trinasolar.scp.common.api.util.MetaData;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import com.trinasolar.scp.system.service.service.ErpSupplierService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/8/15
 */
@Component
@Slf4j
public class ErpSupplierSyncJobHandler {

    @Autowired
    ErpSupplierService erpSupplierService;

    @XxlJob(value = "erpSupplierSyncJobHandler")
    public void handler() {
        log.info("开始同步ERP供应商");
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSON.parseObject(param);
        LocalDateTime toDateTime = LocalDateTime.now();
        LocalDateTime fromDateTime = toDateTime.minusDays(3);
        if (jsonObject != null) {
            if (jsonObject.containsKey("toDateTime") && jsonObject.containsKey("fromDateTime")) {
                String toDateTimeStr = jsonObject.getString("toDateTime");
                toDateTime = LocalDateTimeUtil.parse(toDateTimeStr);
                fromDateTime = LocalDateTimeUtil.parse(jsonObject.getString("fromDateTime"));
            } else if (jsonObject.containsKey("fromDateTime")) {
                // 只有开始时间,则运行开始时间到现在
                fromDateTime = LocalDateTimeUtil.parse(jsonObject.getString("fromDateTime"));
            }
        }
        erpSupplierService.dailySync(fromDateTime, toDateTime);
    }

}
