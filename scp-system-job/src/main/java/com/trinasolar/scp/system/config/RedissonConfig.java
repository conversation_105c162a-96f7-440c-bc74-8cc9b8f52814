package com.trinasolar.scp.system.config;

import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/20
 */
@Configuration
@RefreshScope
public class RedissonConfig {

    @Value("${spring.redis.cluster.nodes:#{null}}")
    public String clusterAddress;

    @Value("${spring.redis.host:#{null}}")
    public String singleAddress;

    @Value("${spring.redis.port:#{null}}")
    public String port;

    @Value("${spring.redis.password:#{null}}")
    public String password;

    @Bean(destroyMethod = "shutdown")
    public RedissonClient redisson() throws IOException {
        Config config = new Config();
        if (StringUtils.isNotBlank(clusterAddress)) {
            //集群模式配置
            List<String> nodes = Arrays.asList(clusterAddress.split(","));

            List<String> clusterNodes = new ArrayList<>();
            for (int i = 0; i < nodes.size(); i++) {
                clusterNodes.add("redis://" + nodes.get(i));
            }
            ClusterServersConfig clusterServersConfig = config.useClusterServers()
                    .addNodeAddress(clusterNodes.toArray(new String[clusterNodes.size()]));

            if (!StringUtils.isEmpty(password)) {
                clusterServersConfig.setPassword(password);
            }
        } else {
            //单节点配置
            String address = "redis://" + singleAddress + ":" + port;
            SingleServerConfig serverConfig = config.useSingleServer();
            serverConfig.setAddress(address);
            if (!StringUtils.isEmpty(password)) {
                serverConfig.setPassword(password);
            }
        }
        //看门狗的锁续期时间，默认30000ms，这里配置成15000ms
        config.setLockWatchdogTimeout(15000);
        return Redisson.create(config);
    }
}
