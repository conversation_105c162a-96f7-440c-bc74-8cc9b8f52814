package com.trinasolar.scp.system.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * 配置默认线程池，后期修改
 *
 * <AUTHOR>
 * @date 2022/1/7
 */
@Configuration
public class ThreadPoolConfig {
    @Bean
    public ExecutorService threadPoolExecutor(ScpThreadPoolProperties config) {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(config.getThreadNamePrefix() + "-%d").build();
        return TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(
                config.getCorePoolSize() ,
                Integer.max(config.getMaxPoolSize(),Runtime.getRuntime().availableProcessors() * 2),
                config.getKeepAliveSeconds(),
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(config.getQueueCapacity()),
                threadFactory,
                new ThreadPoolExecutor.AbortPolicy()
        ));
    }
}
