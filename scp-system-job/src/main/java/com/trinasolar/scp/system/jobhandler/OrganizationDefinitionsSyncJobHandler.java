package com.trinasolar.scp.system.jobhandler;

import com.trinasolar.scp.common.api.util.MetaData;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import com.trinasolar.scp.system.service.service.OrganizationDefinitionsService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/8/15
 */
@Component
@Slf4j
public class OrganizationDefinitionsSyncJobHandler {

    @Autowired
    OrganizationDefinitionsService organizationDefinitionsService;

    @XxlJob(value = "organizationDefinitionsSyncJobHandler")
    public void handler() {
        log.info("开始同步库存组织");
        organizationDefinitionsService.sync();
    }
}
