package com.trinasolar.scp.system.jobhandler;

import com.trinasolar.scp.common.api.util.MetaData;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import com.trinasolar.scp.system.service.service.ApprovalSupplierService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/8/24
 */
@Component
@Slf4j
public class ApprovalSupplierJobHandler {
    @Autowired
    ApprovalSupplierService approvalSupplierService;

    @XxlJob(value = "approvalSupplierJobHandler")
    public void handler() {
        log.info("开始同步批准供应商");
        approvalSupplierService.dailySync();
    }
}
