package com.trinasolar.scp.system.jobhandler;

import com.ibm.dpf.common.config.utils.RedisLock;
import com.trinasolar.scp.common.api.util.MetaData;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import com.trinasolar.scp.system.service.enums.Constants;
import com.trinasolar.scp.system.service.service.DailyRatesService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName: DailyRatesJobHandler.java, Created by IntelliJ IDEA
 * @Description: TODO
 * @Author: wang_jun
 * @Date: 2022/8/29 09:15
 * @Version :1.0
 **/
@Component
@Slf4j
public class DailyRatesJobHandler {

    @Autowired
    DailyRatesService dailyRatesService;

    @Autowired
    RedisLock redisLock;

    @XxlJob(value = "syncDailyRatesERPHandler")
    public void syncDailyRates() {

        log.info("开始从ERP同步汇率信息syncDailyRates");

        boolean lockExists = redisLock.lock(Constants.ERP_DAILY_RATES_SYNC_KEY, 1000 * 60 * 60);
        if (!lockExists) {
            return;
        }

        try {
            dailyRatesService.syncDailyRatesERP();
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            redisLock.unlock(Constants.ERP_DAILY_RATES_SYNC_KEY);
        }
    }
}
