package com.trinasolar.scp.system.service.service.impl;

import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.system.domain.entity.Language;
import com.trinasolar.scp.system.service.repository.LanguageRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LovCacheServiceImpl 测试类
 * 主要测试替代 keys 方法的新实现
 */
@ExtendWith(MockitoExtension.class)
class LovCacheServiceImplTest {

    @Mock
    private StringRedisTemplate stringRedisTemplate;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RLock lock;

    @Mock
    private LanguageRepository languageRepository;

    @InjectMocks
    private LovCacheServiceImpl lovCacheService;

    private static final String TEST_HEADER_CODE = "TEST_LOV";

    @BeforeEach
    void setUp() {
        when(redissonClient.getLock(anyString())).thenReturn(lock);

        // 模拟语言数据
        Language zhCN = new Language();
        zhCN.setCode("zh_CN");
        zhCN.setName("中文");

        Language enUS = new Language();
        enUS.setCode("en_US");
        enUS.setName("English");

        when(languageRepository.findAll()).thenReturn(Arrays.asList(zhCN, enUS));
    }

    @Test
    void testClearLovHeaderCache_ShouldDeleteSpecificKeys() {
        // 模拟 LovUtils 的静态方法
        try (MockedStatic<LovUtils> lovUtilsMock = mockStatic(LovUtils.class)) {
            // 设置模拟返回值
            lovUtilsMock.when(() -> LovUtils.getRedisIdKey(TEST_HEADER_CODE))
                    .thenReturn("lov:id:" + TEST_HEADER_CODE);
            lovUtilsMock.when(() -> LovUtils.getRedisValueKey(TEST_HEADER_CODE))
                    .thenReturn("lov:value:" + TEST_HEADER_CODE);
            lovUtilsMock.when(() -> LovUtils.getWholeLovCacheKey(TEST_HEADER_CODE, true))
                    .thenReturn("lov:whole:" + TEST_HEADER_CODE + ":translate");
            lovUtilsMock.when(() -> LovUtils.getWholeLovCacheKey(TEST_HEADER_CODE, false))
                    .thenReturn("lov:whole:" + TEST_HEADER_CODE);
            lovUtilsMock.when(() -> LovUtils.getWholeNameLovCacheKey(TEST_HEADER_CODE))
                    .thenReturn("lov:name:" + TEST_HEADER_CODE);

            // 模拟所有键都存在
            when(stringRedisTemplate.hasKey(anyString())).thenReturn(true);

            // 执行测试
            lovCacheService.clearLovHeaderCache(TEST_HEADER_CODE);

            // 验证锁的使用
            verify(lock).lock();
            verify(lock).unlock();

            // 验证检查了所有预期的键（包括多语言键）
            verify(stringRedisTemplate).hasKey("lov:id:" + TEST_HEADER_CODE);
            verify(stringRedisTemplate).hasKey("lov:value:" + TEST_HEADER_CODE);
            verify(stringRedisTemplate).hasKey("lov:whole:" + TEST_HEADER_CODE + ":translate");
            verify(stringRedisTemplate).hasKey("lov:whole:" + TEST_HEADER_CODE);
            verify(stringRedisTemplate).hasKey("lov:name:" + TEST_HEADER_CODE);

            // 验证多语言键的检查
            verify(stringRedisTemplate).hasKey("lov:id:zh_CN" + TEST_HEADER_CODE);
            verify(stringRedisTemplate).hasKey("lov:id:en_US" + TEST_HEADER_CODE);
            verify(stringRedisTemplate).hasKey("lov:value:zh_CN" + TEST_HEADER_CODE);
            verify(stringRedisTemplate).hasKey("lov:value:en_US" + TEST_HEADER_CODE);
            verify(stringRedisTemplate).hasKey("SCP_LOVLINEzh_CN" + TEST_HEADER_CODE);
            verify(stringRedisTemplate).hasKey("SCP_LOVLINEen_US" + TEST_HEADER_CODE);
            verify(stringRedisTemplate).hasKey("SCP_LOVLINE" + TEST_HEADER_CODE);

            // 验证批量删除被调用
            verify(stringRedisTemplate).delete(any(List.class));
        }
    }

    @Test
    void testClearLovHeaderCache_WithBlankHeaderCode_ShouldReturn() {
        // 测试空白 headerCode 的情况
        lovCacheService.clearLovHeaderCache("");
        lovCacheService.clearLovHeaderCache(null);
        lovCacheService.clearLovHeaderCache("   ");

        // 验证没有进行任何 Redis 操作
        verify(stringRedisTemplate, never()).hasKey(anyString());
        verify(stringRedisTemplate, never()).delete(any(List.class));
        verify(redissonClient, never()).getLock(anyString());
    }

    @Test
    void testClearLovHeaderCache_WithNoExistingKeys_ShouldNotDelete() {
        try (MockedStatic<LovUtils> lovUtilsMock = mockStatic(LovUtils.class)) {
            // 设置模拟返回值
            lovUtilsMock.when(() -> LovUtils.getRedisIdKey(TEST_HEADER_CODE))
                    .thenReturn("lov:id:" + TEST_HEADER_CODE);
            lovUtilsMock.when(() -> LovUtils.getRedisValueKey(TEST_HEADER_CODE))
                    .thenReturn("lov:value:" + TEST_HEADER_CODE);
            lovUtilsMock.when(() -> LovUtils.getWholeLovCacheKey(TEST_HEADER_CODE, true))
                    .thenReturn("lov:whole:" + TEST_HEADER_CODE + ":translate");
            lovUtilsMock.when(() -> LovUtils.getWholeLovCacheKey(TEST_HEADER_CODE, false))
                    .thenReturn("lov:whole:" + TEST_HEADER_CODE);
            lovUtilsMock.when(() -> LovUtils.getWholeNameLovCacheKey(TEST_HEADER_CODE))
                    .thenReturn("lov:name:" + TEST_HEADER_CODE);

            // 模拟所有键都不存在
            when(stringRedisTemplate.hasKey(anyString())).thenReturn(false);

            // 执行测试
            lovCacheService.clearLovHeaderCache(TEST_HEADER_CODE);

            // 验证锁的使用
            verify(lock).lock();
            verify(lock).unlock();

            // 验证检查了所有预期的键
            verify(stringRedisTemplate, times(5)).hasKey(anyString());

            // 验证没有调用删除操作（因为没有键存在）
            verify(stringRedisTemplate, never()).delete(any(List.class));
        }
    }
}
