package com.trinasolar.scp.system.service.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LovCacheServiceImpl SCAN 方法测试类
 * 测试使用 SCAN 替代 KEYS 的实现
 */
@ExtendWith(MockitoExtension.class)
class LovCacheServiceImplScanTest {

    @Mock
    private StringRedisTemplate stringRedisTemplate;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RLock lock;

    @Mock
    private Cursor<String> cursor;

    @InjectMocks
    private LovCacheServiceImpl lovCacheService;

    private static final String TEST_HEADER_CODE = "Channel";

    @BeforeEach
    void setUp() {
        when(redissonClient.getLock(anyString())).thenReturn(lock);
    }

    @Test
    void testClearLovHeaderCache_WithScan_ShouldDeleteFoundKeys() {
        // 模拟 SCAN 返回的键
        Set<String> mockKeys1 = new HashSet<>(Arrays.asList(
                "SCP_LOVLINE:Channel:zh_CN:V",
                "SCP_LOVLINE:Channel:zh_CN:ALL",
                "some_other_Channel_key"
        ));
        
        Set<String> mockKeys2 = new HashSet<>(Arrays.asList(
                "SCP_LOVLINE:Channel:en_US:V",
                "SCP_LOVLINE:Channel:en_US:ALL"
        ));

        // 模拟 cursor 行为
        when(cursor.hasNext())
                .thenReturn(true, true, true, false) // 第一次扫描返回3个键
                .thenReturn(true, true, false);      // 第二次扫描返回2个键
        
        when(cursor.next())
                .thenReturn("SCP_LOVLINE:Channel:zh_CN:V")
                .thenReturn("SCP_LOVLINE:Channel:zh_CN:ALL")
                .thenReturn("some_other_Channel_key")
                .thenReturn("SCP_LOVLINE:Channel:en_US:V")
                .thenReturn("SCP_LOVLINE:Channel:en_US:ALL");

        // 模拟 stringRedisTemplate.scan() 方法
        when(stringRedisTemplate.scan(any(ScanOptions.class))).thenReturn(cursor);

        // 执行测试
        lovCacheService.clearLovHeaderCache(TEST_HEADER_CODE);

        // 验证锁的使用
        verify(lock).lock();
        verify(lock).unlock();

        // 验证 SCAN 被调用了两次（两个不同的模式）
        verify(stringRedisTemplate, times(2)).scan(any(ScanOptions.class));

        // 验证删除操作被调用
        verify(stringRedisTemplate).delete(any(Set.class));
    }

    @Test
    void testClearLovHeaderCache_WithBlankHeaderCode_ShouldReturn() {
        // 测试空白 headerCode 的情况
        lovCacheService.clearLovHeaderCache("");
        lovCacheService.clearLovHeaderCache(null);
        lovCacheService.clearLovHeaderCache("   ");

        // 验证没有进行任何 Redis 操作
        verify(stringRedisTemplate, never()).scan(any(ScanOptions.class));
        verify(stringRedisTemplate, never()).delete(any(Set.class));
        verify(redissonClient, never()).getLock(anyString());
    }

    @Test
    void testClearLovHeaderCache_WithNoKeysFound_ShouldNotDelete() {
        // 模拟 SCAN 没有找到任何键
        when(cursor.hasNext()).thenReturn(false);
        when(stringRedisTemplate.scan(any(ScanOptions.class))).thenReturn(cursor);

        // 执行测试
        lovCacheService.clearLovHeaderCache(TEST_HEADER_CODE);

        // 验证锁的使用
        verify(lock).lock();
        verify(lock).unlock();

        // 验证 SCAN 被调用
        verify(stringRedisTemplate, times(2)).scan(any(ScanOptions.class));

        // 验证没有调用删除操作（因为没有找到键）
        verify(stringRedisTemplate, never()).delete(any(Set.class));
    }

    @Test
    void testClearLovHeaderCache_WithScanException_ShouldHandleGracefully() {
        // 模拟 SCAN 抛出异常
        when(stringRedisTemplate.scan(any(ScanOptions.class)))
                .thenThrow(new RuntimeException("Redis connection error"));

        // 执行测试
        lovCacheService.clearLovHeaderCache(TEST_HEADER_CODE);

        // 验证锁的使用
        verify(lock).lock();
        verify(lock).unlock();

        // 验证 SCAN 被调用
        verify(stringRedisTemplate, times(2)).scan(any(ScanOptions.class));

        // 验证没有调用删除操作（因为发生异常）
        verify(stringRedisTemplate, never()).delete(any(Set.class));
    }

    @Test
    void testScanOptions_Configuration() {
        // 模拟正常的 cursor 行为
        when(cursor.hasNext()).thenReturn(false);
        when(stringRedisTemplate.scan(any(ScanOptions.class))).thenReturn(cursor);

        // 执行测试
        lovCacheService.clearLovHeaderCache(TEST_HEADER_CODE);

        // 验证 ScanOptions 的配置
        verify(stringRedisTemplate, times(2)).scan(argThat(options -> {
            // 这里可以验证 ScanOptions 的配置是否正确
            // 例如 count 是否设置为 100
            return true; // 简化验证
        }));
    }
}
