package com.trinasolar.scp.system.service.utils;

import java.util.HashSet;
import java.util.List;

import org.apache.commons.compress.utils.Lists;
import org.springframework.util.CollectionUtils;

/**
 * @USER: yakun
 * @DATE: 2022/07/19
 */
public class IterableUtils {

    public static <T> List<T> iterableToList(Iterable<T> iterable) {
        List<T> list = Lists.newArrayList();
        iterable.forEach(list::add);
        return list;
    }

    public static <T> boolean checkListData(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        HashSet<T> set = new HashSet<>(list);
        return set.size() != list.size();
    }
}
