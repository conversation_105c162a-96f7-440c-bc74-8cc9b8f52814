package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.ErpApprovalSupplierFixLotQtyDTO;
import com.trinasolar.scp.system.domain.query.ErpApprovalSupplierFixLotQtyQuery;
import com.trinasolar.scp.system.domain.save.ErpApprovalSupplierFixLotQtySaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 合格供应商整车数或整柜数表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-09 07:56:06
 */
public interface ErpApprovalSupplierFixLotQtyService {
    /**
     * 分页获取合格供应商整车数或整柜数表
     *
     * @param query 查询对象
     * @return 合格供应商整车数或整柜数表分页对象
     */
    Page<ErpApprovalSupplierFixLotQtyDTO> queryByPage(ErpApprovalSupplierFixLotQtyQuery query);

    /**
     * 根据主键获取合格供应商整车数或整柜数表详情
     *
     * @param id 主键
     * @return 合格供应商整车数或整柜数表详情
     */
    ErpApprovalSupplierFixLotQtyDTO queryById(Long id);

    /**
     * 保存或更新合格供应商整车数或整柜数表
     *
     * @param saveDTO 合格供应商整车数或整柜数表保存对象
     * @return 合格供应商整车数或整柜数表对象
     */
    ErpApprovalSupplierFixLotQtyDTO save(ErpApprovalSupplierFixLotQtySaveDTO saveDTO);

    /**
     * 根据主键逻辑删除合格供应商整车数或整柜数表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(ErpApprovalSupplierFixLotQtyQuery query, HttpServletResponse response);

    void importData(MultipartFile file);

    BigDecimal queryLotQty(ErpApprovalSupplierFixLotQtyQuery query);

    BigDecimal queryMinimumOrderQuantity(ErpApprovalSupplierFixLotQtyQuery query);

    ErpApprovalSupplierFixLotQtyDTO queryLot(String ItemNum, String basePlace, Long vendorId);

    Map<String, List<ErpApprovalSupplierFixLotQtyDTO>> getItemCodeAndVendorIdMap();

}

