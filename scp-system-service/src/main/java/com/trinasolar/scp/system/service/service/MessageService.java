package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.MessageDTO;
import com.trinasolar.scp.system.domain.entity.Message;
import com.trinasolar.scp.system.domain.query.MessageQuery;
import com.trinasolar.scp.system.domain.save.MessageSaveDTO;
import org.springframework.data.domain.Page;

/**
 * 系统消息 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
public interface MessageService {
    Page<Message> queryByPage(MessageQuery query);

    MessageDTO queryById(Long id);

    MessageDTO save(MessageSaveDTO saveDTO);

    void deleteById(Long id);

    String getDescriptinoByLangAndCode(String lang, String code);
}

