package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.OperatorConfigDTO;
import com.trinasolar.scp.system.domain.query.OperatorConfigQuery;
import com.trinasolar.scp.system.domain.save.OperatorConfigSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 操作配置表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-01 09:45:41
 */
public interface OperatorConfigService {
    /**
     * 分页获取操作配置表
     *
     * @param query 查询对象
     * @return 操作配置表分页对象
     */
    Page<OperatorConfigDTO> queryByPage(OperatorConfigQuery query);

    /**
     * 根据主键获取操作配置表详情
     *
     * @param id 主键
     * @return 操作配置表详情
     */
    OperatorConfigDTO queryById(Long id);

    /**
     * 保存或更新操作配置表
     *
     * @param saveDTO 操作配置表保存对象
     * @return 操作配置表对象
     */
    OperatorConfigDTO save(OperatorConfigSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除操作配置表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query
     * @param response
     */
    void export(OperatorConfigQuery query, HttpServletResponse response);

    /**
     * 导入
     *
     * @param multipartFile
     * @return 批次号
     */
    String importData(MultipartFile multipartFile);

    OperatorConfigDTO findByUserMenu(OperatorConfigQuery query);
}

