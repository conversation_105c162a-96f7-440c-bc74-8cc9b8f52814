package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.ApiLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 推送数据至其他系统接口日志表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-18 09:26:45
 */
@Repository
public interface ApiLogRepository extends JpaRepository<ApiLog, Long>, QuerydslPredicateExecutor<ApiLog> {
}
