package com.trinasolar.scp.system.service.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.common.api.enums.YesOrNoEnum;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.system.domain.convert.ErpSupplierDEConvert;
import com.trinasolar.scp.system.domain.dto.ErpSupplierDTO;
import com.trinasolar.scp.system.domain.dto.ErpSupplierSelectListDTO;
import com.trinasolar.scp.system.domain.dto.ExternalInterfaceDTO;
import com.trinasolar.scp.system.domain.entity.ErpSupplier;
import com.trinasolar.scp.system.domain.entity.LovHeader;
import com.trinasolar.scp.system.domain.entity.LovLine;
import com.trinasolar.scp.system.domain.entity.QErpSupplier;
import com.trinasolar.scp.system.domain.query.ErpSupplierQuery;
import com.trinasolar.scp.system.domain.query.ErpSupplierSelectQuery;
import com.trinasolar.scp.system.domain.save.ErpSupplierSaveDTO;
import com.trinasolar.scp.system.service.repository.ErpSupplierRepository;
import com.trinasolar.scp.system.service.service.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * erp供应商表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-22 09:10:40
 */
@Slf4j
@Service("erpSupplierService")
@CacheConfig(cacheNames = "ERP_SUPPLIER", cacheManager = "scpRedisCacheManager")
@RefreshScope
public class ErpSupplierServiceImpl implements ErpSupplierService {
    public static final String REDIS_KEY = "ERP_SUPPLIER";

    private static final int PAGE_SIZE = 600;

    QErpSupplier q = QErpSupplier.erpSupplier;

    @Autowired
    ErpSupplierRepository repository;

    @Autowired
    ErpSupplierDEConvert erpSupplierDEConvert;

    @Autowired
    ExecutorService threadPoolExecutor;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    ErpInterfaceService erpInterfaceService;

    @Autowired
    JPAQueryFactory jpaQueryFactory;

    @Autowired
    LovLineService lovLineService;

    @Autowired
    LovHeaderService lovHeaderService;

    @Autowired
    @Lazy
    ErpSupplierService erpSupplierService;

    @Autowired
    @Lazy
    ErpApprovalSupplierService erpApprovalSupplierService;

    @Value("${erp.interface.vendor.url}")
    private String erpUrl;

    @Value("${erp.interface.vendor.clientid}")
    private String eaClientId;

    @Value("${erp.interface.vendor.secret}")
    private String eaClientSecret;

    @Override
    public Page<ErpSupplierDTO> queryByPage(ErpSupplierQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<ErpSupplier> erpSupplierPage = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(erpSupplierDEConvert.toDto(erpSupplierPage.getContent()), pageable, erpSupplierPage.getTotalElements());
    }

    /**
     * 构建查询条件
     *
     * @param query
     * @return
     */
    private BooleanBuilder buildWhere(ErpSupplierQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QErpSupplier erpSupplier = QErpSupplier.erpSupplier;
        if (CollectionUtils.isNotEmpty(query.getSupplierIds())) {
            booleanBuilder.and(erpSupplier.vendorId.in(query.getSupplierIds()));
        }
        if (CollectionUtils.isNotEmpty(query.getNames())) {
            booleanBuilder.and(erpSupplier.vendorName.in(query.getNames()));
        }
        if (CollectionUtils.isNotEmpty(query.getAttribute3List())) {
            booleanBuilder.and(erpSupplier.attribute3.in(query.getAttribute3List()));
        }
        if (CollectionUtils.isNotEmpty(query.getVendorIdList())) {
            booleanBuilder.and(erpSupplier.vendorId.in(query.getVendorIdList()));
        }
        return booleanBuilder;
    }

    @Override
    public ErpSupplierDTO queryById(Long id) {
        ErpSupplier queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        ErpSupplierDTO result = new ErpSupplierDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ErpSupplierDTO save(ErpSupplierSaveDTO saveDTO) {
        ErpSupplier newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new ErpSupplier());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @SneakyThrows
    @Override
    public void export(ErpSupplierQuery query, HttpServletResponse response) {
        List<ErpSupplierDTO> dtos = queryByPage(query).getContent();
        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "erp供应商表", "erp供应商表", excelPara.getSimpleHeader(), excelData);
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String importData(MultipartFile multipartFile, ExcelPara excelPara) {
        String batchNo = UUID.randomUUID().toString();
        List<ErpSupplierDTO> excelData =
                ExcelUtils.readExcel(multipartFile.getInputStream(), null, ErpSupplierDTO.class, excelPara);

        log.info("{}条数据，开始存储数据库！", excelData.size());
        //校验 todo
        repository.saveAll(erpSupplierDEConvert.toEntity(excelData));
        log.info("存储数据库成功！");

        return batchNo;
    }

    @Override
    @CacheEvict(cacheNames = {
            "ERP_SUPPLIER",
            "ERP_SUPPLIER_findByLegalEntity",
            "ERP_SUPPLIER_brandSuppliersIds",
            "ERP_SUPPLIER_findBrandBySupplierId",
            "ErpSupplier_queryByVendorAltName",
            "ErpSupplier_findSuppliersByBrand",
            "ErpSupplier_getByName",
            "ErpSupplier_getById",
            "ErpSupplier_getAllErpSupplierDTOS",
    }, allEntries = true)
    public void sync() {
        LocalDateTime currentTime = LocalDateTimeUtil.parse("2009-12-01T00:00:00");
        LocalDateTime now = LocalDateTime.now();
        Lock lock = new ReentrantLock();
        for (; ; ) {
            if (currentTime.isAfter(now)) {
                break;
            }
            LocalDateTime endTime = currentTime.plusMonths(1L);

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("last_update_date_from", LocalDateTimeUtil.formatNormal(currentTime));
            jsonObject.put("last_update_date_to", LocalDateTimeUtil.formatNormal(endTime));

            currentTime = endTime;
            threadPoolExecutor.execute(() -> {
                ExternalInterfaceDTO params = createParams(jsonObject);
                String json = erpInterfaceService.postForString(params);
                JSONObject outJson = JSON.parseObject(json);
                JSONArray jsonArray = outJson.getJSONArray("data");
                if (jsonArray == null) {
                    return;
                }
                for (int i = 0; i < jsonArray.size(); i++) {
                    ErpSupplier erpSupplier = jsonArray.getObject(i, ErpSupplier.class);
                    lock.lock();
                    try {
                        // 查询是否有
                        JPAQuery<ErpSupplier> where = jpaQueryFactory.select(q)
                                .from(q);

                        where.where(
                                q.vendorId.eq(erpSupplier.getVendorId())
                        );

                        List<ErpSupplier> findSupplier = where.fetch();
                        if (findSupplier != null && !findSupplier.isEmpty()) {
                            ErpSupplier erpSupplier1 = findSupplier.get(0);
                            BeanUtils.copyProperties(erpSupplier, erpSupplier1, "id");
                            erpSupplier = erpSupplier1;
                        }
                        if (!"Y".equals(erpSupplier.getAttribute2())) {
                            erpSupplier.setAttribute2("N");
                        }
                        repository.save(erpSupplier);
                    } finally {
                        lock.unlock();
                    }
                }
            });
        }
    }

    private ExternalInterfaceDTO createParams(JSONObject param) {
        ExternalInterfaceDTO dto = new ExternalInterfaceDTO();
        dto.setUrl(erpUrl);
        dto.setPath("/la002/v1/vendor/list");
        dto.setClientId(eaClientId);
        dto.setSecret(eaClientSecret);
        dto.setRequestBody(param.toJSONString());
        return dto;
    }

    @Override
    public List<ErpSupplierDTO> list(ErpSupplierQuery query) {
        // 使用缓存
        List<ErpSupplierDTO> result = new ArrayList<>();
        List<String> names = query.getNames();
        boolean isAll = true;
        if (names != null && !names.isEmpty()) {
            for (String name : names) {
                ErpSupplierDTO dto = erpSupplierService.getByName(name);
                if (dto != null) {
                    result.add(dto);
                }
            }
            isAll = false;
        }
        List<Long> supplierIds = query.getSupplierIds();
        if (supplierIds != null && !supplierIds.isEmpty()) {
            for (Long supplierId : supplierIds) {
                ErpSupplierDTO dto = erpSupplierService.getById(supplierId);
                if (dto != null) {
                    result.add(dto);
                }
            }
            isAll = false;
        }
        if (isAll) {
            return erpSupplierService.getAllErpSupplierDTOS();
        }

        Map<Long, ErpSupplierDTO> collect = result.stream().collect(Collectors.toMap(ErpSupplierDTO::getVendorId, Function.identity(), (k1, k2) -> k1));
        return new ArrayList<>(collect.values());
    }

    @Override
    public ErpSupplier findByVendorId(Long vendorId) {
        ErpSupplier newObj = Optional.ofNullable(vendorId)
                .map(id -> repository.findByVendorId(vendorId))
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .orElse(new ErpSupplier());
        return newObj;
    }

    @Override
    @CacheEvict(cacheNames = {
            "ERP_SUPPLIER",
            "ERP_SUPPLIER_findByLegalEntity",
            "ERP_SUPPLIER_brandSuppliersIds",
            "ERP_SUPPLIER_findBrandBySupplierId",
            "ErpSupplier_queryByVendorAltName",
            "ErpSupplier_findSuppliersByBrand",
            "ErpSupplier_getByName",
            "ErpSupplier_getById",
            "ErpSupplier_getAllErpSupplierDTOS",
    }, allEntries = true)
    public void dailySync(LocalDateTime fromDateTime, LocalDateTime toDateTime) {
        /*LocalDateTime lastUpdateFrom = LocalDateTime.now().minusDays(3);

        LocalDateTime endTime = LocalDateTime.now();*/

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("last_update_date_from", LocalDateTimeUtil.formatNormal(fromDateTime));
        jsonObject.put("last_update_date_to", LocalDateTimeUtil.formatNormal(toDateTime));
        jsonObject.put("pageSize", PAGE_SIZE);

        int pageNum = 1;
        List<ErpSupplierDTO> updDatas = new ArrayList<>();
        for (; ; ) {
            jsonObject.put("pageNum", pageNum++);
            // "/vendor/list"  Excel没有找到这个接口
            ExternalInterfaceDTO params = createParams(jsonObject);
            String json = erpInterfaceService.postForString(params);

            JSONObject outJson = JSON.parseObject(json);
            JSONArray jsonArray = outJson.getJSONArray("data");
            if (jsonArray == null) {
                break;
            }

            for (int i = 0; i < jsonArray.size(); i++) {
                ErpSupplier erpSupplier = jsonArray.getObject(i, ErpSupplier.class);
                // 查询是否有
                JPAQuery<ErpSupplier> where = jpaQueryFactory.select(q)
                        .from(q);

                where.where(
                        q.vendorId.eq(erpSupplier.getVendorId())
                );

                List<ErpSupplier> findSupplier = where.fetch();
                if (findSupplier != null && !findSupplier.isEmpty()) {
                    ErpSupplier erpSupplier1 = findSupplier.get(0);
                    BeanUtils.copyProperties(erpSupplier, erpSupplier1, "id");
                    erpSupplier = erpSupplier1;
                }
                repository.save(erpSupplier);

                // 同步到批准供应商修改
                updDatas.add(erpSupplierDEConvert.toDto(erpSupplier));
            }
        }
        erpApprovalSupplierService.updateVendorNameByErpSupplier(updDatas);
    }

    @Override
    public List<ErpSupplierSelectListDTO> selectList(ErpSupplierSelectQuery query) {
        JPAQuery<Tuple> jpaQuery = jpaQueryFactory.select(q.vendorId, q.vendorName)
                .from(q)
                .where(q.vendorTypeLookupCode.ne("EMPLOYEE"));
        if (StringUtils.isNotBlank(query.getVendorName())) {
            jpaQuery.where(q.vendorName.like("%" + query.getVendorName() + "%"));
        }
        jpaQuery.limit(500);
        List<Tuple> fetch = jpaQuery.fetch();

        return fetch.stream().map(tuple -> {
            Long vendorId = tuple.get(q.vendorId);
            String vendorName = tuple.get(q.vendorName);

            ErpSupplierSelectListDTO erpSupplierSelectListDTO = new ErpSupplierSelectListDTO();
            erpSupplierSelectListDTO.setVendorId(vendorId);
            erpSupplierSelectListDTO.setVendorName(vendorName);
            return erpSupplierSelectListDTO;
        }).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(cacheNames = {
            "ERP_SUPPLIER",
            "ERP_SUPPLIER_findByLegalEntity",
            "ERP_SUPPLIER_brandSuppliersIds",
            "ERP_SUPPLIER_findBrandBySupplierId",
            "ErpSupplier_queryByVendorAltName",
            "ErpSupplier_findSuppliersByBrand",
            "ErpSupplier_getByName",
            "ErpSupplier_getById",
            "ErpSupplier_getAllErpSupplierDTOS",
    }, allEntries = true)
    public void syncFromLov() {
        LovHeader lovHeader = lovHeaderService.queryByCode("Agent Information");
        List<LovLine> lovLines = lovLineService.listByLovHeaderId(lovHeader.getLovHeaderId());
        for (LovLine lovLine : lovLines) {
            JPAQuery<ErpSupplier> where = jpaQueryFactory.select(q)
                    .from(q);

            where.where(
                    q.vendorName.eq(lovLine.getLovValue())
            );

            ErpSupplier approvalSupplier = where.fetchOne();
            if (approvalSupplier == null) {
                ErpSupplier saveObj = new ErpSupplier();
                saveObj.setVendorId(lovLine.getLovLineId());
                saveObj.setVendorName(lovLine.getLovValue());
                saveObj.setAttribute1(lovLine.getAttribute1());
                saveObj.setAttribute2(lovLine.getAttribute2());
                saveObj.setEnabledFlag(YesOrNoEnum.YES.getCode());
                saveObj.setVendorTypeLookupCode(YesOrNoEnum.YES.getCode());
                repository.save(saveObj);
            }
        }
    }

    @Override
    @Cacheable(cacheNames = "ERP_SUPPLIER_findByLegalEntity", key = "#p0", unless = "#result == null")
    public List<ErpSupplierDTO> findByLegalEntity(String legalEntity) {
        List<ErpSupplier> erpSuppliers = repository.findByAttribute3OrderById(legalEntity);
        return erpSupplierDEConvert.toDto(erpSuppliers);
    }

    @Override
    @Cacheable(cacheNames = "ERP_SUPPLIER_brandSuppliersIds", key = "#p0", unless = "#result == null")
    public List<Long> findBrandSuppliersIdsBySupplierId(String supplierId) {
        List<ErpSupplier> byVendorId = repository.findByVendorId(Long.parseLong(supplierId));
        if (CollectionUtils.isEmpty(byVendorId)) {
            return Arrays.asList(Long.parseLong(supplierId));
        }
        Set<String> brands = byVendorId.stream().filter(i -> StringUtils.isNotBlank(i.getAttribute1()))
                .map(ErpSupplier::getAttribute1).collect(Collectors.toSet());

        List<ErpSupplier> findBrands = repository.findByAttribute1In(brands);
        return findBrands.stream().map(ErpSupplier::getVendorId).distinct().collect(Collectors.toList());
    }

    @Override
    @Cacheable(cacheNames = "ERP_SUPPLIER_findBrandBySupplierId", key = "#p0", unless = "#result == null")
    public List<String> findBrandBySupplierId(String supplierId) {
        List<ErpSupplier> byVendorId = repository.findByVendorId(Long.parseLong(supplierId));
        if (CollectionUtils.isEmpty(byVendorId)) {
            return new ArrayList<>();
        }
        List<String> brands = byVendorId.stream().filter(i -> StringUtils.isNotBlank(i.getAttribute1()))
                .map(ErpSupplier::getAttribute1).distinct().collect(Collectors.toList());

        return brands;
    }

    @Override
    @Cacheable(cacheNames = "ErpSupplier_queryByVendorAltName", key = "#p0", unless = "#result == null")
    public ErpSupplierDTO queryByVendorAltName(String vendorAltName) {
        ErpSupplier erpSupplier = repository.findByVendorNameAlt(vendorAltName);
        return erpSupplierDEConvert.toDto(erpSupplier);
    }

    @Override
    public List<String> queryAllSupplierName() {
        return repository.queryAllSupplierName();
    }

    @Override
    @Cacheable(cacheNames = "ErpSupplier_findSuppliersByBrand", key = "#p0", unless = "#result == null")
    public List<ErpSupplierDTO> findSuppliersByBrand(String brand) {
        List<ErpSupplier> findBrands = repository.findByAttribute1In(Collections.singleton(brand));
        return erpSupplierDEConvert.toDto(findBrands);
    }

    @Override
    @Cacheable(cacheNames = "ErpSupplier_getByName", key = "#p0", unless = "#result == null")
    public ErpSupplierDTO getByName(String vendorName) {
        ErpSupplier erpSupplier = jpaQueryFactory.selectFrom(q)
                .where(q.vendorName.eq(vendorName))
                .where(q.vendorTypeLookupCode.ne("EMPLOYEE").or(q.vendorTypeLookupCode.isNull()))
                .orderBy(q.endDateActive.desc())
                .fetchFirst();
        return erpSupplierDEConvert.toDto(erpSupplier);
    }

    @Override
    @Cacheable(cacheNames = "ErpSupplier_getById", key = "#p0", unless = "#result == null")
    public ErpSupplierDTO getById(Long vendorId) {
        ErpSupplier erpSupplier = jpaQueryFactory.selectFrom(q)
                .where(q.vendorId.eq(vendorId))
                .where(q.vendorTypeLookupCode.isNull().or(q.vendorTypeLookupCode.ne("EMPLOYEE")))
                .orderBy(q.endDateActive.desc())
                .fetchFirst();
        return erpSupplierDEConvert.toDto(erpSupplier);
    }

    @Override
    @Cacheable(cacheNames = "ErpSupplier_getAllErpSupplierDTOS")
    public List<ErpSupplierDTO> getAllErpSupplierDTOS() {
        // 组件辅料供应商的接口修改，供应商的值过滤掉员工供应商。即，表中 VENDOR_TYPE_LOOKUP_CODE<>'EMPLOYEE'的数据。
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QErpSupplier erpSupplier = QErpSupplier.erpSupplier;
        booleanBuilder.and(erpSupplier.vendorTypeLookupCode.ne("EMPLOYEE"));

        List<Tuple> tuples = jpaQueryFactory
                .select(erpSupplier.id, erpSupplier.vendorId, erpSupplier.vendorName, erpSupplier.vendorNameAlt, erpSupplier.attribute1)
                .from(erpSupplier)
                .where(booleanBuilder).fetch();
        List<ErpSupplierDTO> erpSupplierDTOS = new ArrayList<>();
        for (Tuple tuple : tuples) {
            ErpSupplierDTO erpSupplierDTO = new ErpSupplierDTO();
            erpSupplierDTO.setId(tuple.get(erpSupplier.id));
            erpSupplierDTO.setVendorId(tuple.get(erpSupplier.vendorId));
            erpSupplierDTO.setVendorName(tuple.get(erpSupplier.vendorName));
            erpSupplierDTO.setVendorNameAlt(tuple.get(erpSupplier.vendorNameAlt));
            erpSupplierDTO.setAttribute1(tuple.get(erpSupplier.attribute1));
            erpSupplierDTOS.add(erpSupplierDTO);
        }
        return erpSupplierDTOS;
    }

    @Override
    public List<ErpSupplierDTO> getErpSupplierListBy(ErpSupplierQuery query) {
        BooleanBuilder builder = buildWhere(query);
        return erpSupplierDEConvert.toDto(IterableUtils.toList(repository.findAll(builder)));
    }
}
