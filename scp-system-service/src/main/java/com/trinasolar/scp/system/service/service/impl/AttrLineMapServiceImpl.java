package com.trinasolar.scp.system.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.trinasolar.scp.common.api.base.BaseServiceImpl;
import com.trinasolar.scp.system.domain.convert.AttrLineMapDEConvert;
import com.trinasolar.scp.system.domain.dto.AttrLineMapDTO;
import com.trinasolar.scp.system.domain.entity.AttrLineMap;
import com.trinasolar.scp.system.domain.query.AttrLineMapQuery;
import com.trinasolar.scp.system.service.repository.AttrLineMapRepository;
import com.trinasolar.scp.system.service.service.AttrLineMapService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 属性行来源映射表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-05 19:21:39
 */
@Slf4j
@Service("attrLineMapService")
public class AttrLineMapServiceImpl extends BaseServiceImpl<AttrLineMap, Long> implements AttrLineMapService {
    @Autowired
    AttrLineMapRepository repository;

    @Override
    public AttrLineMapDTO queryById(Long id) {
        AttrLineMap queryObj = findById(id);
        if (queryObj == null) {
            return null;
        }

        AttrLineMapDTO result = new AttrLineMapDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Override
    public List<AttrLineMapDTO> getByAttrLineId(AttrLineMapQuery query) {
        List<AttrLineMap> attrLineMapList = repository.findByAttrLineIdEquals(query.getAttrLineId());
        return attrLineMapList.stream().map(attrLineMap -> {
            AttrLineMapDTO attrLineMapDTO = new AttrLineMapDTO();
            BeanUtils.copyProperties(attrLineMap, attrLineMapDTO);
            return attrLineMapDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public void batchSave(List<AttrLineMapDTO> attrLineMapDTOList) {
        List<AttrLineMap> attrLineMapList = attrLineMapDTOList.stream().map(attrLineMapDTO ->
                AttrLineMapDEConvert.INSTANCE.toEntity(attrLineMapDTO)).collect(Collectors.toList());

        saveAll(attrLineMapList);
    }

    @Override
    public List<AttrLineMapDTO> getByAttrLineIds(List<Long> attrLineIds) {
        List<AttrLineMap> attrLineMaps = repository.findByAttrLineIdInAndIsDeletedEquals(attrLineIds, 0);
        if (CollectionUtils.isEmpty(attrLineMaps)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(attrLineMaps, AttrLineMapDTO.class);
    }
}
