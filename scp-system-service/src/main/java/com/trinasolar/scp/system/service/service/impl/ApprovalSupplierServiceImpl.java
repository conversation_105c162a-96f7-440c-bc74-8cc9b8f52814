package com.trinasolar.scp.system.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.convert.ApprovalSupplierDEConvert;
import com.trinasolar.scp.system.domain.dto.ApprovalSupplierDTO;
import com.trinasolar.scp.system.domain.entity.*;
import com.trinasolar.scp.system.domain.feign.ItemsDTO;
import com.trinasolar.scp.system.domain.query.ApprovalSupplierQuery;
import com.trinasolar.scp.system.domain.save.ApprovalSupplierSaveDTO;
import com.trinasolar.scp.system.service.feign.BomFeign;
import com.trinasolar.scp.system.service.repository.ApprovalSupplierRepository;
import com.trinasolar.scp.system.service.repository.ErpSupplierRepository;
import com.trinasolar.scp.system.service.service.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 批准供应商
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-26 20:32:43
 */
@Slf4j
@Service("approvalSupplierService")
public class ApprovalSupplierServiceImpl implements ApprovalSupplierService {
    private final ReentrantLock lock = new ReentrantLock();

    QApprovalSupplier qApprovalSupplier = QApprovalSupplier.approvalSupplier;

    @Autowired
    ApprovalSupplierRepository repository;

    @Autowired
    ApprovalSupplierDEConvert approvalSupplierDEConvert;

    @Autowired
    ExecutorService threadPoolExecutor;

    @Autowired
    ErpApprovalSupplierService erpApprovalSupplierService;

    @Autowired
    ErpSupplierService erpSupplierService;

    @Autowired
    ErpSupplierRepository erpSupplierRepository;

    @Autowired
    LovLineService lovLineService;

    @Autowired
    LovHeaderService lovHeaderService;

    @Autowired
    BomFeign bomFeign;

    @Autowired
    JPAQueryFactory jpaQueryFactory;

    @Override
    public Page<ApprovalSupplierDTO> queryByPage(ApprovalSupplierQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<ApprovalSupplier> approvalSupplierPage = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(approvalSupplierDEConvert.toDto(approvalSupplierPage.getContent()), pageable, approvalSupplierPage.getTotalElements());
    }

    /**
     * 构建查询条件
     *
     * @param query
     * @return
     */
    private BooleanBuilder buildWhere(ApprovalSupplierQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QApprovalSupplier approvalSupplier = QApprovalSupplier.approvalSupplier;
        if (query.getVendorId() != null) {
            booleanBuilder.and(approvalSupplier.vendorId.eq(query.getVendorId()));
        }
        if (StringUtils.isNotBlank(query.getVendorName())) {
            booleanBuilder.and(approvalSupplier.vendorName.like("%" + query.getVendorName() + "%"));
        }
        if (StringUtils.isNotBlank(query.getCategorySegment5())) {
            booleanBuilder.and(approvalSupplier.categorySegment5.eq(query.getCategorySegment5()));
        } else {
            booleanBuilder.and(approvalSupplier.categorySegment5.isNotEmpty());
        }
        return booleanBuilder;
    }

    @Override
    public ApprovalSupplierDTO queryById(Long id) {
        ApprovalSupplier queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        ApprovalSupplierDTO result = new ApprovalSupplierDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApprovalSupplierDTO save(ApprovalSupplierSaveDTO saveDTO) {
        ApprovalSupplier newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new ApprovalSupplier());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @SneakyThrows
    @Override
    public void export(ApprovalSupplierQuery query, HttpServletResponse response) {
        List<ApprovalSupplierDTO> dtos = queryByPage(query).getContent();
        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "批准供应商", "批准供应商", excelPara.getSimpleHeader(), excelData);
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String importData(MultipartFile multipartFile, ExcelPara excelPara) {
        String batchNo = UUID.randomUUID().toString();
        List<ApprovalSupplierDTO> excelData =
                ExcelUtils.readExcel(multipartFile.getInputStream(), null, ApprovalSupplierDTO.class, excelPara);

        log.info("{}条数据，开始存储数据库！", excelData.size());
        //校验 todo
        repository.saveAll(approvalSupplierDEConvert.toEntity(excelData));
        log.info("存储数据库成功！");

        return batchNo;
    }

    @Override
    public void sync() {
        int skip = 0;
        int limit = 1000;

        for (; ; skip += limit) {
            List<ErpApprovalSupplier> erpApprovalSuppliers = erpApprovalSupplierService.getByLimit(skip, limit);
            if (erpApprovalSuppliers.isEmpty()) {
                break;
            }
            ItemsDTO testFind = getItems(erpApprovalSuppliers.get(0).getItemId());
            threadPoolExecutor.execute(() -> {
                for (ErpApprovalSupplier erpApprovalSupplier : erpApprovalSuppliers) {
                    ItemsDTO itemsDTO = getItems(erpApprovalSupplier.getItemId());
                    String categorySegment5 = "";
                    if (itemsDTO != null && StringUtils.isNotBlank(itemsDTO.getCategorySegment5())) {
                        categorySegment5 = itemsDTO.getCategorySegment5();
                    }

                    lock.lock();
                    try {
                        if (erpApprovalSupplier.getVendorId() == null
                                || erpApprovalSupplier.getVendorName() == null
                        ) {
                            continue;
                        }
                        saveApprovalSupplier(erpApprovalSupplier, categorySegment5);
                    } finally {
                        lock.unlock();
                    }
                }
            });
        }
    }

    @Override
    public void syncFromLov() {
        LovHeader lovHeader = lovHeaderService.queryByCode("Agent Information");
        List<LovLine> lovLines = lovLineService.listByLovHeaderId(lovHeader.getLovHeaderId());
        for (LovLine lovLine : lovLines) {
            for (String categorySegment5 : lovLine.getAttribute2().split(",")) {
                try {
                    LovLineDTO lovLineDTO = LovUtils.get(Long.parseLong(categorySegment5));
                    categorySegment5 = lovLineDTO.getLovName();
                } catch (Exception e) {

                }
                JPAQuery<ApprovalSupplier> where = jpaQueryFactory.select(qApprovalSupplier)
                        .from(qApprovalSupplier);
                where.where(qApprovalSupplier.vendorName.eq(lovLine.getLovValue()));
                where.where(qApprovalSupplier.categorySegment5.eq(categorySegment5));
                ApprovalSupplier approvalSupplier = where.fetchOne();

                if (approvalSupplier == null) {
                    approvalSupplier = new ApprovalSupplier();
                    // 先获取供应商的Id
                    List<ErpSupplier> erpSuppliers = erpSupplierRepository.findByVendorName(lovLine.getLovValue());
                    Long vendorId = lovLine.getLovLineId();
                    if (CollectionUtils.isEmpty(erpSuppliers)) {
                        vendorId = erpSuppliers.get(0).getVendorId();
                    }

                    approvalSupplier.setVendorId(vendorId);
                    approvalSupplier.setVendorName(lovLine.getLovValue());
                    approvalSupplier.setBrand(lovLine.getAttribute1());
                    approvalSupplier.setCategorySegment5(categorySegment5);
                    repository.save(approvalSupplier);
                }
            }
        }
    }

    @Override
    public void dailySync() {
        List<ErpApprovalSupplier> erpApprovalSuppliers =
                erpApprovalSupplierService.findByUpdatedTimeGreaterThan(LocalDateTime.now().minusDays(3L));

        if (erpApprovalSuppliers.isEmpty()) {
            return;
        }
        ItemsDTO testFind = getItems(erpApprovalSuppliers.get(0).getItemId());
        threadPoolExecutor.execute(() -> {
            for (ErpApprovalSupplier erpApprovalSupplier : erpApprovalSuppliers) {
                ItemsDTO itemsDTO = getItems(erpApprovalSupplier.getItemId());
                String categorySegment5 = "";
                if (itemsDTO != null && StringUtils.isNotBlank(itemsDTO.getCategorySegment5())) {
                    categorySegment5 = itemsDTO.getCategorySegment5();
                }

                lock.lock();
                try {
                    if (erpApprovalSupplier.getVendorId() == null
                            || erpApprovalSupplier.getVendorName() == null
                    ) {
                        continue;
                    }
                    saveApprovalSupplier(erpApprovalSupplier, categorySegment5);
                } finally {
                    lock.unlock();
                }
            }
        });
    }

    private void saveApprovalSupplier(ErpApprovalSupplier erpApprovalSupplier, String categorySegment5) {
        ApprovalSupplier approvalSupplier = findApprovalSupplierFromErpInfo(erpApprovalSupplier, categorySegment5);
        if (approvalSupplier == null) {
            approvalSupplier = new ApprovalSupplier();
        }
        approvalSupplier.setVendorId(erpApprovalSupplier.getVendorId());
        approvalSupplier.setVendorName(erpApprovalSupplier.getVendorName());
        approvalSupplier.setCategorySegment5(categorySegment5);
        ErpSupplier erpSupplier = erpSupplierService.findByVendorId(erpApprovalSupplier.getVendorId());
        approvalSupplier.setBrand(Optional.ofNullable(erpSupplier.getAttribute1()).orElse("").trim());
        repository.save(approvalSupplier);
    }

    private ApprovalSupplier findApprovalSupplierFromErpInfo(ErpApprovalSupplier erpApprovalSupplier, String categorySegment5) {
        JPAQuery<ApprovalSupplier> where = jpaQueryFactory.select(qApprovalSupplier)
                .from(qApprovalSupplier);

//        where.where(qApprovalSupplier.vendorId.eq(erpApprovalSupplier.getVendorId()));
        where.where(qApprovalSupplier.vendorName.eq(erpApprovalSupplier.getVendorName()));
        where.where(qApprovalSupplier.categorySegment5.eq(categorySegment5));

        return where.fetchOne();
    }

    private ItemsDTO getItems(Long itemId) {
        if (itemId == null) {
            return null;
        }
        // TODO 找到多个取第一个
        Results<ItemsDTO> body = bomFeign.findItemsBySourceItemId(new IdDTO().setId(itemId.toString())).getBody();
        if (body == null || !body.isSuccess()) {
            return null;
        }
        return body.getData();
    }
}
