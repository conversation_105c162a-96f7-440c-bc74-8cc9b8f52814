package com.trinasolar.scp.system.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.system.domain.dto.AttrTypeHeaderDTO;
import com.trinasolar.scp.system.domain.dto.AttrTypeLineDTO;
import com.trinasolar.scp.system.domain.entity.AttrTypeHeader;
import com.trinasolar.scp.system.domain.entity.QAttrTypeHeader;
import com.trinasolar.scp.system.domain.query.AttrTypeHeaderQuery;
import com.trinasolar.scp.system.domain.save.AttrTypeHeaderSaveDTO;
import com.trinasolar.scp.system.service.repository.AttrTypeHeaderRepository;
import com.trinasolar.scp.system.service.service.AttrTypeHeaderService;
import com.trinasolar.scp.system.service.service.AttrTypeLineService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/4/26
 */
@Service("attrTypeHeaderService")
@Slf4j
@CacheConfig(cacheManager = "scpRedisCacheManager")
public class AttrTypeHeaderServiceImpl implements AttrTypeHeaderService {
    QAttrTypeHeader qAttrTypeHeader = QAttrTypeHeader.attrTypeHeader;

    @Autowired
    AttrTypeHeaderRepository attrTypeHeaderRepository;

    @Autowired
    AttrTypeLineService attrTypeLineService;

    @Autowired
    JPAQueryFactory jpaQueryFactory;

    @Override
    public Page<AttrTypeHeader> queryByPage(AttrTypeHeaderQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        if (StringUtils.isNotEmpty(query.getCode())) {
            booleanBuilder.and(qAttrTypeHeader.attrTypeCode.like("%" + query.getCode() + "%"));
        }
        if (StringUtils.isNotEmpty(query.getName())) {
            booleanBuilder.and(qAttrTypeHeader.attrTypeCnName.like("%" + query.getName() + "%"));
        }
        if (query.getAttrCategoryId() != null) {
            booleanBuilder.and(qAttrTypeHeader.attrCategoryId.eq(query.getAttrCategoryId()));
        }
        if (CollectionUtils.isNotEmpty(query.getAttrCategoryIds())) {
            booleanBuilder.and(qAttrTypeHeader.attrCategoryId.in(query.getAttrCategoryIds()));
        }

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        return attrTypeHeaderRepository.findAll(booleanBuilder, pageable);
    }

    @Override
    public AttrTypeHeaderDTO queryById(Long id) {
        AttrTypeHeader queryObj = attrTypeHeaderRepository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }
        AttrTypeHeaderDTO result = new AttrTypeHeaderDTO();
        BeanUtils.copyProperties(queryObj, result);

        result.setLines(
                attrTypeLineService.getByHeaderId(result.getAttrTypeHeaderId())
        );
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(
            evict = {
                    @CacheEvict(cacheNames = "AttrType_queryById", allEntries = true),
                    @CacheEvict(cacheNames = "AttrType_byHeaderId", allEntries = true)
            }
    )
    public AttrTypeHeaderDTO save(AttrTypeHeaderSaveDTO saveDTO) {
        // 判断Code唯一
        AttrTypeHeader byCode = attrTypeHeaderRepository.getByCode(saveDTO.getAttrTypeCode());
        if (byCode != null) {
            if (!Objects.equals(byCode.getAttrTypeHeaderId(), saveDTO.getAttrTypeHeaderId())) {
                throw new BizException("AttrTypeCode 必须唯一");
            }
        }

        AttrTypeHeader newObj;
        if (saveDTO.getAttrTypeHeaderId() != null) {
            newObj = attrTypeHeaderRepository.getOne(saveDTO.getAttrTypeHeaderId());
        } else {
            newObj = new AttrTypeHeader();
        }

        BeanUtils.copyProperties(saveDTO, newObj);
        attrTypeHeaderRepository.save(newObj);

        attrTypeLineService.savesByHeader(saveDTO.getLines(), newObj);

        return this.queryById(newObj.getAttrTypeHeaderId());
    }

    @Override
    public void deleteById(Long id) {
        attrTypeHeaderRepository.deleteById(id);
    }

    @Override
    public List<AttrTypeHeaderDTO> queryByParams(Integer attrCategoryId, String lastUpdatedTime) {
        List<AttrTypeHeader> queryObjs = attrTypeHeaderRepository.queryAllByParams(attrCategoryId, lastUpdatedTime);
        List<AttrTypeHeaderDTO> attrTypeHeaderDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(queryObjs)) {
            return attrTypeHeaderDTOS;
        }
        queryObjs.stream().forEach(p -> {
            AttrTypeHeaderDTO result = new AttrTypeHeaderDTO();
            BeanUtils.copyProperties(p, result);
            attrTypeHeaderDTOS.add(result);
        });

        return attrTypeHeaderDTOS;
    }

    @Override
    public AttrTypeHeaderDTO getByCategorySegment5(String categorySegment5) {
        AttrTypeHeader attrTypeHeader = jpaQueryFactory.selectFrom(qAttrTypeHeader)
                .where(qAttrTypeHeader.attrTypeCode.like("ATTR_TYPE%"))
                .where(qAttrTypeHeader.attrTypeCnName.eq(categorySegment5))
                .fetchFirst();
        AttrTypeHeaderDTO dto = new AttrTypeHeaderDTO();
        BeanUtils.copyProperties(attrTypeHeader, dto);
        return dto;
    }

    @Override
    public List<AttrTypeLineDTO> findItemTransToLovAttrLines(String categorySegment5) {
        AttrTypeHeaderDTO byCategorySegment5 = getByCategorySegment5(categorySegment5);
        return attrTypeLineService.findItemTransToLovAttrLines(byCategorySegment5);
    }

    @Override
    public List<AttrTypeLineDTO> getByAttrTypeCode(String attrTypeCode) {
        AttrTypeHeader attrTypeHeader = jpaQueryFactory.selectFrom(qAttrTypeHeader)
                .where(qAttrTypeHeader.attrTypeCode.eq(attrTypeCode))
                .fetchFirst();
        List<AttrTypeLineDTO> resultList = new ArrayList<>();
        if (Objects.nonNull(attrTypeHeader)) {
            resultList = attrTypeLineService.getByHeaderId(attrTypeHeader.getAttrTypeHeaderId());
        }
        return resultList;
    }


    @Override
    public List<AttrTypeLineDTO> getByAttrTypeName(String attrTypeName) {
        AttrTypeHeader attrTypeHeader = jpaQueryFactory.selectFrom(qAttrTypeHeader)
                .where(qAttrTypeHeader.attrTypeCnName.eq(attrTypeName).or(qAttrTypeHeader.attrTypeEnName.eq(attrTypeName)))
                .fetchFirst();
        List<AttrTypeLineDTO> resultList = new ArrayList<>();
        if (Objects.nonNull(attrTypeHeader)) {
            resultList = attrTypeLineService.getByHeaderId(attrTypeHeader.getAttrTypeHeaderId());
        }
        return resultList;
    }

    @Override
    public List<AttrTypeHeaderDTO> getByAttrTypeHeaderIds(List<Long> attrTypeHeaderIds) {
        if (CollectionUtils.isEmpty(attrTypeHeaderIds)) {
            return Collections.emptyList();
        }
        List<AttrTypeHeader> headers = attrTypeHeaderRepository.getByAttrTypeHeaderIds(attrTypeHeaderIds);
        if (CollectionUtils.isEmpty(headers)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(headers, AttrTypeHeaderDTO.class);
    }
}
