package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.ErpPeople;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * erp人员信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 15:55:04
 */
@Repository
public interface ErpPeopleRepository extends JpaRepository<ErpPeople, Long>, QuerydslPredicateExecutor<ErpPeople> {
    @Query(value = "SELECT * FROM sys_erp_people AS tb WHERE tb.last_update_date IS NOT NULL AND tb.is_deleted=0 ORDER BY  tb.last_update_date DESC LIMIT 1", nativeQuery = true)
    ErpPeople findLastUpdateObj();

    ErpPeople findByEmployeeNumber(String employeeNumber);
}
