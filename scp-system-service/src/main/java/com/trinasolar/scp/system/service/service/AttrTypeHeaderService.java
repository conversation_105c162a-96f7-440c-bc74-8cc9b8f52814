package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.AttrTypeHeaderDTO;
import com.trinasolar.scp.system.domain.dto.AttrTypeLineDTO;
import com.trinasolar.scp.system.domain.entity.AttrTypeHeader;
import com.trinasolar.scp.system.domain.query.AttrTypeHeaderQuery;
import com.trinasolar.scp.system.domain.save.AttrTypeHeaderSaveDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * lov头表(LovHeader)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-24 10:42:26
 */
public interface AttrTypeHeaderService {

    Page<AttrTypeHeader> queryByPage(AttrTypeHeaderQuery query);

    AttrTypeHeaderDTO queryById(Long id);

    AttrTypeHeaderDTO save(AttrTypeHeaderSaveDTO saveDTO);

    void deleteById(Long id);

    List<AttrTypeHeaderDTO> queryByParams(Integer attrCategoryId, String lastUpdatedTime);

    AttrTypeHeaderDTO getByCategorySegment5(String categorySegment5);

    List<AttrTypeLineDTO> findItemTransToLovAttrLines(String categorySegment5);

    List<AttrTypeLineDTO> getByAttrTypeCode(String attrTypeCode);

    List<AttrTypeLineDTO> getByAttrTypeName(String attrTypeName);

    List<AttrTypeHeaderDTO> getByAttrTypeHeaderIds(List<Long> attrTypeHeaderIds);
}
