package com.trinasolar.scp.system.service.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.scp.system.domain.dto.FileTokenDTO;
import com.trinasolar.scp.system.service.service.FileUploadService;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2022/7/6
 */
@Service("fileUploadService")
@RefreshScope
public class FileUploadServiceImpl implements FileUploadService {

    @Value("${security.oauth2.client.client-id}")
    String clientId;

    @Value("${security.oauth2.client.client-secret}")
    String clientSecret;

    @Value("${security.oauth2.client.client-url}")
    String clientUrl;

    @Autowired
    RestTemplate restTemplate;
    @Autowired
    private SCPFileService fileService;

    @Override
    public FileTokenDTO getToken() {
        String url = clientUrl + "/user?clientId=" + clientId + "&clientSecret=" + clientSecret;
        String json = restTemplate.getForObject(url, String.class);
        JSONObject outJson = JSONObject.parseObject(json);
        JSONObject data = outJson.getJSONObject("data");
        return new FileTokenDTO().setFileToken(data.getString("token"));
    }

    @Override
    @SneakyThrows
    public String upload(MultipartFile file) {
        return fileService.upload(file,false);
    }


}
