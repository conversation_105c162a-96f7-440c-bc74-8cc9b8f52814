package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.ErpSupplier;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * erp供应商表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-22 09:10:40
 */
@Repository
public interface ErpSupplierRepository extends JpaRepository<ErpSupplier, Long>, QuerydslPredicateExecutor<ErpSupplier> {
    List<ErpSupplier> findByVendorId(Long vendorId);

    List<ErpSupplier> findByVendorName(String venderName);

    @Query("select e from ErpSupplier e where e.attribute3 = ?1 order by e.id desc")
    List<ErpSupplier> findByAttribute3OrderById(String legalEntity);

    @Query("select e from ErpSupplier e where e.attribute1 in ?1")
    List<ErpSupplier> findByAttribute1In(Set<String> brands);

    @Query("select e from ErpSupplier e where e.vendorNameAlt = ?1 and(e.endDateActive >now() or e.endDateActive is null) and e.startDateActive < now() ")
    ErpSupplier findByVendorNameAlt(String vendorAltName);

    @Query(nativeQuery = true, value = "SELECT distinct(vendor_name)  FROM scp_system.sys_erp_supplier " +
            "where vendor_type_lookup_code != 'EMPLOYEE' and is_deleted =0")
    List<String> queryAllSupplierName();
}
