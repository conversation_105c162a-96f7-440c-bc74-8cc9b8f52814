package com.trinasolar.scp.system.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.common.api.enums.DeleteEnum;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import com.trinasolar.scp.system.domain.convert.OperatorConfigDEConvert;
import com.trinasolar.scp.system.domain.dto.OperatorConfigDTO;
import com.trinasolar.scp.system.domain.entity.OperatorConfig;
import com.trinasolar.scp.system.domain.entity.QOperatorConfig;
import com.trinasolar.scp.system.domain.query.OperatorConfigQuery;
import com.trinasolar.scp.system.domain.save.OperatorConfigSaveDTO;
import com.trinasolar.scp.system.service.repository.OperatorConfigRepository;
import com.trinasolar.scp.system.service.service.OperatorConfigService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 操作配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-01 09:45:41
 */
@Slf4j
@Service("operatorConfigService")
public class OperatorConfigServiceImpl implements OperatorConfigService {
    @Autowired
    OperatorConfigRepository repository;
    @Autowired
    OperatorConfigDEConvert operatorConfigDEConvert;

    @Override
    public Page<OperatorConfigDTO> queryByPage(OperatorConfigQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<OperatorConfig> operatorConfigPage = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(operatorConfigDEConvert.toDto(operatorConfigPage.getContent()), pageable, operatorConfigPage.getTotalElements());
    }

    /**
     * 构建查询条件
     *
     * @param query
     * @return
     */
    private BooleanBuilder buildWhere(OperatorConfigQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QOperatorConfig operatorConfig = QOperatorConfig.operatorConfig;
        booleanBuilder.and(operatorConfig.isDeleted.eq(DeleteEnum.NO.getCode()));

        if (StringUtils.isNotBlank(query.getMenuName())) {
            booleanBuilder.and(operatorConfig.menuName.eq(query.getMenuName()));
        }
        if (StringUtils.isNotBlank(query.getContentType())) {
            booleanBuilder.and(operatorConfig.contentType.eq(query.getContentType()));
        }
        if (StringUtils.isNotBlank(query.getModuleCode())) {
            booleanBuilder.and(operatorConfig.moduleCode.eq(query.getModuleCode()));
        }
        if (StringUtils.isNotBlank(query.getUserId())) {
            booleanBuilder.and(operatorConfig.userId.eq(query.getUserId()));
        }
        if (StringUtils.isNotBlank(query.getMenuCode())) {
            booleanBuilder.and(operatorConfig.menuCode.eq(query.getMenuCode()));
        }
        return booleanBuilder;
    }

    @Override
    public OperatorConfigDTO queryById(Long id) {
        OperatorConfig queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        OperatorConfigDTO result = new OperatorConfigDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public OperatorConfigDTO save(OperatorConfigSaveDTO saveDTO) {
        QOperatorConfig operatorConfig = QOperatorConfig.operatorConfig;
        saveDTO.setUserId(MyThreadLocal.get().getUserId());

//        ArrayList<OperatorConfig> oldDataList = new ArrayList<>();
        ArrayList<Long> ids = new ArrayList<>();
        repository.findAll(new BooleanBuilder()
                .and(operatorConfig.menuCode.eq(saveDTO.getMenuCode()))
                .and(operatorConfig.menuName.eq(saveDTO.getMenuName()))
                .and(operatorConfig.contentType.eq(saveDTO.getContentType()))
                .and(operatorConfig.userId.eq(saveDTO.getUserId()))).forEach(item->ids.add(item.getId()));
        if(CollectionUtils.isNotEmpty(ids)){
            for (Long id : ids) {
                repository.deleteById(id);
            }
        }

        OperatorConfig newObj = new OperatorConfig();
        BeanUtils.copyProperties(saveDTO, newObj);

        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @SneakyThrows
    @Override
    public void export(OperatorConfigQuery query, HttpServletResponse response) {
        EasyExcel.write(response.getOutputStream(), OperatorConfigDTO.class)
                .sheet()
                .doWrite(() -> queryByPage(query).getContent());
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String importData(MultipartFile multipartFile) {
        String batchNo = UUID.randomUUID().toString();
        EasyExcel.read(multipartFile.getInputStream(), OperatorConfigDTO.class, new ReadListener<OperatorConfigDTO>() {
            /**
             * 单次缓存的数据量
             */
            public static final int BATCH_COUNT = 100;
            /**
             *临时存储
             */
            private List<OperatorConfigDTO> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

            @Override
            public void invoke(OperatorConfigDTO data, AnalysisContext context) {
                cachedDataList.add(data);
                if (cachedDataList.size() >= BATCH_COUNT) {
                    saveData();
                    // 存储完成清理 list
                    cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                saveData();
            }

            /**
             * 保存数据库
             */
            private void saveData() {
                log.info("{}条数据，开始存储数据库！", cachedDataList.size());
                repository.saveAll(operatorConfigDEConvert.toEntity(cachedDataList));
                log.info("存储数据库成功！");
            }
        }).sheet().doRead();

        return batchNo;
    }

    @Override
    public OperatorConfigDTO findByUserMenu(OperatorConfigQuery query) {
        query.setUserId(MyThreadLocal.get().getUserId());
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        BooleanBuilder booleanBuilder = buildWhere(query);
        ArrayList<OperatorConfig> list = Lists.newArrayList();
        repository.findAll(booleanBuilder, sort).forEach(list::add);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        OperatorConfig operatorConfig = list.get(0);
        return operatorConfigDEConvert.toDto(operatorConfig);
    }
}
