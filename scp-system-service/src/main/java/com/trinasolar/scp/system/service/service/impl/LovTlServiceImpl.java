package com.trinasolar.scp.system.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.system.domain.dto.LovLineTlTableLineDTO;
import com.trinasolar.scp.system.domain.dto.LovTlDTO;
import com.trinasolar.scp.system.domain.entity.*;
import com.trinasolar.scp.system.domain.query.LovTlQuery;
import com.trinasolar.scp.system.domain.save.LovLineTlTableLinesSaveDTO;
import com.trinasolar.scp.system.domain.save.LovTlSaveDTO;
import com.trinasolar.scp.system.service.enums.LovTlPkTypeEnum;
import com.trinasolar.scp.system.service.repository.LanguageRepository;
import com.trinasolar.scp.system.service.repository.LovConfigRepository;
import com.trinasolar.scp.system.service.repository.LovTlRepository;
import com.trinasolar.scp.system.service.service.LovCacheService;
import com.trinasolar.scp.system.service.service.LovHeaderService;
import com.trinasolar.scp.system.service.service.LovLineService;
import com.trinasolar.scp.system.service.service.LovTlService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统LOV多语言表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
@Slf4j
@Service("lovTlService")
public class LovTlServiceImpl implements LovTlService {
    static final String REDIS_LOV_TL_KEY = "lov_tl_";

    QLovTl q = QLovTl.lovTl;

    @Autowired
    LovTlRepository lovTlRepository;

    @Autowired
    LovConfigRepository lovConfigRepository;

    @Autowired
    LanguageRepository languageRepository;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    private Map<String, String> lovConfigNames;

    @Autowired
    private LovLineService lovLineService;

    @Autowired
    private LovHeaderService lovHeaderService;

    @Autowired
    private LovCacheService lovCacheService;

    @Override
    public Page<LovTl> queryByPage(LovTlQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (StringUtils.isNotBlank(query.getPkType())) {
            booleanBuilder.and(q.pkType.eq(query.getPkType()));
        }
        if (query.getPkId() != null) {
            booleanBuilder.and(q.pkId.eq(query.getPkId()));
        }

        Sort sort = Sort.by(Sort.Direction.ASC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        return lovTlRepository.findAll(booleanBuilder, pageable);
    }

    @Override
    public LovTlDTO queryById(Long id) {
        LovTl queryObj = lovTlRepository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        LovTlDTO result = new LovTlDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Override
    public LovTlDTO save(LovTlSaveDTO saveDTO) {
        if (!LovTlPkTypeEnum.isInEnum(saveDTO.getPkType())) {
            throw new BizException("PkType只能是: " + LovTlPkTypeEnum.getValues().toString());
        }
        LovTl getByCode;
        if (StringUtils.isBlank(saveDTO.getLovField())) {
            saveDTO.setLovField("lovName"); // 默认name
        }
        getByCode = lovTlRepository.findByPkIdAndPkTypeAndLovFieldAndLang(saveDTO.getPkId(), saveDTO.getPkType(),
                saveDTO.getLovField(), saveDTO.getLang());

        LovTl newObj;
        if (getByCode != null) {
            newObj = getByCode;
        } else {
            newObj = new LovTl();
        }

        BeanUtils.copyProperties(saveDTO, newObj, "id");
        lovTlRepository.save(newObj);

        // 多语言保存后更新缓存
        clearCache(newObj);

        return this.queryById(newObj.getId());
    }

    private void clearCache(LovTl newObj) {
        // 获取头Code用于清除缓存
        String lovCode = null;
        if (newObj.getPkType().equals(LovTlPkTypeEnum.HEADER.getValue())) {
            LovHeader lovHeader = lovHeaderService.findById(newObj.getPkId());
            lovCode = lovHeader.getLovCode();
        } else if (newObj.getPkType().equals(LovTlPkTypeEnum.LINE.getValue())) {
            LovLine lovLine = Optional.of(lovLineService.findById(newObj.getPkId()))
                    .orElseThrow(() -> new BizException("找不到Lov行"));
            LovHeader lovHeader = lovHeaderService.findById(lovLine.getLovHeaderId());
            lovCode = lovHeader.getLovCode();
        } else if (newObj.getPkType().equals(LovTlPkTypeEnum.CONFIG.getValue())) {
            LovConfig lovConfig = lovConfigRepository.getOne(newObj.getPkId());
            LovHeader lovHeader = lovHeaderService.findById(lovConfig.getLovHeaderId());
            lovCode = lovHeader.getLovCode();
        }

        // 清除缓存
        lovCacheService.clearLovHeaderCache(lovCode);
    }

    @Override
    public void deleteById(Long id) {
        lovTlRepository.deleteById(id);
    }

    @Override
    public Map<Long, String> mapByLangAndPkTypeAndPkIds(String lang, String pkType, List<Long> pkIds) {
        Map<Long, String> result = new HashMap<>();

        ArrayList<Long> nonFindCache = new ArrayList<>();
        for (Long pkId : pkIds) {
            if (pkId == null) {
                continue;
            }
            String name = (String) stringRedisTemplate.opsForHash().get(REDIS_LOV_TL_KEY + lang, pkId.toString());
            if (name == null) {
                nonFindCache.add(pkId);
            } else {
                result.put(pkId, name);
            }
        }

        if (!nonFindCache.isEmpty()) {
            List<LovTl> lovTls = lovTlRepository.listByLangAndPkTypeAndPkIds(lang, pkType, nonFindCache);
            for (LovTl lovTl : lovTls) {
                stringRedisTemplate.opsForHash().put(REDIS_LOV_TL_KEY + lang, lovTl.getPkId().toString(), lovTl.getName());
            }
        }

        return result;
    }

    @Override
    public List<LovTlDTO> findAllByPkTypeAndPkIdIn(LovTlQuery query) {
        List<LovTl> tls = lovTlRepository.findAllByPkTypeAndPkIdIn(query.getPkType(), query.getPkIds());

        return tls.stream().map(item -> {
            LovTlDTO lovTlDTO = new LovTlDTO();
            BeanUtils.copyProperties(item, lovTlDTO);
            return lovTlDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<LovLineTlTableLineDTO> getLovLineTlTableLines(IdDTO idDTO) {
        List<Language> languages = languageRepository.findAll();
        // id是Lov的Id 先获取Lov的行
        LovLine lovLine = Optional.of(lovLineService.findById(Long.parseLong(idDTO.getId())))
                .orElseThrow(() -> new BizException("找不到Lov行"));

        List<LovConfig> lovConfigs = lovConfigRepository.listByHeaderId(lovLine.getLovHeaderId());
        lovConfigNames = new HashMap<>();
        for (LovConfig lovConfig : lovConfigs) {
            lovConfigNames.put(lovConfig.getAttrColName().trim().toLowerCase(), lovConfig.getAttrColAlias());
        }

        // 将lov行中所有有值的字段解析成 LovLineTlTableLine
        List<LovLineTlTableLineDTO> lines = new ArrayList<>();
        Optional.of(parseLovLineTlTableLine(lovLine, "lovName", lovLine.getLovName(), languages))
                .ifPresent(lines::addAll);
        Optional.of(parseLovLineTlTableLine(lovLine, "attribute1", lovLine.getAttribute1(), languages))
                .ifPresent(lines::addAll);
        Optional.of(parseLovLineTlTableLine(lovLine, "attribute2", lovLine.getAttribute2(), languages))
                .ifPresent(lines::addAll);
        Optional.of(parseLovLineTlTableLine(lovLine, "attribute3", lovLine.getAttribute3(), languages))
                .ifPresent(lines::addAll);
        Optional.of(parseLovLineTlTableLine(lovLine, "attribute4", lovLine.getAttribute4(), languages))
                .ifPresent(lines::addAll);
        Optional.of(parseLovLineTlTableLine(lovLine, "attribute5", lovLine.getAttribute5(), languages))
                .ifPresent(lines::addAll);
        Optional.of(parseLovLineTlTableLine(lovLine, "attribute6", lovLine.getAttribute6(), languages))
                .ifPresent(lines::addAll);
        Optional.of(parseLovLineTlTableLine(lovLine, "attribute7", lovLine.getAttribute7(), languages))
                .ifPresent(lines::addAll);
        Optional.of(parseLovLineTlTableLine(lovLine, "attribute8", lovLine.getAttribute8(), languages))
                .ifPresent(lines::addAll);
        Optional.of(parseLovLineTlTableLine(lovLine, "attribute9", lovLine.getAttribute9(), languages))
                .ifPresent(lines::addAll);
        Optional.of(parseLovLineTlTableLine(lovLine, "attribute10", lovLine.getAttribute10(), languages))
                .ifPresent(lines::addAll);
        Optional.of(parseLovLineTlTableLine(lovLine, "attribute11", lovLine.getAttribute11(), languages))
                .ifPresent(lines::addAll);
        Optional.of(parseLovLineTlTableLine(lovLine, "attribute12", lovLine.getAttribute12(), languages))
                .ifPresent(lines::addAll);
        Optional.of(parseLovLineTlTableLine(lovLine, "attribute13", lovLine.getAttribute13(), languages))
                .ifPresent(lines::addAll);
        Optional.of(parseLovLineTlTableLine(lovLine, "attribute14", lovLine.getAttribute14(), languages))
                .ifPresent(lines::addAll);
        Optional.of(parseLovLineTlTableLine(lovLine, "attribute15", lovLine.getAttribute15(), languages))
                .ifPresent(lines::addAll);

        return lines;
    }

    @Override
    @Transactional
    public void saveLovLineTlTableLines(LovLineTlTableLinesSaveDTO saveDTO) {
        List<LovLineTlTableLineDTO> lines = saveDTO.getLovLineTlTableLines();
        List<LovLineTlTableLineDTO> collect = lines.stream().filter((line) -> StringUtils.isNotBlank(line.getName()))
                .collect(Collectors.toList());

        if (collect.isEmpty()) {
            return;
        }

        for (LovLineTlTableLineDTO lovLineTlTableLineDTO : collect) {
            LovTlSaveDTO lovTlSaveDTO = new LovTlSaveDTO();
            BeanUtils.copyProperties(lovLineTlTableLineDTO, lovTlSaveDTO);
            save(lovTlSaveDTO);
        }
    }

    private List<LovLineTlTableLineDTO> parseLovLineTlTableLine(LovLine lovLine, String field, String fieldValue, List<Language> languages) {
        if (StringUtils.isBlank(fieldValue)) {
            return new ArrayList<>();
        }
        List<LovLineTlTableLineDTO> result = new ArrayList<>();

        for (Language language : languages) {
            LovLineTlTableLineDTO lovLineTlTableLineDTO = new LovLineTlTableLineDTO();
            lovLineTlTableLineDTO.setPkId(lovLine.getLovLineId());
            lovLineTlTableLineDTO.setPkType("line");
            lovLineTlTableLineDTO.setLovField(field);
            lovLineTlTableLineDTO.setLovFieldName(lovConfigNames.get(field));
            lovLineTlTableLineDTO.setLovFieldValue(fieldValue);
            lovLineTlTableLineDTO.setLang(language.getCode());
            lovLineTlTableLineDTO.setLangName(language.getName());
            LovTl lovTl = lovTlRepository.findByPkIdAndPkTypeAndLovFieldAndLang(
                    lovLineTlTableLineDTO.getPkId(),
                    lovLineTlTableLineDTO.getPkType(),
                    lovLineTlTableLineDTO.getLovField(),
                    lovLineTlTableLineDTO.getLang()
            );
            if (lovTl != null) {
                lovLineTlTableLineDTO.setId(lovTl.getId());
                lovLineTlTableLineDTO.setName(lovTl.getName());
                lovLineTlTableLineDTO.setDescription(lovTl.getDescription());
            }
            result.add(lovLineTlTableLineDTO);
        }
        return result;
    }
//
//    @Override
//    public Map<Long, String> mapByLangAndPkTypeAndPkIds(String lang, String pkType, List<Long> pkIds) {
//        String hKey = REDIS_LOV_TL_KEY + lang;
//        Map<Long, String> result = new HashMap<>();
//
//        ArrayList<Long> nonFindCache = new ArrayList<>();
//
//        //字符串类型可以直接使用multiGet
//        /*List<Object> keys = new ArrayList<>();
//        for (Long pkId : pkIds) {
//            String key = pkId.toString();
//            keys.add(key);
//        }
//        List<Object> resultStr = stringRedisTemplate.opsForHash().multiGet(hKey, keys);*/
//
//
//        //对象类型可以使用pipeline,减少Redis连接次数，此处统一使用pipeline
//        stringRedisTemplate.executePipelined(new RedisCallback<String>() {
//            @Override
//            public String doInRedis(RedisConnection redisConnection) throws DataAccessException {
//                StringRedisConnection stringRedisConnection = (StringRedisConnection) redisConnection;
//                for (Long pkId : pkIds) {
//                    String name = (String) stringRedisConnection.hGet(hKey, pkId.toString());
//                    if (name == null) {
//                        nonFindCache.add(pkId);
//                    } else {
//                        result.put(pkId, name);
//                    }
//                }
//                return null;
//            }
//        });
//
//
//       /* for (Long pkId : pkIds) {
//            String name = (String) stringRedisTemplate.opsForHash().get(REDIS_LOV_TL_KEY + lang, pkId.toString());
//            if (name == null) {
//                nonFindCache.add(pkId);
//            } else {
//                result.put(pkId, name);
//            }
//        }
//*/
//        if (!nonFindCache.isEmpty()) {
//            List<LovTl> lovTls = lovTlRepository.listByLangAndPkTypeAndPkIds(lang, pkType, nonFindCache);
//            Map<String, String> map = new HashMap<>();
//            for (LovTl lovTl : lovTls) {
//                map.put(lovTl.getPkId().toString(), lovTl.getName());
//                //stringRedisTemplate.opsForHash().put(hKey, lovTl.getPkId().toString(), lovTl.getName());
//            }
//            //对象类型可以使用pipeline,减少Redis连接次数，此处统一使用pipeline
//            stringRedisTemplate.executePipelined(new RedisCallback<String>() {
//                @Override
//                public String doInRedis(RedisConnection redisConnection) throws DataAccessException {
//                    StringRedisConnection stringRedisConnection = (StringRedisConnection) redisConnection;
//                    stringRedisConnection.hMSet(hKey, map);
//                    return null;
//                }
//            });
//        }
//
//        return result;
//    }
}
