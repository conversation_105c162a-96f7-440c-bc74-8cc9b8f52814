package com.trinasolar.scp.system.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/4/25
 */
@AllArgsConstructor
@Getter
public enum LovTypeEnum {
    /**
     * lovHead表中 lov_type_id 的实现
     */
    VALUE_LIST(1, "值列表"),
    DYNAMIC_SCRIPT(2, "动态结果集");
    private Integer value;
    private String name;

    public static LovTypeEnum getByValue(Integer value) {
        for (LovTypeEnum lovHeaderEnum : LovTypeEnum.values()) {
            if (Objects.equals(lovHeaderEnum.getValue(), value)) {
                return lovHeaderEnum;
            }
        }
        return null;
    }
}
