package com.trinasolar.scp.system.service.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ibm.dpf.base.core.util.StringUtils;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.system.domain.convert.OperatingUnitsDEConvert;
import com.trinasolar.scp.system.domain.dto.ExternalInterfaceDTO;
import com.trinasolar.scp.system.domain.dto.OperatingUnitsDTO;
import com.trinasolar.scp.system.domain.entity.OperatingUnits;
import com.trinasolar.scp.system.domain.entity.QOperatingUnits;
import com.trinasolar.scp.system.domain.query.OperatingUnitsQuery;
import com.trinasolar.scp.system.domain.save.OperatingUnitsSaveDTO;
import com.trinasolar.scp.system.service.repository.OperatingUnitsRepository;
import com.trinasolar.scp.system.service.service.ErpInterfaceService;
import com.trinasolar.scp.system.service.service.OperatingUnitsService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 业务实体
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-07 17:43:44
 */
@Slf4j
@Service("operatingUnitsService")
@RefreshScope
public class OperatingUnitsServiceImpl implements OperatingUnitsService {
    private static final int PAGE_SIZE = 600;

    @Autowired
    OperatingUnitsRepository repository;
    @Autowired
    OperatingUnitsDEConvert operatingUnitsDEConvert;

    @Autowired
    ErpInterfaceService erpInterfaceService;

    @Value("${erp.interface.platform.url}")
    private String erpUrl;

    @Value("${erp.interface.ca004.clientid}")
    private String ca004ClientId;

    @Value("${erp.interface.ca004.secret}")
    private String ca004Secret;

    @Override
    public Page<OperatingUnitsDTO> queryByPage(OperatingUnitsQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<OperatingUnits> operatingUnitsPage = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(operatingUnitsDEConvert.toDto(operatingUnitsPage.getContent()), pageable, operatingUnitsPage.getTotalElements());
    }

    /**
     * 构建查询条件
     *
     * @param query
     * @return
     */
    private BooleanBuilder buildWhere(OperatingUnitsQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QOperatingUnits operatingUnits = QOperatingUnits.operatingUnits;
        if (CollectionUtils.isNotEmpty(query.getOrgIds())) {
            booleanBuilder.and(operatingUnits.orgId.in(query.getOrgIds()));
        }
        if (StringUtils.isNotEmpty(query.getOrgCode())) {
            booleanBuilder.and(operatingUnits.orgCode.likeIgnoreCase("%" + query.getOrgCode() + "%"));
        }
        if (StringUtils.isNotEmpty(query.getOrgName())) {
            booleanBuilder.and(operatingUnits.orgName.likeIgnoreCase("%" + query.getOrgName() + "%"));
        }
        if (StringUtils.isNotEmpty(query.getScpFlag())) {
            booleanBuilder.and(operatingUnits.scpFlag.eq(query.getScpFlag()));
        }
        return booleanBuilder;
    }

    @Override
    public OperatingUnitsDTO queryById(Long id) {
        OperatingUnits queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        OperatingUnitsDTO result = new OperatingUnitsDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public OperatingUnitsDTO save(OperatingUnitsSaveDTO saveDTO) {
        OperatingUnits newObj = Optional.ofNullable(saveDTO.getOrgId())
                .map(id -> repository.getOne(saveDTO.getOrgId()))
                .orElse(new OperatingUnits());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getOrgId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @SneakyThrows
    @Override
    public void export(OperatingUnitsQuery query, HttpServletResponse response) {
        List<OperatingUnitsDTO> dtos = queryByPage(query).getContent();
        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "业务实体", "业务实体", excelPara.getSimpleHeader(), excelData);
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String importData(MultipartFile multipartFile, ExcelPara excelPara) {
        String batchNo = UUID.randomUUID().toString();
        List<OperatingUnitsDTO> excelData =
                ExcelUtils.readExcel(multipartFile.getInputStream(), null, OperatingUnitsDTO.class, excelPara);

        log.info("{}条数据，开始存储数据库！", excelData.size());
        //校验 todo
        repository.saveAll(operatingUnitsDEConvert.toEntity(excelData));
        log.info("存储数据库成功！");

        return batchNo;
    }

    @Override
    public List<OperatingUnitsDTO> queryByList(OperatingUnitsQuery query) {
        BooleanBuilder booleanBuilder = buildWhereDto(query);

        return operatingUnitsDEConvert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
    }

    private BooleanBuilder buildWhereDto(OperatingUnitsQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QOperatingUnits operatingUnits = QOperatingUnits.operatingUnits;
        if (CollectionUtils.isNotEmpty(query.getOrgIds())) {
            booleanBuilder.and(operatingUnits.orgId.in(query.getOrgIds()));
        }
        return booleanBuilder;
    }

    @Override
    public void sync() {
        // 获取最后更新时间
        JSONObject param = new JSONObject();
        param.put("last_update_date_from", "2001-01-01 00:00:00");
        param.put("last_update_date_to", LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
        param.put("pageSize", PAGE_SIZE);
        int pageNum = 1;

        for (; ; ) {
            param.put("pageNum", pageNum++);

            ExternalInterfaceDTO dto = new ExternalInterfaceDTO();
            dto.setUrl(erpUrl);
            dto.setPath("/ca003/v1/hrOperatingUnit/list");
            dto.setClientId(ca004ClientId);
            dto.setSecret(ca004Secret);
            dto.setRequestBody(param.toJSONString());

            String json = erpInterfaceService.postForString(dto);
            JSONObject outJson = JSON.parseObject(json);
            JSONArray datas = outJson.getJSONArray("data");
            if (datas == null) {
                break;
            }

            List<OperatingUnits> list = datas.toJavaList(OperatingUnits.class);
            for (OperatingUnits obj : list) {
                obj.setOrgId(Long.parseLong(obj.getOrgCode()));
                // 查找有没有相同的
                OperatingUnits findObj = repository.findById(obj.getOrgId()).orElse(null);
                if (findObj == null || findObj.getOrgId() == null) {
                    repository.save(obj);
                } else {
                    BeanUtils.copyProperties(obj, findObj, "orgId","scpFlag");
                    repository.save(findObj);
                }
            }
        }
    }
}
