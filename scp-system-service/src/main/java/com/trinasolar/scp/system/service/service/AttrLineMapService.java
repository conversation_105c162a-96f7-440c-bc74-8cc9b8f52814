package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.AttrLineMapDTO;
import com.trinasolar.scp.system.domain.query.AttrLineMapQuery;

import java.util.List;

/**
 * 属性行来源映射表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-05 19:21:39
 */
public interface AttrLineMapService {

    /**
     * 根据主键获取属性行来源映射表详情
     *
     * @param id 主键
     * @return 属性行来源映射表详情
     */
    AttrLineMapDTO queryById(Long id);

    /**
     * 通过属性行id查询多条数据
     *
     * @param query
     * @return
     */
    List<AttrLineMapDTO> getByAttrLineId(AttrLineMapQuery query);

    /**
     * 批量保存
     *
     * @param attrLineMapDTOList
     */
    void batchSave(List<AttrLineMapDTO> attrLineMapDTOList);

    /**
     * 单行删除
     *
     * @param id
     */
    void deleteById(Long id);

    /**
     * 通过多条属性行id查询多条数据
     * @param attrLineIds
     * @return
     */
    List<AttrLineMapDTO> getByAttrLineIds(List<Long> attrLineIds);
}

