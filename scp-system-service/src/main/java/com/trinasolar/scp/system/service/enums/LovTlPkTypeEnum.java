package com.trinasolar.scp.system.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/29
 */
@AllArgsConstructor
@Getter
public enum LovTlPkTypeEnum {
    /**
     * 配置
     */
    CONFIG("config"),
    /**
     * 头
     */
    HEADER("header"),
    /**
     * 行
     */
    LINE("line");
    private String value;

    public static LovTlPkTypeEnum valueOf2(String value) {
        for (LovTlPkTypeEnum pkTypeEnum : LovTlPkTypeEnum.values()) {
            if (Objects.equals(pkTypeEnum.getValue(), value)) {
                return pkTypeEnum;
            }
        }
        return null;
    }

    public static List<String> getValues() {
        return Arrays.stream(LovTlPkTypeEnum.values()).map(LovTlPkTypeEnum::getValue).collect(Collectors.toList());
    }

    public static boolean isInEnum(String code) {
        for (LovTlPkTypeEnum databaseType : LovTlPkTypeEnum.values()) {
            if (databaseType.getValue().equals(code)) {
                return true;
            }
        }
        return false;
    }


}
