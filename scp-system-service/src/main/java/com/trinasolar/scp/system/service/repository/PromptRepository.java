package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.Prompt;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 前端多语言配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
@Repository
public interface PromptRepository
        extends JpaRepository<Prompt, Long>, QuerydslPredicateExecutor<Prompt> {
    @Query(value = "SELECT * FROM sys_prompt AS tb WHERE tb.lang= ?1 AND tb.code= ?2 AND tb.is_deleted=0",
            nativeQuery = true)
    Prompt getByLangAndCode(String lang, String code);

    @Query(value = "SELECT * FROM sys_prompt AS tb WHERE  if(IFNULL(?2, '') != '' , tb.`key` = ?2 , 1 = 1 ) and  tb.lang= ?1 AND tb.is_deleted=0",
            nativeQuery = true)
    List<Prompt> findByLangAndKey(String lang, String key);

    List<Prompt> findByLang(String lang);

}
