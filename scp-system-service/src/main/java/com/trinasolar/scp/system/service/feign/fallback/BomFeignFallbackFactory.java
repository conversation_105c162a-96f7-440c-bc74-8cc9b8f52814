package com.trinasolar.scp.system.service.feign.fallback;

import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.ItemAttrLovDTO;
import com.trinasolar.scp.system.domain.feign.ItemCodesQuery;
import com.trinasolar.scp.system.domain.feign.ItemsDTO;
import com.trinasolar.scp.system.domain.feign.ItemsOptionQuery;
import com.trinasolar.scp.system.domain.feign.MainMaterialInfoSaveDTO;
import com.trinasolar.scp.system.service.feign.BomFeign;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: BomFeignFallbackFactory.java, Created by IntelliJ IDEA
 * @Description: TODO
 * @Author: wang_jun
 * @Date: 2022/7/6 14:23
 * @Version :1.0
 **/
public class BomFeignFallbackFactory implements BomFeign {

    @Override
    public ResponseEntity<Results<List<ItemAttrLovDTO>>> queryListBySrcAttrId(IdDTO idDTO) {
        return Results.createFailRes();
    }

    @Override
    public ResponseEntity<Results<Object>> itemAttrLovSync() {
        return Results.createFailRes();
    }

    @Override
    public ResponseEntity<Results<Object>> itemAttrSync() {
        return Results.createFailRes();
    }

    @Override
    public ResponseEntity<Results<ItemsDTO>> findItemsBySourceItemId(IdDTO idDTO) {
        return Results.createFailRes();
    }

    @Override
    public ResponseEntity<Results<Map<String, String>>> findItemDescByItemCodes(ItemCodesQuery query) {
        return Results.createFailRes();
    }

    @Override
    public ResponseEntity<Results<MainMaterialInfoSaveDTO>> getMaterialInfoByItemCode(ItemsOptionQuery query) {
        return Results.createFailRes();
    }
}
