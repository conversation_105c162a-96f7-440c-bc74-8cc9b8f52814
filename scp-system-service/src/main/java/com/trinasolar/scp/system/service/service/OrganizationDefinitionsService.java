package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.OrganizationDefinitionsDTO;
import com.trinasolar.scp.system.domain.query.OrganizationDefinitionsQuery;
import com.trinasolar.scp.system.domain.save.OrganizationDefinitionsSaveDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-28 11:38:58
 */
public interface OrganizationDefinitionsService {
    Page<OrganizationDefinitionsDTO> queryByPage(OrganizationDefinitionsQuery query);

    OrganizationDefinitionsDTO queryById(Long id);

    OrganizationDefinitionsDTO save(OrganizationDefinitionsSaveDTO saveDTO);

    void deleteById(Long id);

    OrganizationDefinitionsDTO queryByCode(String code);

    /**
     * 按条件查询列表数据
     *
     * @param query
     * @return
     */
    List<OrganizationDefinitionsDTO> queryByList(OrganizationDefinitionsQuery query);

    void updateFlag(OrganizationDefinitionsSaveDTO saveDTO);

    void sync();

    List<OrganizationDefinitionsDTO> listForAps(OrganizationDefinitionsQuery query);
}

