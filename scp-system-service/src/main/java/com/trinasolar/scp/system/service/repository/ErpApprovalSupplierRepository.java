package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.ErpApprovalSupplier;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 批准供应商
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:05:46
 */
@Repository
public interface ErpApprovalSupplierRepository extends JpaRepository<ErpApprovalSupplier, Long>, QuerydslPredicateExecutor<ErpApprovalSupplier> {
    ErpApprovalSupplier findByOrganizationIdAndItemIdAndVendorId(Long organizationId, Long itemId, Long vendorId);

    List<ErpApprovalSupplier> findByUpdatedTimeGreaterThan(LocalDateTime updateTime);

    List<ErpApprovalSupplier> findByItemNumAndVendorName(String itemNum, String vendorName);

    List<ErpApprovalSupplier> findByItemNum(String itemCode);

    @Query(nativeQuery = true, value = "SELECT distinct " +
            "sup.item_id,\n" +
            "       sup.item_num,\n" +
            "       sup.vendor_id,\n" +
            "       sup.vendor_name,\n" +
            "       ses.attribute1,\n" +
            "       ses.attribute2,\n" +
            "       sup.asl_status\n" +
            "FROM sys_erp_approval_supplier sup\n LEFT JOIN sys_erp_supplier ses ON sup.vendor_id = ses.vendor_id" +
            " WHERE sup.is_deleted = 0\n" +
            "  AND sup.asl_status IN ('批准', '研发')\n" +
            "  AND sup.vendor_id IS NOT NULL" +
            "  and sup.item_num = :itemNums")
    List<Map<String, Object>> queryApprovalSupplierByItemNum(@Param("itemNums") String itemNums);
}
