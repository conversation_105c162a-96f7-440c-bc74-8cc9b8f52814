package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.system.domain.dto.DataPrivilegeDTO;
import com.trinasolar.scp.system.domain.query.DataPrivilegeQuery;
import com.trinasolar.scp.system.domain.save.DataPrivilegeSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 数据权限表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-17 15:39:12
 */
public interface DataPrivilegeService {
    /**
     * 分页获取数据权限表
     *
     * @param query 查询对象
     * @return 数据权限表分页对象
     */
    Page<DataPrivilegeDTO> queryByPage(DataPrivilegeQuery query);

    /**
     * 根据主键获取数据权限表详情
     *
     * @param id 主键
     * @return 数据权限表详情
     */
    DataPrivilegeDTO queryById(Long id);

    /**
     * 保存或更新数据权限表
     *
     * @param saveDTO 数据权限表保存对象
     * @return 数据权限表对象
     */
    DataPrivilegeDTO save(DataPrivilegeSaveDTO saveDTO);

    /**
     * 批量保存或更新数据权限表
     *
     * @param dtos 数据权限表保存对象
     * @return 数据权限表对象
     */
    void batchSave(List<DataPrivilegeSaveDTO> dtos);

    /**
     * 根据主键逻辑删除数据权限表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query
     * @param response
     */
    void export(DataPrivilegeQuery query, HttpServletResponse response);

    /**
     * 导入
     *
     * @param multipartFile
     * @return 批次号
     */
    String importData(MultipartFile multipartFile, ExcelPara excelPara);

    /**
     * 获取相应数据权限列表
     *
     * @param query
     * @return
     */
    List<DataPrivilegeDTO> queryByList(DataPrivilegeQuery query);

    /**
     * 获取数据权限
     * @param privilegeType
     * @return
     */
    List<String> getPrivilegeCodes(String privilegeType);
}

