package com.trinasolar.scp.system.service.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.system.domain.convert.ErpPeopleDEConvert;
import com.trinasolar.scp.system.domain.dto.ErpPeopleDTO;
import com.trinasolar.scp.system.domain.dto.ExternalInterfaceDTO;
import com.trinasolar.scp.system.domain.entity.ErpPeople;
import com.trinasolar.scp.system.domain.entity.QErpPeople;
import com.trinasolar.scp.system.domain.query.ErpPeopleQuery;
import com.trinasolar.scp.system.domain.save.ErpPeopleSaveDTO;
import com.trinasolar.scp.system.service.enums.Constants;
import com.trinasolar.scp.system.service.repository.ErpPeopleRepository;
import com.trinasolar.scp.system.service.service.ErpInterfaceService;
import com.trinasolar.scp.system.service.service.ErpPeopleService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * erp人员信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 15:55:04
 */
@Slf4j
@Service("erpPeopleService")
@RefreshScope
public class ErpPeopleServiceImpl implements ErpPeopleService {
    @Autowired
    ErpPeopleRepository repository;
    @Autowired
    ErpPeopleDEConvert erpPeopleDEConvert;
    @Autowired
    ErpInterfaceService erpInterfaceService;

    @Value("${erp.interface.platform.url}")
    private String erpUrl;

    @Value("${erp.interface.ca001.clientid}")
    private String ca001ClientId;

    @Value("${erp.interface.ca001.secret}")
    private String ca001Secret;

    @Override
    public Page<ErpPeopleDTO> queryByPage(ErpPeopleQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<ErpPeople> erpPeoplePage = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(erpPeopleDEConvert.toDto(erpPeoplePage.getContent()), pageable, erpPeoplePage.getTotalElements());
    }

    /**
     * 构建查询条件
     *
     * @param query
     * @return
     */
    private BooleanBuilder buildWhere(ErpPeopleQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QErpPeople erpPeople = QErpPeople.erpPeople;
        if (StringUtils.isNotBlank(query.getEmail())) {
            booleanBuilder.and(erpPeople.email.eq(query.getEmail()));
        }
        return booleanBuilder;
    }

    @Override
    public ErpPeopleDTO queryById(Long id) {
        ErpPeople queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        ErpPeopleDTO result = new ErpPeopleDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ErpPeopleDTO save(ErpPeopleSaveDTO saveDTO) {
        ErpPeople newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new ErpPeople());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @SneakyThrows
    @Override
    public void export(ErpPeopleQuery query, HttpServletResponse response) {
        List<ErpPeopleDTO> dtos = queryByPage(query).getContent();
        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "erp人员信息", "erp人员信息", excelPara.getSimpleHeader(), excelData);
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String importData(MultipartFile multipartFile, ExcelPara excelPara) {
        String batchNo = UUID.randomUUID().toString();
        List<ErpPeopleDTO> excelData =
                ExcelUtils.readExcel(multipartFile.getInputStream(), null, ErpPeopleDTO.class, excelPara);

        log.info("{}条数据，开始存储数据库！", excelData.size());
        //校验 todo
        repository.saveAll(erpPeopleDEConvert.toEntity(excelData));
        log.info("存储数据库成功！");

        return batchNo;
    }

    @Override
    public void sync() {
        // 获取最后更新时间
        ErpPeople lastUpdateObj = repository.findLastUpdateObj();
        JSONObject param = new JSONObject();
        if (lastUpdateObj == null) {
            param.put("last_update_date_from", "2001-01-01 00:00:00");
        } else {
            param.put("last_update_date_from", lastUpdateObj.getLastUpdateDate());
        }
        param.put("last_update_date_to", LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
        param.put("pageSize", Constants.PAGE_SIZE);
        int pageNum = 1;
        for (; ; ) {
            param.put("pageNum", pageNum++);

            ExternalInterfaceDTO dto = new ExternalInterfaceDTO();
            dto.setUrl(erpUrl);
            dto.setPath("/ca001/v1/peopleInfo/list");
            dto.setClientId(ca001ClientId);
            dto.setSecret(ca001Secret);
            dto.setRequestBody(param.toJSONString());

            String json = erpInterfaceService.postForString(dto);
            JSONObject outJson = JSON.parseObject(json);
            JSONArray datas = outJson.getJSONArray("data");
            if (datas == null) {
                break;
            }
            List<ErpPeople> list = datas.toJavaList(ErpPeople.class);
            for (ErpPeople obj : list) {
                // 查找有没有相同的
                ErpPeople findObj = repository.findByEmployeeNumber(obj.getEmployeeNumber());
                if (findObj == null) {
                    repository.save(obj);
                } else {
                    BeanUtils.copyProperties(obj, findObj, "id");
                    repository.save(findObj);
                }
            }
        }

    }

    @Override
    public List<ErpPeopleDTO> queryByList(ErpPeopleQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);

        return erpPeopleDEConvert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
    }
}
