package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.Language;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 系统语言
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:17
 */
@Repository
public interface LanguageRepository
        extends JpaRepository<Language, Long>, QuerydslPredicateExecutor<Language> {

    @Query(value = "SELECT * FROM sys_language AS tb WHERE tb.code= ?1 AND tb.is_deleted=0", nativeQuery = true)
    Language getByCode(String code);
}
