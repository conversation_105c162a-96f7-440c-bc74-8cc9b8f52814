package com.trinasolar.scp.system.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.system.domain.convert.ApiLogDEConvert;
import com.trinasolar.scp.system.domain.dto.ApiLogDTO;
import com.trinasolar.scp.system.domain.entity.ApiLog;
import com.trinasolar.scp.system.domain.entity.QApiLog;
import com.trinasolar.scp.system.domain.query.ApiLogQuery;
import com.trinasolar.scp.system.domain.save.ApiLogSaveDTO;
import com.trinasolar.scp.system.service.repository.ApiLogRepository;
import com.trinasolar.scp.system.service.service.ApiLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 推送数据至其他系统接口日志表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-18 09:26:45
 */
@Slf4j
@Service("apiLogService")
public class ApiLogServiceImpl implements ApiLogService {
    @Autowired
    ApiLogRepository repository;
    @Autowired
    ApiLogDEConvert apiLogDEConvert;

    @Override
    public Page<ApiLogDTO> queryByPage(ApiLogQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<ApiLog> apiLogPage = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(apiLogDEConvert.toDto(apiLogPage.getContent()), pageable, apiLogPage.getTotalElements());
    }

    /**
     * 构建查询条件
     *
     * @param query
     * @return
     */
    private BooleanBuilder buildWhere(ApiLogQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QApiLog apiLog = QApiLog.apiLog;

        return booleanBuilder;
    }

    @Override
    public ApiLogDTO queryById(Long id) {
        ApiLog queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        ApiLogDTO result = new ApiLogDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApiLogDTO save(ApiLogSaveDTO saveDTO) {
        ApiLog newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new ApiLog());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }
}
