package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.system.domain.dto.OperatingUnitsDTO;
import com.trinasolar.scp.system.domain.query.OperatingUnitsQuery;
import com.trinasolar.scp.system.domain.save.OperatingUnitsSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 业务实体 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-07 17:43:44
 */
public interface OperatingUnitsService {
    /**
     * 分页获取业务实体
     *
     * @param query 查询对象
     * @return 业务实体分页对象
     */
    Page<OperatingUnitsDTO> queryByPage(OperatingUnitsQuery query);

    /**
     * 根据主键获取业务实体详情
     *
     * @param id 主键
     * @return 业务实体详情
     */
    OperatingUnitsDTO queryById(Long id);

    /**
     * 保存或更新业务实体
     *
     * @param saveDTO 业务实体保存对象
     * @return 业务实体对象
     */
    OperatingUnitsDTO save(OperatingUnitsSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除业务实体
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query
     * @param response
     */
    void export(OperatingUnitsQuery query, HttpServletResponse response);

    /**
     * 导入
     *
     * @param multipartFile
     * @return 批次号
     */
    String importData(MultipartFile multipartFile, ExcelPara excelPara);

    /**
     * 按条件查询列表数据
     *
     * @param query
     * @return
     */
    List<OperatingUnitsDTO> queryByList(OperatingUnitsQuery query);

    void sync();
}

