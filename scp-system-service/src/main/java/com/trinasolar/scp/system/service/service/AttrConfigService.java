package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.system.domain.dto.AttrConfigDTO;
import com.trinasolar.scp.system.domain.query.AttrConfigQuery;
import com.trinasolar.scp.system.domain.save.AttrConfigSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 系统扩展字段配置 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 13:58:46
 */
public interface AttrConfigService {
    /**
     * 分页获取系统扩展字段配置
     *
     * @param query 查询对象
     * @return 系统扩展字段配置分页对象
     */
    Page<AttrConfigDTO> queryByPage(AttrConfigQuery query);

    /**
     * 根据主键获取系统扩展字段配置详情
     *
     * @param id 主键
     * @return 系统扩展字段配置详情
     */
    AttrConfigDTO queryById(Long id);

    /**
     * 保存或更新系统扩展字段配置
     *
     * @param saveDTO 系统扩展字段配置保存对象
     * @return 系统扩展字段配置对象
     */
    AttrConfigDTO save(AttrConfigSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除系统扩展字段配置
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query
     * @param response
     */
    void export(AttrConfigQuery query, HttpServletResponse response);

    /**
     * 导入
     *
     * @param multipartFile
     * @return 批次号
     */
    String importData(MultipartFile multipartFile, ExcelPara excelPara);

    List<AttrConfigDTO> findBySenceAndHeaderId(String sence, Long lovHeaderId);

    void saves(String sence, Long attrHeaderId, List<AttrConfigSaveDTO> lovConfigSaveDTOList);

    List<AttrConfigDTO> listByHeaderId(AttrConfigQuery query);
}

