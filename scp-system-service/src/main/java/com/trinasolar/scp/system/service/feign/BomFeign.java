package com.trinasolar.scp.system.service.feign;

import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.dto.ItemAttrLovDTO;
import com.trinasolar.scp.system.domain.feign.ItemCodesQuery;
import com.trinasolar.scp.system.domain.feign.ItemsDTO;
import com.trinasolar.scp.system.domain.feign.ItemsOptionQuery;
import com.trinasolar.scp.system.domain.feign.MainMaterialInfoSaveDTO;
import com.trinasolar.scp.system.service.enums.Constants;
import com.trinasolar.scp.system.service.feign.fallback.BomFeignFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: BomFeign.java, Created by IntelliJ IDEA
 * @Description: TODO
 * @Author: wang_jun
 * @Date: 2022/7/6 14:25
 * @Version :1.0
 **/
//@FeignClient(value = Constants.BOM_SERVICE, fallbackFactory = BomFeignFallbackFactory.class, path = "/scp-bom-api")
@FeignClient(value = Constants.BOM_SERVICE, fallbackFactory = BomFeignFallbackFactory.class, path = "/scp-bom-api")
public interface BomFeign {

    @PostMapping(value = "/itemAttrLov/queryBySrcAttrId")
    ResponseEntity<Results<List<ItemAttrLovDTO>>> queryListBySrcAttrId(@RequestBody IdDTO idDTO);

    @PostMapping(value = "/itemAttrLov/sync")
    ResponseEntity<Results<Object>> itemAttrLovSync();

    @PostMapping("/item-attr/sync")
    ResponseEntity<Results<Object>> itemAttrSync();

    @PostMapping("/items/findBySourceItemId")
    ResponseEntity<Results<ItemsDTO>> findItemsBySourceItemId(@RequestBody IdDTO idDTO);


    @PostMapping("/items/findItemDescByItemCodes")
    ResponseEntity<Results<Map<String, String>>> findItemDescByItemCodes(@RequestBody ItemCodesQuery query);

    @PostMapping("/items/getMaterialInfoByItemCode")
    ResponseEntity<Results<MainMaterialInfoSaveDTO>> getMaterialInfoByItemCode(@RequestBody ItemsOptionQuery query);
}
