package com.trinasolar.scp.system.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.system.domain.dto.LanguageDTO;
import com.trinasolar.scp.system.domain.entity.Language;
import com.trinasolar.scp.system.domain.query.LanguageQuery;
import com.trinasolar.scp.system.domain.save.LanguageSaveDTO;
import com.trinasolar.scp.system.service.repository.LanguageRepository;
import com.trinasolar.scp.system.service.service.LanguageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 系统语言
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:17
 */
@Slf4j
@Service("languageService")
public class LanguageServiceImpl implements LanguageService {
    @Autowired
    LanguageRepository languageRepository;

    @Override
    public Page<Language> queryByPage(LanguageQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        Sort sort = Sort.by(Sort.Direction.ASC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        return languageRepository.findAll(booleanBuilder, pageable);
    }

    @Override
    public LanguageDTO queryById(Long id) {
        Language queryObj = languageRepository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        LanguageDTO result = new LanguageDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Override
    public LanguageDTO save(LanguageSaveDTO saveDTO) {
        // 配置Code不允许重复
        Language getByCode = languageRepository.getByCode(saveDTO.getCode());
        if (getByCode != null) {
            if (!Objects.equals(getByCode.getLanguageId(), saveDTO.getLanguageId())) {
                throw new BizException("定义的(code)已存在");
            }
        }

        Language newObj;
        if (saveDTO.getLanguageId() != null) {
            newObj = languageRepository.getOne(saveDTO.getLanguageId());
        } else {
            newObj = new Language();
        }

        BeanUtils.copyProperties(saveDTO, newObj);
        languageRepository.save(newObj);

        return this.queryById(newObj.getLanguageId());
    }

    @Override
    public void deleteById(Long id) {
        languageRepository.deleteById(id);
    }
}
