package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.CodeSeqDTO;
import com.trinasolar.scp.system.domain.entity.CodeSeq;
import com.trinasolar.scp.system.domain.query.CodeSeqQuery;
import com.trinasolar.scp.system.domain.save.CodeSeqSaveDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 单据序列主表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-09 15:05:46
 */
public interface CodeSeqService {
    /**
     * 分页获取单据序列主表
     *
     * @param query 查询对象
     * @return 单据序列主表分页对象
     */
    Page<CodeSeq> queryByPage(CodeSeqQuery query);

    /**
     * 根据主键获取单据序列主表详情
     *
     * @param id 主键
     * @return 单据序列主表详情
     */
    CodeSeqDTO queryById(Long id);

    /**
     * 保存或更新单据序列主表
     *
     * @param saveDTO 单据序列主表保存对象
     * @return 单据序列主表对象
     */
    CodeSeqDTO save(CodeSeqSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除单据序列主表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    String createCodeSeq(CodeSeqQuery query);
}

