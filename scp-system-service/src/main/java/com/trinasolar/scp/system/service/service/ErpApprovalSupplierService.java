package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.system.domain.dto.ErpApprovalSupplierDTO;
import com.trinasolar.scp.system.domain.dto.ErpApprovalSupplierPageNumDTO;
import com.trinasolar.scp.system.domain.dto.ErpSupplierDTO;
import com.trinasolar.scp.system.domain.entity.ErpApprovalSupplier;
import com.trinasolar.scp.system.domain.query.ErpApprovalSupplierQuery;
import com.trinasolar.scp.system.domain.save.ErpApprovalSupplierSaveDTO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 批准供应商 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:05:46
 */
public interface ErpApprovalSupplierService {
    /**
     * 分页获取批准供应商
     *
     * @param query 查询对象
     * @return 批准供应商分页对象
     */
    Page<ErpApprovalSupplierDTO> queryByPage(ErpApprovalSupplierQuery query);

    /**
     * 根据主键获取批准供应商详情
     *
     * @param id 主键
     * @return 批准供应商详情
     */
    ErpApprovalSupplierDTO queryById(Long id);

    /**
     * 查询4A 7A料号
     *
     * @return
     */
    ErpApprovalSupplierPageNumDTO querySupplierPage(ErpApprovalSupplierQuery query);

    /**
     * 保存或更新批准供应商
     *
     * @param saveDTO 批准供应商保存对象
     * @return 批准供应商对象
     */
    ErpApprovalSupplierDTO save(ErpApprovalSupplierSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除批准供应商
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query
     * @param response
     */
    void export(ErpApprovalSupplierQuery query, HttpServletResponse response);

    /**
     * 导入
     *
     * @param multipartFile
     * @return 批次号
     */
    String importData(MultipartFile multipartFile, ExcelPara excelPara);

    void sync();

    @Cacheable(cacheNames = "ApprovalSupplier_queryApprovalSupplier",
            key = "#p0", unless = "#result == null")
    List<ErpApprovalSupplierDTO> queryApprovalSupplierByItemNum(String itemNum);

    List<ErpApprovalSupplier> getByLimit(int skip, int limit);

    void dailySync(LocalDateTime fromDateTime, LocalDateTime toDateTime);

    List<ErpApprovalSupplier> findByUpdatedTimeGreaterThan(LocalDateTime updateTime);

    void updateAllCategorySegment5();

    List<String> getBrandsByItemCode(String itemCode);

    Map<String, Set<String>> getItemCodeAndVendorsSetByItemCodes(List<String> itemCodes);

    Map<String, Set<Long>> getItemCodeAndVendorIdSetByItemCodes(List<String> itemCodes);

    List<String> getItemCodesByVendorId(String vendorId);

    Map<String, List<ErpApprovalSupplierDTO>> queryApprovalSupplier(List<String> itemNums);

    void updateVendorNameByErpSupplier(List<ErpSupplierDTO> supplierDTO);

    void updateAllVendorName();

    /**
     * 根据条件查询
     *
     * @param query
     * @return
     */
    ErpApprovalSupplierDTO findApprovalSupplierByCondition(ErpApprovalSupplierQuery query);
}

