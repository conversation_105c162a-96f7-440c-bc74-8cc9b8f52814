package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.Message;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 系统消息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
@Repository
public interface MessageRepository
        extends JpaRepository<Message, Long>, QuerydslPredicateExecutor<Message> {
    @Query(value = "SELECT * FROM sys_message AS tb WHERE tb.lang= ?1 AND tb.code= ?2 AND tb.is_deleted=0",
            nativeQuery = true)
    Message getByLangAndCode(String lang, String code);
}
