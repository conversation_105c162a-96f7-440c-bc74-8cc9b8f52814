package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.system.domain.dto.SysScriptDTO;
import com.trinasolar.scp.system.domain.query.SysScriptQuery;
import com.trinasolar.scp.system.domain.save.SysScriptSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 脚本维护
 *
 * <AUTHOR>
 * @date 2022-10-13
 */
 public interface SysScriptService {
    /**
     * 分页获取脚本维护
     *
     * @param query 查询对象
     * @return 脚本维护 分页对象
     */
    Page<SysScriptDTO> queryByPage(SysScriptQuery query);
    /**
     * 根据主键获取脚本维护 详情
     *
     * @param id 主键
     * @return 脚本维护 详情
     */
    SysScriptDTO queryById(Long id);
    /**
     * 保存或更新脚本维护
     *
     * @param saveDTO 脚本维护 保存对象
     * @return 脚本维护 对象
     */
    SysScriptDTO save(SysScriptSaveDTO saveDTO);
    /**
     * 根据主键逻辑删除脚本维护
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);
     /**
     * 导出
     *
     * @param query
     * @param response
     */
    void export(SysScriptQuery query, HttpServletResponse response);
    /**
     * 导入
     *
     * @param multipartFile
     * @return 批次号
     */
    String importData(MultipartFile multipartFile, ExcelPara excelPara);
}
