package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.DailyRatesDTO;
import com.trinasolar.scp.system.domain.query.DailyRatesQuery;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 汇率表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-29 09:01:50
 */
public interface DailyRatesService {
    /**
     * 分页获取汇率表
     *
     * @param query 查询对象
     * @return 汇率表分页对象
     */
    Page<DailyRatesDTO> queryByPage(DailyRatesQuery query);

    /**
     * 根据主键获取汇率表详情
     *
     * @param id 主键
     * @return 汇率表详情
     */
    DailyRatesDTO queryById(Long id);

    /**
     * 从ERP同步汇率
     */
    void syncDailyRatesERP();

    /**
     * 保存或更新汇率表
     *
     * @param dailyRatesDTOList 汇率表保存对象
     * @return 汇率表对象
     */
    void batchSave(List<DailyRatesDTO> dailyRatesDTOList);
}

