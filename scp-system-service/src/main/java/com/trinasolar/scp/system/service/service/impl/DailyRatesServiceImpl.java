package com.trinasolar.scp.system.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.system.domain.convert.DailyRatesDEConvert;
import com.trinasolar.scp.system.domain.dto.DailyRatesDTO;
import com.trinasolar.scp.system.domain.dto.ExternalInterfaceDTO;
import com.trinasolar.scp.system.domain.entity.DailyRates;
import com.trinasolar.scp.system.domain.entity.QDailyRates;
import com.trinasolar.scp.system.domain.query.DailyRatesQuery;
import com.trinasolar.scp.system.service.repository.DailyRatesRepository;
import com.trinasolar.scp.system.service.service.DailyRatesService;
import com.trinasolar.scp.system.service.service.ErpInterfaceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 汇率表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-29 09:01:50
 */
@Slf4j
@Service("dailyRatesService")
@RefreshScope
public class DailyRatesServiceImpl implements DailyRatesService {

    @Autowired
    DailyRatesRepository ratesRepository;

    @Resource
    DailyRatesDEConvert dailyRatesDEConvert;

    @Autowired
    ErpInterfaceService erpInterfaceService;

    @Value("${erp.interface.platform.url}")
    private String erpUrl;

    @Value("${erp.interface.ca004.clientid}")
    private String ca004ClientId;

    @Value("${erp.interface.ca004.secret}")
    private String ca004Secret;

    @Override
    public Page<DailyRatesDTO> queryByPage(DailyRatesQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<DailyRates> dailyRatesPage = ratesRepository.findAll(booleanBuilder, pageable);
        return new PageImpl(dailyRatesDEConvert.toDto(dailyRatesPage.getContent()), pageable, dailyRatesPage.getTotalElements());
    }

    /**
     * 构建查询条件
     *
     * @param query
     * @return
     */
    private BooleanBuilder buildWhere(DailyRatesQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QDailyRates dailyRates = QDailyRates.dailyRates;

        return booleanBuilder;
    }

    @Override
    public DailyRatesDTO queryById(Long id) {
        DailyRates queryObj = ratesRepository.findById(id).get();
        if (queryObj == null) {
            return null;
        }

        DailyRatesDTO result = new DailyRatesDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncDailyRatesERP() {
        // 每月1号运行，取1号当天的期初汇率
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar1 = Calendar.getInstance();
        calendar1.set(Calendar.DAY_OF_MONTH, 1);
        String firstDay = sdf.format(calendar1.getTime());
        JSONObject params = new JSONObject();
        // 期初汇率
        params.put("conversionType", "1002");
        params.put("conversion_date_from", firstDay + " 00:00:00");
        params.put("conversion_date_to", firstDay + " 23:59:59");

        ExternalInterfaceDTO dto = new ExternalInterfaceDTO();
        dto.setUrl(erpUrl);
        dto.setPath("/ca004/v1/conversionRate/list");
        dto.setClientId(ca004ClientId);
        dto.setSecret(ca004Secret);
        dto.setRequestBody(params.toJSONString());

        String resultJson = erpInterfaceService.postForString(dto);

        JSONObject outJson = JSON.parseObject(resultJson);
        JSONArray datas = outJson.getJSONArray("data");
        if (datas == null) {
            return;
        }

        DailyRatesDTO dailyRatesDTO;
        List<DailyRatesDTO> dailyRatesDTOList = new ArrayList<>();
        for (int i = 0; i < datas.size(); i++) {
            JSONObject jsonObject = datas.getJSONObject(i);
            dailyRatesDTO = new DailyRatesDTO();
            dailyRatesDTO.setFromCurrency(jsonObject.getString("from_currency"));
            dailyRatesDTO.setToCurrency(jsonObject.getString("to_currency"));
            String conversionDateStr = jsonObject.getString("conversion_date");
            LocalDateTime conversionDate = LocalDateTime.parse(conversionDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            dailyRatesDTO.setConversionDate(conversionDate);
            dailyRatesDTO.setConversionType(jsonObject.getString("conversion_type"));
            dailyRatesDTO.setConversionRate(jsonObject.getString("conversion_rate"));
            dailyRatesDTO.setStatusCode("A");

            dailyRatesDTOList.add(dailyRatesDTO);
        }

        batchSave(dailyRatesDTOList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchSave(List<DailyRatesDTO> ratesDTOList) {
        List<DailyRates> dailyRatesList = dailyRatesDEConvert.toEntity(ratesDTOList);
        ratesRepository.saveAll(dailyRatesList);
    }
}
