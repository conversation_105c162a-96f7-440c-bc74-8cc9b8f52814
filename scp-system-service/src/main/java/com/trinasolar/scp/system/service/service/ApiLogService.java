package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.ApiLogDTO;
import com.trinasolar.scp.system.domain.query.ApiLogQuery;
import com.trinasolar.scp.system.domain.save.ApiLogSaveDTO;
import org.springframework.data.domain.Page;

/**
 * 推送数据至其他系统接口日志表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-18 09:26:45
 */
public interface ApiLogService {
    /**
     * 分页获取推送数据至其他系统接口日志表
     *
     * @param query 查询对象
     * @return 推送数据至其他系统接口日志表分页对象
     */
    Page<ApiLogDTO> queryByPage(ApiLogQuery query);

    /**
     * 根据主键获取推送数据至其他系统接口日志表详情
     *
     * @param id 主键
     * @return 推送数据至其他系统接口日志表详情
     */
    ApiLogDTO queryById(Long id);

    /**
     * 保存或更新推送数据至其他系统接口日志表
     *
     * @param saveDTO 推送数据至其他系统接口日志表保存对象
     * @return 推送数据至其他系统接口日志表对象
     */
    ApiLogDTO save(ApiLogSaveDTO saveDTO);
}

