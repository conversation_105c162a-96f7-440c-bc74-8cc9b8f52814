package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.OrganizationDefinitions;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-28 11:38:58
 */
@Repository
public interface OrganizationDefinitionsRepository
        extends JpaRepository<OrganizationDefinitions, Long>, QuerydslPredicateExecutor<OrganizationDefinitions> {
    OrganizationDefinitions findByOrganizationCode(String code);
}
