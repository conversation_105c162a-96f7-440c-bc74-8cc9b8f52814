package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.LovLineDTO;

import java.util.List;
import java.util.Map;

/**
 * LovCache服务,用于管理Lov的RedisCache 用LovUtils中移出来的
 * 为了解决LovHeader的缓存问题
 *
 * <AUTHOR>
 * @date 2024/2/6
 */
public interface LovCacheService {
    void clearLovHeaderCache(String lovHeaderCode);

    void clearLovLinesFromRedis(List<LovLineDTO> lovLineList);

    String getLovHeaderUpdKey(String headerCode);

    String getLovHeaderNameUpdKey(String headerCode);

    void putLovLinesIntoRedis(List<com.trinasolar.scp.common.api.base.LovLineDTO> lovLineDTOS);

    void putLovHeaderCache(String headerCode, List<LovLineDTO> lovLineDTOS, boolean withTranslateKey);

    void putNameToRedis(String headerCode, Map<String, LovLineDTO> queryLocalizedLovLinesFromDB);

    List<LovLineDTO> getByHeaderCodeFromRedis(String headerCode, boolean withTranslateKey);
}
