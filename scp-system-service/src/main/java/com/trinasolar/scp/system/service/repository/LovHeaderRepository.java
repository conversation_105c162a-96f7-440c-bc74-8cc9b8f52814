package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.common.api.base.BaseRepository;
import com.trinasolar.scp.common.api.base.BaseServiceImpl;
import com.trinasolar.scp.system.domain.entity.LovHeader;
import com.trinasolar.scp.system.domain.entity.LovLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * lov头表(LovHeader)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-24 10:42:24
 */
@Repository
public interface LovHeaderRepository
        extends BaseRepository<LovHeader,Long>
{
    LovHeader findByLovCode(String code);

    LovHeader findByLovCodeIgnoreCase(String lovCode);
}

