package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.LovTl;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 系统LOV多语言表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
@Repository
public interface LovTlRepository
        extends JpaRepository<LovTl, Long>, QuerydslPredicateExecutor<LovTl> {
    @Query(value = "SELECT * FROM sys_lov_tl AS tb WHERE tb.pk_id= ?1 AND tb.pk_type= ?2 AND tb.lang= ?3 AND tb.is_deleted=0", nativeQuery = true)
    LovTl getByPkIdAndPkTypeAndLang(Long pkId, String pkType, String lang);

    @Query(value = "SELECT * FROM sys_lov_tl AS tb WHERE tb.pk_id in (?3) AND tb.pk_type= ?2 AND tb.lang= ?1 AND tb.is_deleted=0", nativeQuery = true)
    List<LovTl> listByLangAndPkTypeAndPkIds(String lang, String pkType, List<Long> pkIds);

    LovTl findByPkIdAndPkTypeAndLang(Long pkId, String pkType, String lang);

    List<LovTl> findAllByPkTypeAndPkIdIn(String pkType, List<Long> pkIds);

    List<LovTl> findAllByLangAndPkTypeAndPkIdIn(String lang, String pkType, List<Long> pkIds);

    LovTl findByPkIdAndPkTypeAndLovFieldAndLang(Long pkId, String pkType, String lovField, String lang);
}
