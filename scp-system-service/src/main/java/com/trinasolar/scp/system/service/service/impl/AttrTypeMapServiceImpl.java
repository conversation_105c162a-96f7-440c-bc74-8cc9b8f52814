package com.trinasolar.scp.system.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.system.domain.dto.AttrTypeMapDTO;
import com.trinasolar.scp.system.domain.entity.AttrTypeMap;
import com.trinasolar.scp.system.domain.query.AttrTypeMapQuery;
import com.trinasolar.scp.system.domain.save.AttrTypeMapSaveDTO;
import com.trinasolar.scp.system.service.repository.AttrTypeMapRepository;
import com.trinasolar.scp.system.service.service.AttrTypeMapService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/4/26
 */
@Service("attrTypeMapService")
public class AttrTypeMapServiceImpl implements AttrTypeMapService {
    @Autowired
    AttrTypeMapRepository attrTypeMapRepository;

    @Override
    public Page<AttrTypeMap> queryByPage(AttrTypeMapQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        return attrTypeMapRepository.findAll(booleanBuilder, pageable);
    }

    @Override
    public AttrTypeMapDTO queryById(Long id) {
        AttrTypeMap queryObj = attrTypeMapRepository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        AttrTypeMapDTO result = new AttrTypeMapDTO();
        BeanUtils.copyProperties(queryObj, result);
        return result;
    }

    @Override
    public AttrTypeMapDTO save(AttrTypeMapSaveDTO saveDTO) {
        AttrTypeMap newObj;
        if (saveDTO.getAttributeMapId() != null) {
            newObj = attrTypeMapRepository.getOne(saveDTO.getAttributeMapId());
        } else {
            newObj = new AttrTypeMap();
        }

        BeanUtils.copyProperties(saveDTO, newObj);
        attrTypeMapRepository.save(newObj);

        return this.queryById(newObj.getAttributeMapId());
    }

    @Override
    public void deleteById(Long id) {
        attrTypeMapRepository.deleteById(id);
    }
}
