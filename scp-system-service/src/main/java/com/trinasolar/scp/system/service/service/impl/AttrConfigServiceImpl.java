package com.trinasolar.scp.system.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.system.domain.convert.AttrConfigDEConvert;
import com.trinasolar.scp.system.domain.dto.AttrConfigDTO;
import com.trinasolar.scp.system.domain.entity.AttrConfig;
import com.trinasolar.scp.system.domain.entity.QAttrConfig;
import com.trinasolar.scp.system.domain.query.AttrConfigQuery;
import com.trinasolar.scp.system.domain.save.AttrConfigSaveDTO;
import com.trinasolar.scp.system.service.repository.AttrConfigRepository;
import com.trinasolar.scp.system.service.service.AttrConfigService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 系统扩展字段配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 13:58:46
 */
@Slf4j
@Service("attrConfigService")
public class AttrConfigServiceImpl implements AttrConfigService {
    @Autowired
    AttrConfigRepository repository;
    @Autowired
    AttrConfigDEConvert attrConfigDEConvert;

    @Override
    public Page<AttrConfigDTO> queryByPage(AttrConfigQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<AttrConfig> attrConfigPage = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(attrConfigDEConvert.toDto(attrConfigPage.getContent()), pageable, attrConfigPage.getTotalElements());
    }

    /**
     * 构建查询条件
     *
     * @param query
     * @return
     */
    private BooleanBuilder buildWhere(AttrConfigQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QAttrConfig attrConfig = QAttrConfig.attrConfig;

        return booleanBuilder;
    }

    @Override
    public AttrConfigDTO queryById(Long id) {
        AttrConfig queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        AttrConfigDTO result = new AttrConfigDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AttrConfigDTO save(AttrConfigSaveDTO saveDTO) {
        AttrConfig newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new AttrConfig());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @SneakyThrows
    @Override
    public void export(AttrConfigQuery query, HttpServletResponse response) {
        List<AttrConfigDTO> dtos = queryByPage(query).getContent();
        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "系统扩展字段配置", "系统扩展字段配置", excelPara.getSimpleHeader(), excelData);
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String importData(MultipartFile multipartFile, ExcelPara excelPara) {
        String batchNo = UUID.randomUUID().toString();
        List<AttrConfigDTO> excelData =
                ExcelUtils.readExcel(multipartFile.getInputStream(), null, AttrConfigDTO.class, excelPara);

        log.info("{}条数据，开始存储数据库！", excelData.size());
        //校验 todo
        repository.saveAll(attrConfigDEConvert.toEntity(excelData));
        log.info("存储数据库成功！");

        return batchNo;
    }

    @Override
    public List<AttrConfigDTO> findBySenceAndHeaderId(String sence, Long lovHeaderId) {
        List<AttrConfig> result = repository.findBySenceAndAttrHeaderId(sence, lovHeaderId);
        return attrConfigDEConvert.toDto(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saves(String sence, Long attrHeaderId, List<AttrConfigSaveDTO> lovConfigSaveDTOList) {
        verifyAndDelete(sence, attrHeaderId, lovConfigSaveDTOList);
        // 验证完毕，保存或更新
        for (AttrConfigSaveDTO lovConfigSaveDTO : lovConfigSaveDTOList) {
            lovConfigSaveDTO.setAttrHeaderId(attrHeaderId);
            lovConfigSaveDTO.setSence(sence);
            save(lovConfigSaveDTO);
        }
    }

    @Override
    public List<AttrConfigDTO> listByHeaderId(AttrConfigQuery query) {
        List<AttrConfig> result = repository.findByAttrHeaderId(query.getAttrHeaderId());

        return attrConfigDEConvert.toDto(result);
    }

    private void verifyAndDelete(String sence, Long attrHeaderId, List<AttrConfigSaveDTO> lines) {
        // 1. 先找出以前存在但现在没有的
        List<AttrConfig> savedConfigs = repository.findBySenceAndAttrHeaderId(sence, attrHeaderId);

        if (lines == null || lines.isEmpty()) {
            if (!savedConfigs.isEmpty()) {
                // 删除所有然后返回
                repository.deleteAll(savedConfigs);
            }
            return;
        }
        List<Long> currentIds = lines.stream().filter(item -> item.getId() != null).map(AttrConfigSaveDTO::getId)
                .collect(Collectors.toList());
        List<AttrConfig> deleteConfigs = savedConfigs.stream().filter(item -> !currentIds.contains(item.getId()))
                .collect(Collectors.toList());

        if (!deleteConfigs.isEmpty()) {
            repository.deleteAll(deleteConfigs);
        }
    }
}
