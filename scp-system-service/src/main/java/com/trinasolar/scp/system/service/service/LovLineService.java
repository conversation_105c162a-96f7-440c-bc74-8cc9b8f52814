package com.trinasolar.scp.system.service.service;


import com.trinasolar.scp.common.api.base.BaseService;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.system.domain.dto.LovLineDTO;
import com.trinasolar.scp.system.domain.dto.LovLineExtDTO;
import com.trinasolar.scp.system.domain.dto.ShipmentMailImportDTO;
import com.trinasolar.scp.system.domain.entity.LovLine;
import com.trinasolar.scp.system.domain.query.LovLineQuery;
import com.trinasolar.scp.system.domain.save.LovLineSaveDTO;
import com.trinasolar.scp.system.domain.save.PutValueToLovLineSaveDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * lov头表(LovLine)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-24 10:42:26
 */
public interface LovLineService extends BaseService<LovLine, Long> {

    /**
     * 通过ID查询单条数据
     *
     * @param lovLineId 主键
     * @return 实例对象
     */
    LovLineDTO queryById(Long lovLineId);

    /**
     * 分页查询
     *
     * @param lovLineQuery 筛选条件
     * @return 查询结果
     */
    Page<LovLineExtDTO> queryByPage(LovLineQuery lovLineQuery);

    /**
     * 通过主键删除数据
     *
     * @param lovLineId 主键
     * @return 是否成功
     */
    void deleteById(Long lovLineId);

    List<LovLine> listByLovHeaderId(Long lovHeaderId);

    List<LovLineDTO> queryByLovQuery(LovLineQuery lovLineQuery);

    List<LovLineDTO> queryByLovQuery(List<LovLineQuery> lovLineQueries);

    LovLineDTO save(LovLineSaveDTO lovHeaderSaveDTO);

    List<LovLineDTO> queryByIds(IdsDTO idsQuery);

    List<LovLineDTO> listDtoByDate(String code, String lastUpdatedTime);

    List<LovLineDTO> saveAll(List<LovLineDTO> dtoList);

    List<LovLineDTO> importExcel(List<LovLineDTO> lovLineExtDTOS, String lovCode);

    List<LovLineDTO> getByLineIds(List<LovLineQuery> lovLineQuerys);

    void evict(Long lovLineId);

    void putValueToLov(PutValueToLovLineSaveDTO saveDTO);

    List<LovLineDTO> queryAllByHeaderCode(String headerCode);

    void importShipmentEmailExcel(List<ShipmentMailImportDTO> areaList, List<ShipmentMailImportDTO> tmWorkGroupList, List<ShipmentMailImportDTO> orgList, String lovCode);

    LovLineDTO getByHeaderCodesAndName(LovLineQuery lovLineQuery);

    List<LovLineDTO> queryByCodeNew(LovLineQuery lovLineQuery);
}
