package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.CodeSeq;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 单据序列主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-09 15:05:46
 */
@Repository
public interface CodeSeqRepository extends JpaRepository<CodeSeq, Long>, QuerydslPredicateExecutor<CodeSeq> {
    CodeSeq findBySeqCode(String seqCode);
}
