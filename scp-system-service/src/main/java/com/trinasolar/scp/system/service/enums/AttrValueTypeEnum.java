package com.trinasolar.scp.system.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/4/25
 */
@AllArgsConstructor
@Getter
public enum AttrValueTypeEnum {
    /**
     * 属性值类型名称 1-文本 2-数字  3-值列表 4-日期
     */
    TEXT(1, "文本"),
    NUMBER(2, "数字"),
    VALUE_LIST(3, "值列表"),
    DATE(4, "日期");
    private Integer value;
    private String name;

    public static AttrValueTypeEnum getByValue(Integer value) {
        for (AttrValueTypeEnum lovHeaderEnum : AttrValueTypeEnum.values()) {
            if (Objects.equals(lovHeaderEnum.getValue(), value)) {
                return lovHeaderEnum;
            }
        }
        return null;
    }
}
