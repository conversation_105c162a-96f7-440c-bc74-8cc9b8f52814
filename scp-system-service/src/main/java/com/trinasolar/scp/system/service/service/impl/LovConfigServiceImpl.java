package com.trinasolar.scp.system.service.service.impl;

import com.trinasolar.scp.system.domain.dto.LovConfigDTO;
import com.trinasolar.scp.system.domain.entity.LovConfig;
import com.trinasolar.scp.system.domain.save.LovConfigSaveDTO;
import com.trinasolar.scp.system.service.repository.LovConfigRepository;
import com.trinasolar.scp.system.service.service.LovConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * lov头表(LovHeader)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-24 10:42:26
 */
@Service("lovConfigService")
public class LovConfigServiceImpl implements LovConfigService {
    @Autowired
    LovConfigRepository lovConfigRepository;

    @Override
    public void save(List<LovConfigSaveDTO> lovConfigSaveDTOList, Long lovHeaderId) {
        // 删除以前有但现在没有的config
        // 1. 先找出以前存在但现在没有的
        List<LovConfig> savedConfigs = lovConfigRepository.listByHeaderId(lovHeaderId);

        if (lovConfigSaveDTOList == null || lovConfigSaveDTOList.isEmpty()) {
            if (savedConfigs.isEmpty()) {
                return;
            } else {
                // 删除所有然后返回
                lovConfigRepository.deleteAll(savedConfigs);
            }
        }

        List<Long> currentIds = new ArrayList<>();
        for (LovConfigSaveDTO lovConfigSaveDTO : lovConfigSaveDTOList) {
            if (lovConfigSaveDTO.getLovAttrColConId() != null) {
                currentIds.add(lovConfigSaveDTO.getLovAttrColConId());
            }
        }
        List<LovConfig> deleteConfigs = new ArrayList<>();
        if (!savedConfigs.isEmpty()) {
            for (LovConfig savedConfig : savedConfigs) {
                if (!currentIds.contains(savedConfig.getLovAttrColConId())) {
                    deleteConfigs.add(savedConfig);
                }
            }
        }
        if (!deleteConfigs.isEmpty()) {
            lovConfigRepository.deleteAll(deleteConfigs);
        }

        // 验证完毕，保存或更新
        for (LovConfigSaveDTO lovConfigSaveDTO : lovConfigSaveDTOList) {
            lovConfigSaveDTO.setLovHeaderId(lovHeaderId);
            saveLovConfigSaveDTO(lovConfigSaveDTO);
        }
    }

    @Override
    public List<LovConfigDTO> listByHeaderId(Long lovHeaderId) {
        List<LovConfig> lovConfigs = lovConfigRepository.listByHeaderId(lovHeaderId);
        return lovConfigs.stream().map(item -> {
            LovConfigDTO lovConfigDTO = new LovConfigDTO();
            BeanUtils.copyProperties(item, lovConfigDTO);
            return lovConfigDTO;
        }).collect(Collectors.toList());
    }

    private void saveLovConfigSaveDTO(LovConfigSaveDTO lovConfigSaveDTO) {
        LovConfig lovConfig;
        if (lovConfigSaveDTO.getLovAttrColConId() != null) {
            lovConfig = lovConfigRepository.getOne(lovConfigSaveDTO.getLovAttrColConId());
        } else {
            lovConfig = new LovConfig();
        }

        BeanUtils.copyProperties(lovConfigSaveDTO, lovConfig);
        lovConfigRepository.save(lovConfig);
    }
}
