package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.OperatorConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 操作配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-01 09:45:41
 */
@Repository
public interface OperatorConfigRepository extends JpaRepository<OperatorConfig, Long>, QuerydslPredicateExecutor<OperatorConfig> {
}
