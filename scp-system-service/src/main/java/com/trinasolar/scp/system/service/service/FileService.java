package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.FileDTO;
import com.trinasolar.scp.system.domain.query.FileQuery;
import com.trinasolar.scp.system.domain.save.FileSaveDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 附件表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-08 18:35:02
 */
public interface FileService {
    /**
     * 分页获取附件表
     *
     * @param query 查询对象
     * @return 附件表分页对象
     */
    Page<FileDTO> queryByPage(FileQuery query);

    /**
     * 根据主键获取附件表详情
     *
     * @param id 主键
     * @return 附件表详情
     */
    FileDTO queryById(Long id);

    /**
     * 保存或更新附件表
     *
     * @param saveDTO 附件表保存对象
     * @return 附件表对象
     */
    FileDTO save(FileSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除附件表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 获取附件列表
     *
     * @param bizKey  业务主键
     * @param bizType 业务类型
     * @return
     */
    List<FileDTO> getFilesByBizKeyAndBizType(String bizKey, String bizType);
}

