package com.trinasolar.scp.system.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.common.api.enums.DeleteEnum;
import com.trinasolar.scp.system.domain.convert.SupplierDEConvert;
import com.trinasolar.scp.system.domain.dto.SupplierDTO;
import com.trinasolar.scp.system.domain.entity.QSupplier;
import com.trinasolar.scp.system.domain.entity.Supplier;
import com.trinasolar.scp.system.domain.query.SupplierQuery;
import com.trinasolar.scp.system.domain.save.SupplierSaveDTO;
import com.trinasolar.scp.system.service.repository.SupplierRepository;
import com.trinasolar.scp.system.service.service.SupplierService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 供应商信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-28 15:29:29
 */
@Slf4j
@Service("supplierService")
public class SupplierServiceImpl implements SupplierService {
    @Autowired
    SupplierRepository repository;

    @Override
    public Page<SupplierDTO> queryByPage(SupplierQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<Supplier> supplierPage = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(SupplierDEConvert.INSTANCE.toDto(supplierPage.getContent()), pageable, supplierPage.getTotalElements());
    }

    @Override
    public List<SupplierDTO> queryByList(SupplierQuery query) {
        return queryByPage(query).getContent();
    }

    /**
     * 构建查询条件
     *
     * @param query
     * @return
     */
    private BooleanBuilder buildWhere(SupplierQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QSupplier supplier = QSupplier.supplier;
        booleanBuilder.and(supplier.isDeleted.eq(DeleteEnum.NO.getCode()));

        if (CollectionUtils.isNotEmpty(query.getSupplierIds())) {
            booleanBuilder.and(supplier.supplierId.in(query.getSupplierIds()));
        }
        if (CollectionUtils.isNotEmpty(query.getNames())) {
            booleanBuilder.and(supplier.name.in(query.getNames()));
        }

        return booleanBuilder;
    }

    @Override
    public SupplierDTO queryById(Long id) {
        Supplier queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        SupplierDTO result = new SupplierDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SupplierDTO save(SupplierSaveDTO saveDTO) {
        Supplier newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new Supplier());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @SneakyThrows
    @Override
    public void export(SupplierQuery query, HttpServletResponse response) {
        EasyExcel.write(response.getOutputStream(), SupplierDTO.class)
                .sheet()
                .doWrite(() -> queryByPage(query).getContent());
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String importData(MultipartFile multipartFile) {
        String batchNo = UUID.randomUUID().toString();
        EasyExcel.read(multipartFile.getInputStream(), SupplierDTO.class, new ReadListener<SupplierDTO>() {
            /**
             * 单次缓存的数据量
             */
            public static final int BATCH_COUNT = 100;
            /**
             *临时存储
             */
            private List<SupplierDTO> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

            @Override
            public void invoke(SupplierDTO data, AnalysisContext context) {
                cachedDataList.add(data);
                if (cachedDataList.size() >= BATCH_COUNT) {
                    saveData();
                    // 存储完成清理 list
                    cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                saveData();
            }

            /**
             * 保存数据库
             */
            private void saveData() {
                log.info("{}条数据，开始存储数据库！", cachedDataList.size());
                repository.saveAll(SupplierDEConvert.INSTANCE.toEntity(cachedDataList));
                log.info("存储数据库成功！");
            }
        }).sheet().doRead();

        return batchNo;
    }
}
