package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.AttrTypeLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-04-24 10:42:24
 */
@Repository
public interface AttrTypeLineRepository
        extends JpaRepository<AttrTypeLine, Long>, QuerydslPredicateExecutor<AttrTypeLine> {

    @Query(value = "SELECT * FROM sys_attr_type_lines WHERE sys_attr_type_lines.attr_type_header_id= ?1 AND is_deleted=0",
            nativeQuery = true)
    List<AttrTypeLine> getByTypeHeaderId(Long attrTypeHeaderId);

    @Query(value = "SELECT * FROM sys_attr_type_lines AS tb WHERE tb.attr_code= ?1 AND tb.attr_type_header_id=?2 AND is_deleted=0",
            nativeQuery = true)
    AttrTypeLine getByCodeAndAttrTypeHeaderId(String attrCode, Long attrTypeHeaderId);

    List<AttrTypeLine> findByIsDeletedAndAttrLineIdIn(Integer isDeleted,List<Long> ids);
}

