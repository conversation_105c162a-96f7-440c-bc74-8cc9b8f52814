package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.LovConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * lov头表(LovHeader)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-24 10:42:24
 */
@Repository
public interface LovConfigRepository
        extends JpaRepository<LovConfig, Long>, QuerydslPredicateExecutor<LovConfig> {

    @Query(value = "SELECT * FROM sys_lov_config WHERE sys_lov_config.lov_header_id= ?1 AND is_deleted=0",
            nativeQuery = true)
    List<LovConfig> listByHeaderId(Long lovHeaderId);
}

