package com.trinasolar.scp.system.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.system.domain.convert.SysScriptDEConvert;
import com.trinasolar.scp.system.domain.dto.SysScriptDTO;
import com.trinasolar.scp.system.domain.entity.QSysScript;
import com.trinasolar.scp.system.domain.entity.SysScript;
import com.trinasolar.scp.system.domain.query.SysScriptQuery;
import com.trinasolar.scp.system.domain.save.SysScriptSaveDTO;
import com.trinasolar.scp.system.service.repository.SysScriptRepository;
import com.trinasolar.scp.system.service.service.SysScriptService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

/**
 * 脚本维护
 *
 * <AUTHOR>
 * @date 2022-10-13
 */
@Slf4j
@Service("sysScriptService")
public class SysScriptServiceImpl implements SysScriptService {
    @Autowired
    private SysScriptRepository repository;
    @Autowired
    private SysScriptDEConvert sysScriptDEConvert;

    @Override
    public Page<SysScriptDTO> queryByPage(SysScriptQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<SysScript> sysScriptPage = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(sysScriptDEConvert.toDto(sysScriptPage.getContent()), pageable, sysScriptPage.getTotalElements());
    }

    /**
     * 构建查询条件
     *
     * @param query
     * @return
     */
    private BooleanBuilder buildWhere(SysScriptQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QSysScript sysScript = QSysScript.sysScript;
        if(StringUtils.isNotBlank(query.getScriptModule())){
            booleanBuilder.and(sysScript.scriptModule.like(StringUtils.join("%",query.getScriptModule(),"%")));
        }
        if(StringUtils.isNotBlank(query.getScriptCode())){
            booleanBuilder.and(sysScript.scriptCode.like(StringUtils.join("%",query.getScriptCode(),"%")));
        }
        if(StringUtils.isNotBlank(query.getScriptName())){
            booleanBuilder.and(sysScript.scriptName.like(StringUtils.join("%",query.getScriptName(),"%")));
        }
        return booleanBuilder;
    }

    @Override
    public SysScriptDTO queryById(Long id) {
        SysScript queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }
        SysScriptDTO result = new SysScriptDTO();
        BeanUtils.copyProperties(queryObj, result);
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SysScriptDTO save(SysScriptSaveDTO saveDTO) {
        SysScript newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new SysScript());

        this.verifyUniqueness(saveDTO);

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);
        return this.queryById(newObj.getId());
    }

    private void verifyUniqueness(SysScriptSaveDTO saveDTO) {
        BooleanBuilder builder = new BooleanBuilder();
        QSysScript sysScript = QSysScript.sysScript;
        builder.and(sysScript.scriptCode.eq(saveDTO.getScriptCode()))
                .and(sysScript.scriptModule.eq(saveDTO.getScriptModule()));
        if (Objects.nonNull(saveDTO.getId())) {
            builder.andNot(sysScript.id.eq(saveDTO.getId()));
        }
        long count = repository.count(builder);
        Assert.isTrue(count == 0 , String.format("模块：%s，脚本编码：%s，不可重复！",
                saveDTO.getScriptModule(), saveDTO.getScriptCode()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @SneakyThrows
    @Override
    public void export(SysScriptQuery query, HttpServletResponse response) {
        List<SysScriptDTO> dtos = queryByPage(query).getContent();
        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);
        ExcelUtils.exportEx(response, "脚本维护", "脚本维护", excelPara.getSimpleHeader(), excelData);
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String importData(MultipartFile multipartFile, ExcelPara excelPara) {
        String batchNo = UUID.randomUUID().toString();
        List<SysScriptDTO> excelData =
                ExcelUtils.readExcel(multipartFile.getInputStream(), null, SysScriptDTO.class, excelPara);
        log.info("{}条数据，开始存储数据库！", excelData.size());
        //校验 todo
        repository.saveAll(sysScriptDEConvert.toEntity(excelData));
        log.info("存储数据库成功！");
        return batchNo;
    }

}
