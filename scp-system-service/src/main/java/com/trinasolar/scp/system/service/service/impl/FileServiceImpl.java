package com.trinasolar.scp.system.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.common.api.enums.DeleteEnum;
import com.trinasolar.scp.system.domain.convert.FileDEConvert;
import com.trinasolar.scp.system.domain.dto.FileDTO;
import com.trinasolar.scp.system.domain.entity.File;
import com.trinasolar.scp.system.domain.entity.QFile;
import com.trinasolar.scp.system.domain.query.FileQuery;
import com.trinasolar.scp.system.domain.save.FileSaveDTO;
import com.trinasolar.scp.system.service.repository.FileRepository;
import com.trinasolar.scp.system.service.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 附件表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-08 18:35:02
 */
@Slf4j
@Service("fileService")
public class FileServiceImpl implements FileService {
    @Autowired
    FileRepository repository;
    @Autowired
    FileDEConvert fileDEConvert;

    @Override
    public Page<FileDTO> queryByPage(FileQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<File> filePage = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(fileDEConvert.toDto(filePage.getContent()), pageable, filePage.getTotalElements());
    }

    /**
     * 构建查询条件
     *
     * @param query
     * @return
     */
    private BooleanBuilder buildWhere(FileQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QFile file = QFile.file;

        if (StringUtils.isNotBlank(query.getFk())) {
            booleanBuilder.and(file.fk.eq(query.getFk()));
        }
        if (StringUtils.isNotBlank(query.getName())) {
            booleanBuilder.and(file.name.eq(query.getName()));
        }
        if (StringUtils.isNotBlank(query.getFileKey())) {
            booleanBuilder.and(file.fileKey.eq(query.getFileKey()));
        }
        if (StringUtils.isNotBlank(query.getFileUrl())) {
            booleanBuilder.and(file.fileUrl.eq(query.getFileUrl()));
        }
        if (StringUtils.isNotBlank(query.getFileType())) {
            booleanBuilder.and(file.fileType.eq(query.getFileType()));
        }
        if (StringUtils.isNotBlank(query.getBizKey())) {
            booleanBuilder.and(file.bizKey.eq(query.getBizKey()));
        }
        if (StringUtils.isNotBlank(query.getBizType())) {
            booleanBuilder.and(file.bizType.eq(query.getBizType()));
        }
        return booleanBuilder;
    }

    @Override
    public FileDTO queryById(Long id) {
        File queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        FileDTO result = new FileDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public FileDTO save(FileSaveDTO saveDTO) {
        File newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new File());
        if (!saveDTO.getFileUrl().contains("https")) {
            saveDTO.setFileUrl(saveDTO.getFileUrl().replace("http", "https"));
        }


        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        List<File> files = repository.findAllById(ids);
        repository.deleteAll(files);
    }

    @Override
    public List<FileDTO> getFilesByBizKeyAndBizType(String bizKey, String bizType) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QFile qFile = QFile.file;
        booleanBuilder.and(qFile.isDeleted.eq(DeleteEnum.NO.getCode()));
        booleanBuilder.and(qFile.bizType.eq(bizType));
        if (StringUtils.isNotBlank(bizKey)) {
            booleanBuilder.and(qFile.bizKey.eq(bizKey));
        }

        return fileDEConvert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
    }

}
