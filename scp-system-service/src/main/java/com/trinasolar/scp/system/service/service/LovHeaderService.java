package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.common.api.base.BaseService;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.system.domain.dto.LovHeaderDTO;
import com.trinasolar.scp.system.domain.entity.LovHeader;
import com.trinasolar.scp.system.domain.feign.ItemAttrLovDTO;
import com.trinasolar.scp.system.domain.query.LovHeaderQuery;
import com.trinasolar.scp.system.domain.query.LovLineQuery;
import com.trinasolar.scp.system.domain.save.LovHeaderSaveDTO;
import org.springframework.data.domain.Page;

/**
 * lov头表(LovHeader)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-24 10:42:26
 */
public interface LovHeaderService extends BaseService<LovHeader, Long> {

    /**
     * 通过ID查询单条数据
     *
     * @param lovHeaderId 主键
     * @return 实例对象
     */
    LovHeaderDTO queryById(Long lovHeaderId);

    LovHeaderDTO save(LovHeaderSaveDTO lovHeaderSaveDTO) throws BizException;


    /**
     * 分页查询
     *
     * @param lovHeaderQuery 筛选条件
     * @return 查询结果
     */
    Page<LovHeader> queryByPage(LovHeaderQuery lovHeaderQuery);

    /**
     * 通过主键删除数据
     *
     * @param lovHeaderId 主键
     */
    default void delete(Long lovHeaderId) {
        deleteById(lovHeaderId);
    }

    LovHeaderDTO queryByLovQuery(LovLineQuery lovLineQuery);

    LovHeader queryByCode(String code);

    void syncItemAttrLov(ItemAttrLovDTO itemAttrLovDTO);

    void syncItemAttrLovByBattery(ItemAttrLovDTO itemAttrLovDTO);

    void clearCache(String code);
}
