package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.system.domain.dto.ErpSupplierDTO;
import com.trinasolar.scp.system.domain.dto.ErpSupplierSelectListDTO;
import com.trinasolar.scp.system.domain.entity.ErpSupplier;
import com.trinasolar.scp.system.domain.query.ErpSupplierQuery;
import com.trinasolar.scp.system.domain.query.ErpSupplierSelectQuery;
import com.trinasolar.scp.system.domain.save.ErpSupplierSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

/**
 * erp供应商表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-22 09:10:40
 */
public interface ErpSupplierService {
    /**
     * 分页获取erp供应商表
     *
     * @param query 查询对象
     * @return erp供应商表分页对象
     */
    Page<ErpSupplierDTO> queryByPage(ErpSupplierQuery query);

    /**
     * 根据主键获取erp供应商表详情
     *
     * @param id 主键
     * @return erp供应商表详情
     */
    ErpSupplierDTO queryById(Long id);

    /**
     * 保存或更新erp供应商表
     *
     * @param saveDTO erp供应商表保存对象
     * @return erp供应商表对象
     */
    ErpSupplierDTO save(ErpSupplierSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除erp供应商表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query
     * @param response
     */
    void export(ErpSupplierQuery query, HttpServletResponse response);

    /**
     * 导入
     *
     * @param multipartFile
     * @return 批次号
     */
    String importData(MultipartFile multipartFile, ExcelPara excelPara);

    void sync();

    List<ErpSupplierDTO> list(ErpSupplierQuery query);

    ErpSupplier findByVendorId(Long vendorId);

    void dailySync(LocalDateTime fromDateTime, LocalDateTime toDateTime);

    List<ErpSupplierSelectListDTO> selectList(ErpSupplierSelectQuery query);

    void syncFromLov();

    List<ErpSupplierDTO> findByLegalEntity(String legalEntity);

    List<Long> findBrandSuppliersIdsBySupplierId(String supplierId);

    List<String> findBrandBySupplierId(String id);

    ErpSupplierDTO queryByVendorAltName(String vendorAltName);

    List<String> queryAllSupplierName();

    List<ErpSupplierDTO> findSuppliersByBrand(String brand);

    ErpSupplierDTO getByName(String vendorName);

    ErpSupplierDTO getById(Long vendorId);

    List<ErpSupplierDTO> getAllErpSupplierDTOS();

    List<ErpSupplierDTO> getErpSupplierListBy(ErpSupplierQuery query);
}

