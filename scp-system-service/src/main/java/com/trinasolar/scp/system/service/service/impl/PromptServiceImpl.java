package com.trinasolar.scp.system.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.trinasolar.scp.common.api.enums.YesOrNoEnum;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import com.trinasolar.scp.system.domain.dto.PromptDTO;
import com.trinasolar.scp.system.domain.entity.Prompt;
import com.trinasolar.scp.system.domain.entity.QPrompt;
import com.trinasolar.scp.system.domain.query.PromptQuery;
import com.trinasolar.scp.system.domain.save.PromptSaveDTO;
import com.trinasolar.scp.system.service.repository.PromptRepository;
import com.trinasolar.scp.system.service.service.PromptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 前端多语言配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
@Slf4j
@Service("promptService")
public class PromptServiceImpl implements PromptService {
    @Autowired
    PromptRepository promptRepository;

    // 自定义查询条件
    QPrompt q = QPrompt.prompt;

    @Override
    public Page<Prompt> queryByPage(PromptQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        if (query != null) {
            if (StringUtils.isNotEmpty(query.getLang())) {
                booleanBuilder.and(q.lang.like("%" + query.getLang() + "%"));
            }
            if (StringUtils.isNotEmpty(query.getCode())) {
                booleanBuilder.and(q.code.like("%" + query.getCode() + "%"));
            }
            if (StringUtils.isNotEmpty(query.getKey())) {
                booleanBuilder.and(q.key.like("%" + query.getKey() + "%"));
            }
            if (StringUtils.isNotEmpty(query.getPlatform())) {
                booleanBuilder.and(q.platform.eq(query.getPlatform()));
            } else {
                booleanBuilder.and(q.platform.isNull().or(q.platform.isEmpty()));
            }
            if (StringUtils.isNotEmpty(query.getDescription())) {
                booleanBuilder.and(q.description.like("%" + query.getDescription() + "%"));
            }
        }

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        return promptRepository.findAll(booleanBuilder, pageable);
    }

    @Override
    public PromptDTO queryById(Long id) {
        Prompt queryObj = promptRepository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        PromptDTO result = new PromptDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Override
    public PromptDTO save(PromptSaveDTO saveDTO) {
        Prompt newObj;
        if (saveDTO.getPromptId() != null) {
            newObj = promptRepository.findOne(q.promptId.eq(saveDTO.getPromptId())).orElseThrow(() -> new RuntimeException("数据不存在"));
        } else {
            BooleanExpression and = q.lang.eq(saveDTO.getLang()).and(q.code.eq(saveDTO.getCode())).and(q.key.eq(saveDTO.getKey()));
            if (StringUtils.isNotBlank(saveDTO.getPlatform())) {
                and = and.and(q.platform.eq(saveDTO.getPlatform()));
            } else {
                and = and.and(q.platform.isNull().or(q.platform.isEmpty()));
            }
            newObj = promptRepository.findOne(and).orElse(new Prompt());
        }

        BeanUtils.copyProperties(saveDTO, newObj, "promptId");
        promptRepository.save(newObj);

        return this.queryById(newObj.getPromptId());
    }

    @Override
    public void deleteById(Long id) {
        promptRepository.deleteById(id);
    }

    @Override
    public Map<String, String> getDescription(PromptQuery query) {
        List<Prompt> prompts;
        BooleanExpression and = q.lang.eq(MyThreadLocal.get().getLang());
        if (query.getPlatform() == null) {
            and = and.and(q.platform.isNull().or(q.platform.isEmpty()));
        } else {
            and = and.and(q.platform.eq(query.getPlatform()));
        }

        if (StringUtils.isNotBlank(query.getKey())) {
            and = and.and(q.key.eq(query.getKey()));
        }
        prompts = IterableUtils.toList(promptRepository.findAll(and));

        Map<String, String> result = new HashMap<>(16);
        for (Prompt prompt : prompts) {
            result.put(prompt.getKey() + "." + prompt.getCode(), prompt.getDescription());
        }

        return result;
    }

    @Override
    public List<Prompt> getTransByDescription(PromptQuery query) {
        List<Prompt> prompts;
        BooleanExpression and = q.isDeleted.eq(0);
        //多语言
        if (query.getLang() == null) {
            //没传默认中文
            and = and.and(q.lang.eq("zh_CN"));
        } else {
            and = and.and(q.lang.eq(query.getLang()));
        }

        //平台
        if (query.getPlatform() == null) {
            and = and.and(q.platform.isNull().or(q.platform.isEmpty()));
        } else {
            and = and.and(q.platform.eq(query.getPlatform()));
        }

        //模块
        if (StringUtils.isNotBlank(query.getKey())) {
            and = and.and(q.key.eq(query.getKey()));
        }

        //描述
        if (StringUtils.isNotBlank(query.getDescription())) {
            and = and.and(q.description.eq(query.getDescription()));
        }

        prompts = IterableUtils.toList(promptRepository.findAll(and));

        return prompts;
    }


}
