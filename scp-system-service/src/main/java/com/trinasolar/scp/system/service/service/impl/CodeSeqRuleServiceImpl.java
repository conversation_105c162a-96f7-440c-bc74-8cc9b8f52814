package com.trinasolar.scp.system.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.system.domain.dto.CodeSeqRuleDTO;
import com.trinasolar.scp.system.domain.entity.CodeSeqRule;
import com.trinasolar.scp.system.domain.query.CodeSeqRuleQuery;
import com.trinasolar.scp.system.domain.save.CodeSeqRuleSaveDTO;
import com.trinasolar.scp.system.service.repository.CodeSeqRuleRepository;
import com.trinasolar.scp.system.service.service.CodeSeqRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 单据序列规则定义表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-09 15:05:51
 */
@Slf4j
@Service("codeSeqRuleService")
public class CodeSeqRuleServiceImpl implements CodeSeqRuleService {
    @Autowired
    CodeSeqRuleRepository repository;

    @Override
    public Page<CodeSeqRule> queryByPage(CodeSeqRuleQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        return repository.findAll(booleanBuilder, pageable);
    }

    @Override
    public CodeSeqRuleDTO queryById(Long id) {
        CodeSeqRule queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        CodeSeqRuleDTO result = new CodeSeqRuleDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CodeSeqRuleDTO save(CodeSeqRuleSaveDTO saveDTO) {
        CodeSeqRule newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new CodeSeqRule());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }
}
