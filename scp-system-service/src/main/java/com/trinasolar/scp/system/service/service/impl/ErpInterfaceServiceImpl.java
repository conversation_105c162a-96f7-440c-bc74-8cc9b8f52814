package com.trinasolar.scp.system.service.service.impl;

import com.trinasolar.scp.system.domain.dto.ExternalInterfaceDTO;
import com.trinasolar.scp.system.service.service.ErpInterfaceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2022/9/7
 */
@Slf4j
@Service("erpInterfaceService")
public class ErpInterfaceServiceImpl implements ErpInterfaceService {
    @Autowired
    RestTemplate restTemplate;

    @Override
    public String postForString(ExternalInterfaceDTO dto) {
        String url = dto.getUrl() + dto.getPath();

        log.info("接口调用URL: {}", url);
        HttpHeaders headers = new HttpHeaders();
        headers.set("tsl-clientid", dto.getClientId());
        headers.set("tsl-clientsecret", dto.getSecret());
        HttpEntity<String> httpEntity = new HttpEntity<>(dto.getRequestBody(), headers);
        String resultJson = null;
        try {
            resultJson = restTemplate.postForObject(url, httpEntity, String.class);
            log.info("接口返回: {}", resultJson);
        } catch (Exception e) {
            log.error("接口请求错误 url: {}  接口返回: {} 错误: {}", url, resultJson, e.getMessage());
            throw e;
        }
        return resultJson;
    }
}
