package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.Supplier;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 供应商信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-28 15:29:29
 */
@Repository
public interface SupplierRepository extends JpaRepository<Supplier, Long>, QuerydslPredicateExecutor<Supplier> {
}
