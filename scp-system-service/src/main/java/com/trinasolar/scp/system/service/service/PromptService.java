package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.PromptDTO;
import com.trinasolar.scp.system.domain.entity.Prompt;
import com.trinasolar.scp.system.domain.query.PromptQuery;
import com.trinasolar.scp.system.domain.save.PromptSaveDTO;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

/**
 * 前端多语言配置 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
public interface PromptService {
    Page<Prompt> queryByPage(PromptQuery query);

    PromptDTO queryById(Long id);

    PromptDTO save(PromptSaveDTO saveDTO);

    void deleteById(Long id);

    Map<String, String> getDescription(PromptQuery query);

    List<Prompt> getTransByDescription(PromptQuery query);
}

