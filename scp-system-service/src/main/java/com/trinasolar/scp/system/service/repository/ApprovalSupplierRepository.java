package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.ApprovalSupplier;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 批准供应商
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-26 20:32:43
 */
@Repository
public interface ApprovalSupplierRepository extends JpaRepository<ApprovalSupplier, Long>, QuerydslPredicateExecutor<ApprovalSupplier> {
}
