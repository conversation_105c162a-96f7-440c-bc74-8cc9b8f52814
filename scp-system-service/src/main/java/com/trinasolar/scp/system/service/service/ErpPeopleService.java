package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.system.domain.dto.ErpPeopleDTO;
import com.trinasolar.scp.system.domain.query.ErpPeopleQuery;
import com.trinasolar.scp.system.domain.save.ErpPeopleSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * erp人员信息 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 15:55:04
 */
public interface ErpPeopleService {
    /**
     * 分页获取erp人员信息
     *
     * @param query 查询对象
     * @return erp人员信息分页对象
     */
    Page<ErpPeopleDTO> queryByPage(ErpPeopleQuery query);

    /**
     * 根据主键获取erp人员信息详情
     *
     * @param id 主键
     * @return erp人员信息详情
     */
    ErpPeopleDTO queryById(Long id);

    /**
     * 保存或更新erp人员信息
     *
     * @param saveDTO erp人员信息保存对象
     * @return erp人员信息对象
     */
    ErpPeopleDTO save(ErpPeopleSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除erp人员信息
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query
     * @param response
     */
    void export(ErpPeopleQuery query, HttpServletResponse response);

    /**
     * 导入
     *
     * @param multipartFile
     * @return 批次号
     */
    String importData(MultipartFile multipartFile, ExcelPara excelPara);

    void sync();

    /**
     * 按条件获取人员信息
     *
     * @param query
     * @return
     */
    List<ErpPeopleDTO> queryByList(ErpPeopleQuery query);
}

