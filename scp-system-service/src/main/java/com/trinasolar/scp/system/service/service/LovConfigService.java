package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.LovConfigDTO;
import com.trinasolar.scp.system.domain.save.LovConfigSaveDTO;

import java.util.List;

/**
 * lov头表(LovHeader)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-24 10:42:26
 */
public interface LovConfigService {

    void save(List<LovConfigSaveDTO> lovConfigSaveDTOList, Long lovHeaderId);

    List<LovConfigDTO> listByHeaderId(Long lovHeaderId);
}
