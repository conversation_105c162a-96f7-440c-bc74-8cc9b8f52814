package com.trinasolar.scp.system.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.system.domain.dto.LovLineDTO;
import com.trinasolar.scp.system.domain.entity.Language;
import com.trinasolar.scp.system.domain.entity.LovHeader;
import com.trinasolar.scp.system.service.enums.LovTypeEnum;
import com.trinasolar.scp.system.service.repository.LanguageRepository;
import com.trinasolar.scp.system.service.service.LovCacheService;
import com.trinasolar.scp.system.service.service.LovHeaderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/2/6
 */
@Slf4j
@Service("lovCacheService")
public class LovCacheServiceImpl implements LovCacheService {
    @Autowired
    RedissonClient redissonClient;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    @Lazy
    private LovHeaderService lovHeaderService;

    @Autowired
    private LanguageRepository languageRepository;

    @Override
    public void clearLovHeaderCache(String lovHeaderCode) {
        if (StringUtils.isBlank(lovHeaderCode)) {
            return;
        }
        // 加分布式锁
        RLock lock = redissonClient.getLock(getLovHeaderUpdKey(lovHeaderCode));
        lock.lock();
        try {
            // 替代keys方法：明确删除已知的键类型，避免使用keys命令
            List<String> keysToDelete = new ArrayList<>();

            // 获取系统支持的所有语言
            List<String> supportedLanguages = getSupportedLanguages();

            // 1. 删除ID键 (Hash结构) - 包括基础键和各语言键
            String baseIdKey = LovUtils.getRedisIdKey(lovHeaderCode);
            if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(baseIdKey))) {
                keysToDelete.add(baseIdKey);
            }

            // 删除各语言版本的ID键
            for (String lang : supportedLanguages) {
                String langIdKey = LovUtils.getRedisIdKey(lang + lovHeaderCode);
                if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(langIdKey))) {
                    keysToDelete.add(langIdKey);
                }
            }

            // 2. 删除Value键 (Hash结构) - 包括基础键和各语言键
            String baseValueKey = LovUtils.getRedisValueKey(lovHeaderCode);
            if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(baseValueKey))) {
                keysToDelete.add(baseValueKey);
            }

            // 删除各语言版本的Value键
            for (String lang : supportedLanguages) {
                String langValueKey = LovUtils.getRedisValueKey(lang + lovHeaderCode);
                if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(langValueKey))) {
                    keysToDelete.add(langValueKey);
                }
            }

            // 3. 删除整体缓存键 (List结构) - 包含翻译和不含翻译两种
            String wholeCacheKey = LovUtils.getWholeLovCacheKey(lovHeaderCode, true);
            if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(wholeCacheKey))) {
                keysToDelete.add(wholeCacheKey);
            }

            String wholeCacheKeyNoTranslate = LovUtils.getWholeLovCacheKey(lovHeaderCode, false);
            if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(wholeCacheKeyNoTranslate))) {
                keysToDelete.add(wholeCacheKeyNoTranslate);
            }

            // 4. 删除名称缓存键 (Hash结构)
            String nameCacheKey = LovUtils.getWholeNameLovCacheKey(lovHeaderCode);
            if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(nameCacheKey))) {
                keysToDelete.add(nameCacheKey);
            }

            // 5. 根据实际键名模式删除多语言键 (基于您提供的键名格式)
            // 格式: SCP_LOVLINE[语言代码]HeaderCode
            for (String lang : supportedLanguages) {
                String multiLangKey = "SCP_LOVLINE" + lang + lovHeaderCode;
                if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(multiLangKey))) {
                    keysToDelete.add(multiLangKey);
                }
            }

            // 删除基础的多语言键（不带语言代码）
            String baseMultiLangKey = "SCP_LOVLINE" + lovHeaderCode;
            if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(baseMultiLangKey))) {
                keysToDelete.add(baseMultiLangKey);
            }

            // 批量删除所有相关键
            if (!keysToDelete.isEmpty()) {
                stringRedisTemplate.delete(keysToDelete);
                log.info("清除LOV缓存: {} 个键被删除, headerCode: {}", keysToDelete.size(), lovHeaderCode);
            }
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取系统支持的语言列表
     * @return 语言代码列表
     */
    private List<String> getSupportedLanguages() {
        try {
            List<Language> languages = languageRepository.findAll();
            List<String> languageCodes = new ArrayList<>();
            for (Language language : languages) {
                if (StringUtils.isNotBlank(language.getCode())) {
                    languageCodes.add(language.getCode());
                }
            }
            // 如果数据库中没有语言配置，使用默认的常用语言
            if (languageCodes.isEmpty()) {
                languageCodes = Arrays.asList("zh_CN", "en_US");
            }
            return languageCodes;
        } catch (Exception e) {
            log.warn("获取语言列表失败，使用默认语言: {}", e.getMessage());
            // 发生异常时使用默认语言
            return Arrays.asList("zh_CN", "en_US");
        }
    }

    @Override
    public void clearLovLinesFromRedis(List<LovLineDTO> lovLineList) {
        if (lovLineList == null || lovLineList.isEmpty()) {
            return;
        }
        for (LovLineDTO lovLine : lovLineList) {
            String type = lovLine.getLovHeaderCode();
            if (StringUtils.isNotEmpty(type)) {
                clearLovHeaderCache(type);
            }
        }
        LovUtils.clearCache();
    }

    @Override
    public String getLovHeaderUpdKey(String headerCode) {
        return "LOV:" + headerCode + ":WHOLE";
    }

    @Override
    public String getLovHeaderNameUpdKey(String headerCode) {
        return "LOV:" + headerCode + ":WHOLE:NAME";
    }

    @Override
    public void putLovLinesIntoRedis(List<com.trinasolar.scp.common.api.base.LovLineDTO> lovLineList) {
        if (lovLineList == null || lovLineList.isEmpty()) {
            return;
        }
        // type修改时,需要先全量清除typeV
//        Set<String> types = lovLineList.stream().map(LovLineDTO::getLovHeaderCode).collect(Collectors.toSet());
//        for (String type : types) {
//            stringRedisTemplate.delete(getWholeLovCacheKey(type));
//            stringRedisTemplate.delete(getRedisValueKey(type));
//        }

        for (com.trinasolar.scp.common.api.base.LovLineDTO lovLine : lovLineList) {
            String value = lovLine.getLovValue();
            String id = String.valueOf(lovLine.getLovLineId());
            String json = JSON.toJSONString(lovLine);
            String type = lovLine.getLovHeaderCode();
            if (StringUtils.isNotEmpty(type)) {
                //识别是否为动态SQL
                if (lovLine.getSourceSystemId() != null && lovLine.getSourceSystemId() != -9999) {
                    stringRedisTemplate.opsForHash().put(LovUtils.getRedisIdKey(""), id, json);
                }
                stringRedisTemplate.opsForHash().put(LovUtils.getRedisIdKey(type), id, json);
                stringRedisTemplate.opsForHash().put(LovUtils.getRedisValueKey(type), value, json);
            }
        }
    }

    @Override
    public List<LovLineDTO> getByHeaderCodeFromRedis(String headerCode, boolean withTranslateKey) {
        String redisWholeKeyFlag = LovUtils.getWholeLovCacheKey(headerCode, withTranslateKey);
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisWholeKeyFlag))) {
            List<LovLineDTO> lovLineDTOS = new ArrayList<>();
            List<String> hResult = stringRedisTemplate.opsForList().range(redisWholeKeyFlag, 0, -1);
            for (String value : hResult) {
                LovLineDTO lovLineDTO = JSON.parseObject(value, LovLineDTO.class);
                lovLineDTOS.add(lovLineDTO);
            }
            // 有整体缓存标记但是却没找数据，尝试再从DB查找
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(lovLineDTOS)) {
                return lovLineDTOS;
            }
        }
        return null;
    }
    @Override
    public void putLovHeaderCache(String headerCode, List<LovLineDTO> lovLineDTOS, boolean withTranslateKey) {
        String lovHeaderUpdKey = getLovHeaderUpdKey(headerCode);
        RLock lock = redissonClient.getLock(lovHeaderUpdKey);
        lock.lock();
        try {
            //设置整体缓存标识,失效时间(动态SQL设置为10分钟,LOV&ATTR设置较长时间)
            String redisWholeKeyFlag = LovUtils.getWholeLovCacheKey(headerCode, withTranslateKey);
            LovHeader lovHeader = lovHeaderService.queryByCode(headerCode);
            clearLovHeaderCache(headerCode);
            for (LovLineDTO lovLineDTO : lovLineDTOS) {
                stringRedisTemplate.opsForList().rightPush(redisWholeKeyFlag, JSON.toJSONString(lovLineDTO));
            }
            if (lovHeader != null && LovTypeEnum.DYNAMIC_SCRIPT.getValue().equals(lovHeader.getLovTypeId())) {
                stringRedisTemplate.expire(redisWholeKeyFlag, 10, TimeUnit.MINUTES);
            } else {
                stringRedisTemplate.expire(redisWholeKeyFlag, 30, TimeUnit.MINUTES);
            }
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void putNameToRedis(String headerCode, Map<String, LovLineDTO> queryLocalizedLovLinesFromDB) {
        String redisWholeKeyFlag = LovUtils.getWholeNameLovCacheKey(headerCode);
        HashMap<String, LovLineDTO> result = null;
        if (Boolean.FALSE.equals(stringRedisTemplate.hasKey(redisWholeKeyFlag))) {
            try {
                queryLocalizedLovLinesFromDB.forEach((key, value) -> stringRedisTemplate.opsForHash()
                        .put(redisWholeKeyFlag, key, JSON.toJSONString(value)));
                stringRedisTemplate.expire(redisWholeKeyFlag, 30, TimeUnit.MINUTES);
            } catch (Exception e) {
                stringRedisTemplate.delete(redisWholeKeyFlag);
                throw e;
            }
        }
    }

}
