package com.trinasolar.scp.system.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.system.domain.dto.LovLineDTO;
import com.trinasolar.scp.system.domain.entity.LovHeader;
import com.trinasolar.scp.system.service.enums.LovTypeEnum;
import com.trinasolar.scp.system.service.service.LovCacheService;
import com.trinasolar.scp.system.service.service.LovHeaderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/2/6
 */
@Slf4j
@Service("lovCacheService")
public class LovCacheServiceImpl implements LovCacheService {
    @Autowired
    RedissonClient redissonClient;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    @Lazy
    private LovHeaderService lovHeaderService;

    @Override
    public void clearLovHeaderCache(String lovHeaderCode) {
        if (StringUtils.isBlank(lovHeaderCode)) {
            return;
        }
        // 加分布式锁
        RLock lock = redissonClient.getLock(getLovHeaderUpdKey(lovHeaderCode));
        lock.lock();
        try {
            // 使用 SCAN 替代 KEYS 命令，避免阻塞 Redis

            // 扫描所有可能的键模式
            Set<String> keysToDelete = new HashSet<>(scanKeys( lovHeaderCode + "*"));

            // 批量删除找到的键
            if (!keysToDelete.isEmpty()) {
                stringRedisTemplate.delete(keysToDelete);
                log.info("使用SCAN清除LOV缓存: {} 个键被删除, headerCode: {}, 键列表: {}",
                        keysToDelete.size(), lovHeaderCode, keysToDelete);
            } else {
                log.info("使用SCAN清除LOV缓存: 未找到相关键, headerCode: {}", lovHeaderCode);
            }
        } finally {
            lock.unlock();
        }
    }

    /**
     * 使用 SCAN 命令查找匹配的键（替代 KEYS 命令）
     * @param pattern 键的匹配模式
     * @return 匹配的键集合
     */
    private Set<String> scanKeys(String pattern) {
        Set<String> keys = new HashSet<>();
        try {
            // 方法1：使用 RedisTemplate 的 execute 方法
            keys = stringRedisTemplate.execute((RedisCallback<Set<String>>) connection -> {
                Set<String> result = new HashSet<>();
                ScanOptions options = ScanOptions.scanOptions()
                        .match(pattern)
                        .count(100)
                        .build();

                Cursor<byte[]> cursor = connection.scan(options);
                while (cursor.hasNext()) {
                    result.add(new String(cursor.next()));
                }
                cursor.close();
                return result;
            });
        } catch (Exception e) {
            log.error("SCAN 命令执行失败，pattern: {}, error: {}", pattern, e.getMessage());

            // 降级方案：如果 SCAN 失败，可以考虑使用受限的 KEYS（仅在必要时）
            try {
                log.warn("SCAN 失败，降级使用 KEYS 命令，pattern: {}", pattern);
                keys = stringRedisTemplate.keys(pattern);
                if (keys == null) {
                    keys = new HashSet<>();
                }
            } catch (Exception fallbackException) {
                log.error("KEYS 降级方案也失败，pattern: {}, error: {}", pattern, fallbackException.getMessage());
                keys = new HashSet<>();
            }
        }
        return keys;
    }

    @Override
    public void clearLovLinesFromRedis(List<LovLineDTO> lovLineList) {
        if (lovLineList == null || lovLineList.isEmpty()) {
            return;
        }
        for (LovLineDTO lovLine : lovLineList) {
            String type = lovLine.getLovHeaderCode();
            if (StringUtils.isNotEmpty(type)) {
                clearLovHeaderCache(type);
            }
        }
        LovUtils.clearCache();
    }

    @Override
    public String getLovHeaderUpdKey(String headerCode) {
        return "LOV:" + headerCode + ":WHOLE";
    }

    @Override
    public String getLovHeaderNameUpdKey(String headerCode) {
        return "LOV:" + headerCode + ":WHOLE:NAME";
    }

    @Override
    public void putLovLinesIntoRedis(List<com.trinasolar.scp.common.api.base.LovLineDTO> lovLineList) {
        if (lovLineList == null || lovLineList.isEmpty()) {
            return;
        }
        // type修改时,需要先全量清除typeV
//        Set<String> types = lovLineList.stream().map(LovLineDTO::getLovHeaderCode).collect(Collectors.toSet());
//        for (String type : types) {
//            stringRedisTemplate.delete(getWholeLovCacheKey(type));
//            stringRedisTemplate.delete(getRedisValueKey(type));
//        }

        for (com.trinasolar.scp.common.api.base.LovLineDTO lovLine : lovLineList) {
            String value = lovLine.getLovValue();
            String id = String.valueOf(lovLine.getLovLineId());
            String json = JSON.toJSONString(lovLine);
            String type = lovLine.getLovHeaderCode();
            if (StringUtils.isNotEmpty(type)) {
                //识别是否为动态SQL
                if (lovLine.getSourceSystemId() != null && lovLine.getSourceSystemId() != -9999) {
                    stringRedisTemplate.opsForHash().put(LovUtils.getRedisIdKey(""), id, json);
                }
                stringRedisTemplate.opsForHash().put(LovUtils.getRedisIdKey(type), id, json);
                stringRedisTemplate.opsForHash().put(LovUtils.getRedisValueKey(type), value, json);
            }
        }
    }

    @Override
    public List<LovLineDTO> getByHeaderCodeFromRedis(String headerCode, boolean withTranslateKey) {
        String redisWholeKeyFlag = LovUtils.getWholeLovCacheKey(headerCode, withTranslateKey);
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisWholeKeyFlag))) {
            List<LovLineDTO> lovLineDTOS = new ArrayList<>();
            List<String> hResult = stringRedisTemplate.opsForList().range(redisWholeKeyFlag, 0, -1);
            for (String value : hResult) {
                LovLineDTO lovLineDTO = JSON.parseObject(value, LovLineDTO.class);
                lovLineDTOS.add(lovLineDTO);
            }
            // 有整体缓存标记但是却没找数据，尝试再从DB查找
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(lovLineDTOS)) {
                return lovLineDTOS;
            }
        }
        return null;
    }
    @Override
    public void putLovHeaderCache(String headerCode, List<LovLineDTO> lovLineDTOS, boolean withTranslateKey) {
        String lovHeaderUpdKey = getLovHeaderUpdKey(headerCode);
        RLock lock = redissonClient.getLock(lovHeaderUpdKey);
        lock.lock();
        try {
            //设置整体缓存标识,失效时间(动态SQL设置为10分钟,LOV&ATTR设置较长时间)
            String redisWholeKeyFlag = LovUtils.getWholeLovCacheKey(headerCode, withTranslateKey);
            LovHeader lovHeader = lovHeaderService.queryByCode(headerCode);
            clearLovHeaderCache(headerCode);
            for (LovLineDTO lovLineDTO : lovLineDTOS) {
                stringRedisTemplate.opsForList().rightPush(redisWholeKeyFlag, JSON.toJSONString(lovLineDTO));
            }
            if (lovHeader != null && LovTypeEnum.DYNAMIC_SCRIPT.getValue().equals(lovHeader.getLovTypeId())) {
                stringRedisTemplate.expire(redisWholeKeyFlag, 10, TimeUnit.MINUTES);
            } else {
                stringRedisTemplate.expire(redisWholeKeyFlag, 30, TimeUnit.MINUTES);
            }
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void putNameToRedis(String headerCode, Map<String, LovLineDTO> queryLocalizedLovLinesFromDB) {
        String redisWholeKeyFlag = LovUtils.getWholeNameLovCacheKey(headerCode);
        HashMap<String, LovLineDTO> result = null;
        if (Boolean.FALSE.equals(stringRedisTemplate.hasKey(redisWholeKeyFlag))) {
            try {
                queryLocalizedLovLinesFromDB.forEach((key, value) -> stringRedisTemplate.opsForHash()
                        .put(redisWholeKeyFlag, key, JSON.toJSONString(value)));
                stringRedisTemplate.expire(redisWholeKeyFlag, 30, TimeUnit.MINUTES);
            } catch (Exception e) {
                stringRedisTemplate.delete(redisWholeKeyFlag);
                throw e;
            }
        }
    }

}
