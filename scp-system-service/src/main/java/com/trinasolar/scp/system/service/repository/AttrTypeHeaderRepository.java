package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.AttrTypeHeader;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-04-24 10:42:24
 */
@Repository
public interface AttrTypeHeaderRepository
        extends JpaRepository<AttrTypeHeader, Long>, QuerydslPredicateExecutor<AttrTypeHeader> {

    @Query(value = "SELECT * FROM sys_attr_type_header WHERE sys_attr_type_header.attr_type_code= ?1 AND is_deleted=0",
            nativeQuery = true)
    AttrTypeHeader getByCode(String attrTypeCode);
    @Query(value = "SELECT * FROM sys_attr_type_header WHERE sys_attr_type_header.attr_category_id= ?1 AND sys_attr_type_header.updated_time>=?2 AND sys_attr_type_header.attr_type_code LIKE 'SCR%' ",
            nativeQuery = true)
    List<AttrTypeHeader> queryAllByParams(Integer attrCategoryId,String lastUpdatedTime);

    @Query(value = "SELECT * FROM sys_attr_type_header WHERE attr_type_header_id in (?1) AND is_deleted=0",
            nativeQuery = true)
    List<AttrTypeHeader> getByAttrTypeHeaderIds(List<Long> attrTypeHeaderIds);
}

