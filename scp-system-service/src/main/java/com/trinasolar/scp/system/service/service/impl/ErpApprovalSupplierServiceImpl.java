package com.trinasolar.scp.system.service.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.enums.YesOrNoEnum;
import com.trinasolar.scp.common.api.util.*;
import com.trinasolar.scp.system.domain.constant.IsOverseaEnum;
import com.trinasolar.scp.system.domain.constant.RocketMqConstant;
import com.trinasolar.scp.system.domain.convert.ErpApprovalSupplierDEConvert;
import com.trinasolar.scp.system.domain.dto.*;
import com.trinasolar.scp.system.domain.entity.ErpApprovalSupplier;
import com.trinasolar.scp.system.domain.entity.QErpApprovalSupplier;
import com.trinasolar.scp.system.domain.entity.QErpSupplier;
import com.trinasolar.scp.system.domain.feign.ItemCodesQuery;
import com.trinasolar.scp.system.domain.feign.ItemsDTO;
import com.trinasolar.scp.system.domain.query.ErpApprovalSupplierQuery;
import com.trinasolar.scp.system.domain.save.ErpApprovalSupplierSaveDTO;
import com.trinasolar.scp.system.service.enums.ErpApprovalSupplierConfirmStatusEnum;
import com.trinasolar.scp.system.service.feign.BomFeign;
import com.trinasolar.scp.system.service.repository.ErpApprovalSupplierRepository;
import com.trinasolar.scp.system.service.repository.ErpSupplierRepository;
import com.trinasolar.scp.system.service.service.ErpApprovalSupplierFixLotQtyService;
import com.trinasolar.scp.system.service.service.ErpApprovalSupplierService;
import com.trinasolar.scp.system.service.service.ErpInterfaceService;
import com.trinasolar.scp.system.service.service.ErpSupplierService;
import com.trinasolar.scp.system.service.utils.ReadExcelAndCheckUtils;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 批准供应商
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:05:46
 */
@Slf4j
@Service("erpApprovalSupplierService")
@CacheConfig(cacheManager = "scpRedisCacheManager")
@RefreshScope
public class ErpApprovalSupplierServiceImpl implements ErpApprovalSupplierService {
    @Autowired
    private ErpSupplierRepository erpSupplierRepository;

    private static final int PAGE_SIZE = 600;

    QErpApprovalSupplier qErpApprovalSupplier = QErpApprovalSupplier.erpApprovalSupplier;

    QErpSupplier qErpSupplier = QErpSupplier.erpSupplier;

    static TimedCache<String, Map<String, List<ErpApprovalSupplierDTO>>> approvalSupplierCache = CacheUtil.newTimedCache(30 * 1000);

    @Autowired
    ErpApprovalSupplierRepository repository;

    @Autowired
    ErpApprovalSupplierDEConvert erpApprovalSupplierDEConvert;

    @Autowired
    ExecutorService threadPoolExecutor;

    @Autowired
    JPAQueryFactory jpaQueryFactory;

    @Autowired
    ErpInterfaceService erpInterfaceService;

    @Autowired
    ErpSupplierService erpSupplierService;

    @Autowired
    @Lazy
    ErpApprovalSupplierService erpApprovalSupplierService;

    @Autowired
    @Lazy
    ErpApprovalSupplierFixLotQtyService erpApprovalSupplierFixLotQtyService;

    @Autowired
    BomFeign bomFeign;

    @Value("${erp.interface.vendor.url}")
    private String erpUrl;

    @Value("${erp.interface.vendor.clientid}")
    private String la002ClientId;

    @Value("${erp.interface.vendor.secret}")
    private String la002Secret;

    @Setter(onMethod_ = @Autowired)
    private RocketMQTemplate rocketMQTemplate;


    @Override
    public Page<ErpApprovalSupplierDTO> queryByPage(ErpApprovalSupplierQuery query) {
        Map<String, LovLineDTO> lovList = LovUtils.getAllByHeaderCode("ATTR_TYPE_APS_SUPPLIER");
        List<String> lovValues = lovList.values().stream().distinct().map(LovLineDTO::getLovValue).collect(Collectors.toList());

        JPAQuery<Tuple> jpaQuery = jpaQueryFactory.select(
                        qErpApprovalSupplier.itemNum,
                        qErpApprovalSupplier.itemCategory,
                        qErpApprovalSupplier.priUom,
                        qErpApprovalSupplier.vendorId,
                        qErpApprovalSupplier.vendorName,
                        qErpApprovalSupplier.aslStatus,
                        qErpApprovalSupplier.fixLotQty,
                        qErpApprovalSupplier.leadTime,
                        qErpApprovalSupplier.confirmStatus,
                        qErpApprovalSupplier.categorySegment5
                )
                .from(qErpApprovalSupplier).leftJoin(qErpSupplier).on(qErpSupplier.vendorId.eq(qErpApprovalSupplier.vendorId));
//                .distinct();
        //请帮忙筛选掉供应商表字段： attribute2 为 'Y'的数据，保留为空或为“N”的数据。
//        JPAQuery<Long> vendorIds = jpaQueryFactory.select(qErpSupplier.vendorId).from(qErpSupplier)
//                .where(qErpSupplier.attribute2.ne("Y"));
        //放开合格供应商使能查询到
//        jpaQuery.where(qErpSupplier.attribute2.ne("Y"));
        buildWhere(jpaQuery, query);
        //辅料类型必须是维护在lov中的
        jpaQuery.where(qErpApprovalSupplier.categorySegment5.in(lovValues));

//        long count = jpaQuery.fetchCount();
        List<Tuple> data = jpaQuery.fetch().stream().distinct().collect(Collectors.toList());
        List<Tuple> fetch = new ArrayList<>();

        // #24538 【组件辅材需求管理】—合格供应商属性维护增加列 增加整车数信息
        Map<String, List<ErpApprovalSupplierFixLotQtyDTO>> itemCodeAndVendorIdMap = erpApprovalSupplierFixLotQtyService.getItemCodeAndVendorIdMap();

        if (StringUtils.isNotBlank(query.getHasPurchaseQuantity())) {
            if (YesOrNoEnum.YES.getCode().equals(query.getHasPurchaseQuantity())) {
                data = data.stream().filter(i -> itemCodeAndVendorIdMap.containsKey(i.get(qErpApprovalSupplier.itemNum) + "_" + i.get(qErpApprovalSupplier.vendorId)))
                        .collect(Collectors.toList());
            } else if (YesOrNoEnum.NO.getCode().equals(query.getHasPurchaseQuantity())) {
                data = data.stream().filter(i -> !itemCodeAndVendorIdMap.containsKey(i.get(qErpApprovalSupplier.itemNum) + "_" + i.get(qErpApprovalSupplier.vendorId)))
                        .collect(Collectors.toList());
            } else {
                throw new BizException("是否包含整车数必须为 Y/N");
            }
        }

        if (query.getPageNumber() * query.getPageSize() <= data.size()) {
            fetch = data.subList((query.getPageNumber() - 1) * query.getPageSize(), query.getPageNumber() * query.getPageSize());
        } else {
            fetch = data.subList((query.getPageNumber() - 1) * query.getPageSize(), data.size());
        }

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        List<ErpApprovalSupplierDTO> result = new ArrayList<>();
        for (Tuple tuple : fetch) {
            ErpApprovalSupplierDTO dto = new ErpApprovalSupplierDTO();
            dto.setItemNum(tuple.get(qErpApprovalSupplier.itemNum));
            // TODO itemDescription先不展示
//            dto.setItemDescription(tuple.get(qErpApprovalSupplier.itemDescription));
            dto.setItemCategory(tuple.get(qErpApprovalSupplier.itemCategory));
            dto.setPriUom(tuple.get(qErpApprovalSupplier.priUom));
            dto.setVendorId(tuple.get(qErpApprovalSupplier.vendorId));
            dto.setVendorName(tuple.get(qErpApprovalSupplier.vendorName));
            dto.setAslStatus(tuple.get(qErpApprovalSupplier.aslStatus));
            dto.setFixLotQty(tuple.get(qErpApprovalSupplier.fixLotQty));
            dto.setLeadTime(tuple.get(qErpApprovalSupplier.leadTime));
            dto.setConfirmStatus(tuple.get(qErpApprovalSupplier.confirmStatus));
            dto.setCategorySegment5(tuple.get(qErpApprovalSupplier.categorySegment5));
            List<ErpApprovalSupplierFixLotQtyDTO> fixLotQtyDTOS = itemCodeAndVendorIdMap.getOrDefault(
                    tuple.get(qErpApprovalSupplier.itemNum) + "_" + tuple.get(qErpApprovalSupplier.vendorId),
                    Collections.emptyList());
            Map<String, List<ErpApprovalSupplierFixLotQtyDTO>> isOverseaAndLotQtyDTOS = fixLotQtyDTOS
                    .stream().collect(Collectors.groupingBy(i -> Optional.ofNullable(i.getIsOversea()).orElse("")));
            StringBuilder sb = new StringBuilder();
            isOverseaAndLotQtyDTOS.forEach((isOversea, l) -> {
                if (StringUtils.isBlank(isOversea)) {
                    sb.append("国内海外全部基地");
                    return;
                }
                String basePlaces = l.stream().map(ErpApprovalSupplierFixLotQtyDTO::getBasePlace).filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(","));
                if (StringUtils.isBlank(basePlaces)) {
                    sb.append("[").append(IsOverseaEnum.getDesc(isOversea)).append(" : 全部基地];");
                } else {
                    sb.append("[").append(IsOverseaEnum.getDesc(isOversea)).append(" : ").append(basePlaces).append("];");
                }
            });
            dto.setPurchaseQuantityInfo(sb.toString());
            dto.setHasPurchaseQuantity(CollectionUtils.isEmpty(fixLotQtyDTOS) ? YesOrNoEnum.NO.getDesc() : YesOrNoEnum.YES.getDesc());

            result.add(dto);

        }
        // 继续设置先去获取物料名称,再设置
        Set<String> collect = result.stream().map(ErpApprovalSupplierDTO::getItemNum).collect(Collectors.toSet());
        ItemCodesQuery itemCodesQuery = new ItemCodesQuery();
        itemCodesQuery.setItemCodes(new ArrayList<>(collect));
        Map<String, String> itemCodeAndDescMap = bomFeign.findItemDescByItemCodes(itemCodesQuery).getBody().getData();
        result.forEach(item -> {
            item.setItemDescription(itemCodeAndDescMap.get(item.getItemNum()));
            //设置厂牌
            List<String> brands = erpSupplierService.findBrandBySupplierId(item.getVendorId().toString());
            item.setBrands(StringUtils.join(brands, ","));
        });

        return new PageImpl(result, pageable, data.size());
    }

    private void buildWhere(JPAQuery<Tuple> jpaQuery, ErpApprovalSupplierQuery query) {
        if (query.getItemId() != null) {
            jpaQuery.where(qErpApprovalSupplier.itemId.eq(query.getItemId()));
        }
        if (StringUtils.isNotBlank(query.getItemNum())) {
            jpaQuery.where(qErpApprovalSupplier.itemNum.eq(query.getItemNum()));
        }
        if (query.getVendorId() != null) {
            jpaQuery.where(qErpApprovalSupplier.vendorId.eq(query.getVendorId()));
        }
        if (StringUtils.isNotBlank(query.getVendorName())) {
            jpaQuery.where(qErpApprovalSupplier.vendorName.eq(query.getVendorName()));
        }

        if (query.getStatusId() != null) {
            jpaQuery.where(qErpApprovalSupplier.statusId.eq(query.getStatusId()));
        }
        if (StringUtils.isNotBlank(query.getAslStatus())) {
            jpaQuery.where(qErpApprovalSupplier.aslStatus.eq(query.getAslStatus()));
        }
        if (StringUtils.isNotBlank(query.getConfirmStatus())) {
            jpaQuery.where(qErpApprovalSupplier.confirmStatus.eq(query.getConfirmStatus()));
        }
        if (CollectionUtils.isNotEmpty(query.getCategorySegment5())) {
            jpaQuery.where(qErpApprovalSupplier.categorySegment5.in(query.getCategorySegment5()));
        }
        jpaQuery.where(qErpApprovalSupplier.categorySegment5.isNotNull());
        jpaQuery.where(qErpApprovalSupplier.isDeleted.eq(0));
        jpaQuery.where(qErpSupplier.isDeleted.eq(0));
        //去除状态为”已排除“的
        jpaQuery.where(qErpApprovalSupplier.aslStatus.ne("已排除"));

        jpaQuery.orderBy(qErpApprovalSupplier.vendorId.desc());
    }

    private void buildWherePage(JPAQuery<Tuple> jpaQuery) {
        jpaQuery.where(qErpApprovalSupplier.isDeleted.eq(0));
        jpaQuery.where(qErpSupplier.isDeleted.eq(0));
        jpaQuery.orderBy(qErpApprovalSupplier.vendorId.desc());
    }

    /**
     * 构建查询条件
     *
     * @param query
     * @return
     */
    private BooleanBuilder buildWhere(ErpApprovalSupplierQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QErpApprovalSupplier erpApprovalSupplier = QErpApprovalSupplier.erpApprovalSupplier;

        if (query.getItemId() != null) {
            booleanBuilder.and(erpApprovalSupplier.itemId.eq(query.getItemId()));
        }
        if (StringUtils.isNotBlank(query.getItemNum())) {
            booleanBuilder.and(erpApprovalSupplier.itemNum.eq(query.getItemNum()));
        }
        if (query.getVendorId() != null) {
            booleanBuilder.and(erpApprovalSupplier.vendorId.eq(query.getVendorId()));
        }
        if (StringUtils.isNotBlank(query.getVendorName())) {
            booleanBuilder.and(erpApprovalSupplier.vendorName.eq(query.getVendorName()));
        }

        if (query.getStatusId() != null) {
            booleanBuilder.and(erpApprovalSupplier.statusId.eq(query.getStatusId()));
        }
        if (StringUtils.isNotBlank(query.getAslStatus())) {
            booleanBuilder.and(erpApprovalSupplier.aslStatus.eq(query.getAslStatus()));
        }
        if (StringUtils.isNotBlank(query.getConfirmStatus())) {
            booleanBuilder.and(erpApprovalSupplier.confirmStatus.eq(query.getConfirmStatus()));
        }
        if (CollectionUtils.isNotEmpty(query.getCategorySegment5())) {
            booleanBuilder.and(erpApprovalSupplier.categorySegment5.in(query.getCategorySegment5()));
        }
        booleanBuilder.and(erpApprovalSupplier.categorySegment5.isNotNull());

        //请帮忙赛选掉供应商表字段： attribute2 为 'Y'的数据，保留为空或为“N”的数据。
        JPAQuery<Long> vendorIds = jpaQueryFactory.select(qErpSupplier.vendorId).from(qErpSupplier)
                .where(qErpSupplier.attribute2.ne("Y"));
        booleanBuilder.and(erpApprovalSupplier.vendorId.in(vendorIds));

        return booleanBuilder;
    }

    @Override
    public ErpApprovalSupplierDTO queryById(Long id) {
        ErpApprovalSupplier queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        ErpApprovalSupplierDTO result = new ErpApprovalSupplierDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    /**
     * 查询4A 7A料号
     *
     * @return
     */
    @Override
    public ErpApprovalSupplierPageNumDTO querySupplierPage(ErpApprovalSupplierQuery query) {
        JPAQuery<Tuple> jpaQuery = jpaQueryFactory.select(
                        qErpApprovalSupplier.itemId,
                        qErpApprovalSupplier.itemDescription,
                        qErpApprovalSupplier.vendorId,
                        qErpApprovalSupplier.vendorName,
                        qErpSupplier.vendorNameAlt,
                        qErpApprovalSupplier.aslStatus
                )
                .from(qErpApprovalSupplier).leftJoin(qErpSupplier).on(qErpApprovalSupplier.vendorId.eq(qErpSupplier.vendorId));
        buildWherePage(jpaQuery);

        List<Tuple> data = jpaQuery.fetch().stream().distinct().collect(Collectors.toList());
        List<Tuple> fetch = new ArrayList<>();
        if (query.getPageNumber() * query.getPageSize() <= data.size()) {
            fetch = data.subList((query.getPageNumber() - 1) * query.getPageSize(), query.getPageNumber() * query.getPageSize());
        } else {
            fetch = data.subList((query.getPageNumber() - 1) * query.getPageSize(), data.size());
        }

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        ErpApprovalSupplierPageNumDTO pageNumDTO = new ErpApprovalSupplierPageNumDTO();
        pageNumDTO.setPageSize(query.getPageSize());
        pageNumDTO.setPageNumber(query.getPageNumber());
        List<ErpApprovalSupplierPageDTO> dataList = new ArrayList<>();
        for (Tuple tuple : fetch) {
            ErpApprovalSupplierPageDTO dto = new ErpApprovalSupplierPageDTO();
            dto.setItemId(tuple.get(qErpApprovalSupplier.itemId));
            dto.setItemDescription(tuple.get(qErpApprovalSupplier.itemDescription));
            dto.setVendorId(tuple.get(qErpApprovalSupplier.vendorId));
            dto.setVendorName(tuple.get(qErpApprovalSupplier.vendorName));
            dto.setVendorNameAlt(tuple.get(qErpSupplier.vendorNameAlt));
            dto.setAslStatus(tuple.get(qErpApprovalSupplier.aslStatus));
            dataList.add(dto);
        }
        if (CollectionUtils.isNotEmpty(dataList)) {
            pageNumDTO.setDate(dataList);
        }
        pageNumDTO.setTotalPages(getPageCount(data.size(), pageNumDTO.getPageSize()));
        return pageNumDTO;
    }

    /**
     * 计算总页数
     *
     * @param totalCount
     * @param pageSize
     * @return
     */
    public int getPageCount(int totalCount, int pageSize) {
        return (totalCount - 1) / pageSize + 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ErpApprovalSupplierDTO save(ErpApprovalSupplierSaveDTO saveDTO) {
        List<ErpApprovalSupplier> list = repository.findByItemNumAndVendorName(saveDTO.getItemNum(), saveDTO.getVendorName());
        list.forEach(one -> {
            one.setFixLotQty(saveDTO.getFixLotQty());
            one.setLeadTime(saveDTO.getLeadTime());
            if (ErpApprovalSupplierConfirmStatusEnum.CONFIRMED.getValue().equals(saveDTO.getConfirmStatus())) {
                one.setConfirmStatus(ErpApprovalSupplierConfirmStatusEnum.CONFIRMED.getValue());
            } else {
                one.setConfirmStatus(ErpApprovalSupplierConfirmStatusEnum.UNCONFIRMED.getValue());
            }
            repository.save(one);
        });

        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @SneakyThrows
    @Override
    public void export(ErpApprovalSupplierQuery query, HttpServletResponse response) {
        List<ErpApprovalSupplierDTO> dtos = queryByPage(query).getContent();
        ExcelPara excelPara = JSON.parseObject(query.getExcelPara(), ExcelPara.class);
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "批准供应商", "批准供应商", excelPara.getSimpleHeader(), excelData);
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String importData(MultipartFile multipartFile, ExcelPara excelPara) {
        String batchNo = UUID.randomUUID().toString();
//        List<ErpApprovalSupplierDTO> excelData =
//                ExcelUtils.readExcel(multipartFile.getInputStream(), null, ErpApprovalSupplierDTO.class, excelPara);

        List<ErpApprovalSupplierDTO> excelData = ReadExcelAndCheckUtils.readExcelData(multipartFile.getInputStream(), null, ErpApprovalSupplierDTO.class, excelPara);

        log.info("{}条数据，开始存储数据库！", excelData.size());
        // 只保存数量和状态
        List<ErpApprovalSupplier> entities = erpApprovalSupplierDEConvert.toEntity(excelData);
        entities.forEach(item -> {
            List<ErpApprovalSupplier> list = repository.findByItemNumAndVendorName(item.getItemNum(), item.getVendorName());
            list.forEach(one -> {
                one.setFixLotQty(item.getFixLotQty());
                one.setLeadTime(item.getLeadTime());
                if (ErpApprovalSupplierConfirmStatusEnum.CONFIRMED.getValue().equals(item.getConfirmStatus())) {
                    one.setConfirmStatus(ErpApprovalSupplierConfirmStatusEnum.CONFIRMED.getValue());
                } else {
                    one.setConfirmStatus(ErpApprovalSupplierConfirmStatusEnum.UNCONFIRMED.getValue());
                }
                repository.save(one);
            });
        });
        log.info("存储数据库成功！");

        return batchNo;
    }

    @Override
    public void sync() {
        LocalDateTime currentTime = LocalDateTimeUtil.parse("2009-01-01T00:00:00");
        LocalDateTime now = LocalDateTime.now();
        for (; ; ) {
            if (currentTime.isAfter(now)) {
                break;
            }
            LocalDateTime endTime = currentTime.plusMonths(1L);
            String lastUpdateDateFrom = LocalDateTimeUtil.formatNormal(currentTime);
            String lastUpdateDateTo = LocalDateTimeUtil.formatNormal(endTime);
            currentTime = endTime;

            threadPoolExecutor.execute(() -> {
                Integer page = 1;
                for (; ; ) {
                    Map<String, String> data = new HashMap<>();
                    data.put("last_update_date_from", lastUpdateDateFrom);
                    data.put("last_update_date_to", lastUpdateDateTo);
                    data.put("pageSize", "1000");
                    data.put("pageNum", page.toString());
                    page++;
                    ExternalInterfaceDTO params = createParams(data);

                    String json = erpInterfaceService.postForString(params);
                    JSONObject outJson = JSON.parseObject(json);
                    JSONArray jsonArray = outJson.getJSONArray("data");
                    if (jsonArray == null) {
                        return;
                    }
                    if (jsonArray.size() == 0) {
                        break;
                    }
                    List<ErpApprovalSupplier> updateList = new CopyOnWriteArrayList<>();
                    for (int i = 0; i < jsonArray.size(); i++) {
                        ErpApprovalSupplier line = jsonArray.getObject(i, ErpApprovalSupplier.class);
                        saveBySync(line);
                        updateList.add(line);
                    }
                    sendToMq(updateList);
                }
            });
        }
    }

    private void sendToMq(List<ErpApprovalSupplier> updateList) {
        for (ErpApprovalSupplier erpApprovalSupplier : updateList) {
            ErpApprovalSupplierPageDTO dto = new ErpApprovalSupplierPageDTO();
            dto.setItemId(erpApprovalSupplier.getItemId());
            dto.setItemNum(erpApprovalSupplier.getItemNum());
            dto.setItemDescription(erpApprovalSupplier.getItemDescription());
            dto.setVendorId(erpApprovalSupplier.getVendorId());
            dto.setVendorName(erpApprovalSupplier.getVendorName());
            ErpSupplierDTO byId = erpSupplierService.getById(erpApprovalSupplier.getVendorId());
            if (byId != null) {
                dto.setVendorNameAlt(byId.getVendorNameAlt());
            }
            dto.setAslStatus(erpApprovalSupplier.getAslStatus());

            Message<String> msg = MessageBuilder.withPayload(JSON.toJSONString(dto)).build();
            // 判断是否为不合格的供应商(冻结的供应商), 如果是则携带不合格供应商的tag
            if ("批准".equals(dto.getAslStatus()) || "研发".equals(dto.getAslStatus())) {
                SendResult sendResult = rocketMQTemplate.syncSend(RocketMqConstant.approvalSupplierUpdTopic, msg);
                if (log.isDebugEnabled()) {
                    log.debug("发送消息到MQ, topic: {}, msg: {}, sendResult: {}", RocketMqConstant.approvalSupplierUpdTopic, msg, sendResult);
                }
            } else {
                SendResult sendResult = rocketMQTemplate.syncSend(RocketMqConstant.approvalSupplierUpdTopic + ":" + RocketMqConstant.approvalSupplierUpdTopicUnqualifiedTag,
                        msg);
                if (log.isDebugEnabled()) {
                    log.debug("发送消息到MQ, topic: {}, msg: {}, sendResult: {}", RocketMqConstant.approvalSupplierUpdTopic, msg, sendResult);
                }
            }
        }
    }

    private void saveBySync(ErpApprovalSupplier line) {
        ErpApprovalSupplier find = repository.findByOrganizationIdAndItemIdAndVendorId(
                line.getOrganizationId(), line.getItemId(), line.getVendorId());
        // 默认未确认
        line.setConfirmStatus(ErpApprovalSupplierConfirmStatusEnum.UNCONFIRMED.getValue());
        if (find != null) {
            // 更新
            BeanUtils.copyProperties(line, find, "id", "priUom", "fixLotQty", "confirmStatus", "leadTime");
            line = find;
        }
        // 查找 items
        try {
            ResponseEntity<Results<ItemsDTO>> itemsBySourceItemId = bomFeign.findItemsBySourceItemId(new IdDTO().setId(String.valueOf(line.getItemId())));
            log.warn("sys->bom利用itemId（{}）查询返回结果：{}", line.getItemId(), itemsBySourceItemId);
            if (itemsBySourceItemId.getBody() != null && itemsBySourceItemId.getBody().isSuccess()) {
                ItemsDTO itemsDTO = itemsBySourceItemId.getBody().getData();
                if (itemsDTO != null) {
                    line.setPriUom(itemsDTO.getPriUom());
                    line.setCategorySegment5(itemsDTO.getCategorySegment5());
                }
            }
        } catch (Exception e) {
            line.setPriUom("Item查询错误,请手动处理");
            line.setCategorySegment5("Item查询错误,请手动处理");
        }

        // 获取最新的供应商名称
        ErpSupplierDTO erpSupplierDTO = erpSupplierService.getById(line.getVendorId());
        if (erpSupplierDTO != null) {
            line.setVendorName(erpSupplierDTO.getVendorName());
        }

        repository.save(line);
    }

    @Override
    public void updateAllCategorySegment5() {
        long count = repository.count();
        final long LIMIT = 1000;
        for (int i = 0; i < count + LIMIT; i += LIMIT) {
            List<ErpApprovalSupplier> fetch = jpaQueryFactory.selectFrom(qErpApprovalSupplier)
                    .limit(LIMIT)
                    .offset(i).fetch();
            for (ErpApprovalSupplier line : fetch) {
                ResponseEntity<Results<ItemsDTO>> itemsBySourceItemId = bomFeign.findItemsBySourceItemId(new IdDTO().setId(String.valueOf(line.getItemId())));
                if (itemsBySourceItemId.getBody() != null && itemsBySourceItemId.getBody().isSuccess()) {
                    ItemsDTO itemsDTO = itemsBySourceItemId.getBody().getData();
                    if (itemsDTO != null) {
                        line.setCategorySegment5(itemsDTO.getCategorySegment5());
                    }
                }
                repository.save(line);
            }
        }
    }

    @Override
    @Cacheable(cacheNames = "ApprovalSupplierGetBrandsByItemCode",
            key = "#p0", unless = "#result == null")
    public List<String> getBrandsByItemCode(String itemCode) {
        // 限制厂牌
        Map<String, LovLineDTO> constraintLabel = LovUtils.getAllByHeaderCode("constraint_label");
        List<Long> constraintVendorIds = constraintLabel.values().stream().map(i -> Long.parseLong(i.getLovValue()))
                .distinct().collect(Collectors.toList());


        List<String> fetch = jpaQueryFactory.select(qErpSupplier.attribute1)
                .from(qErpSupplier)
                .innerJoin(qErpApprovalSupplier)
                .on(qErpSupplier.vendorId.eq(qErpApprovalSupplier.vendorId).and(qErpApprovalSupplier.isDeleted.eq(0)))
                .where(qErpApprovalSupplier.itemNum.eq(itemCode))
                .where(qErpSupplier.vendorId.notIn(constraintVendorIds))
                // attribute2 = Y 表示是内部供应商
                .where(qErpSupplier.attribute2.ne("Y"))
                //issue 16250 料号匹配特殊单厂牌，bom验证特殊单厂牌，传aps验证厂牌，在处理厂牌批准供应商列表环节，批次供应商列表状态限制
                //只取批准和研发
                .where(qErpApprovalSupplier.aslStatus.in("批准", "研发"))

                .fetch();

        return new ArrayList<>(fetch.stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet()));

    }

    @Override
    public Map<String, Set<String>> getItemCodeAndVendorsSetByItemCodes(List<String> itemCodes) {
        Map<String, Set<String>> result = new HashMap<>();
        List<String> aslStatus = Arrays.asList("批准", "研发");
        List<Tuple> itemCodeAndVendorNameAlt = jpaQueryFactory.select(qErpApprovalSupplier.itemNum, qErpSupplier.vendorNameAlt)
                .from(qErpApprovalSupplier)
                .leftJoin(qErpSupplier).on(qErpApprovalSupplier.vendorId.eq(qErpSupplier.vendorId))
                .where(qErpSupplier.attribute2.isNull().or(qErpSupplier.attribute2.eq("N")))
                .where(qErpApprovalSupplier.aslStatus.in(aslStatus))
                .where(qErpApprovalSupplier.itemNum.in(itemCodes))
                .fetch();

        for (Tuple tuple : itemCodeAndVendorNameAlt) {
            Set<String> vendorNames =
                    result.computeIfAbsent(tuple.get(qErpApprovalSupplier.itemNum), k -> new HashSet<>());
            vendorNames.add(tuple.get(qErpSupplier.vendorNameAlt));
        }

        return result;
    }

    @Override
    public Map<String, Set<Long>> getItemCodeAndVendorIdSetByItemCodes(List<String> itemCodes) {
        Map<String, Set<Long>> result = new HashMap<>();
        List<String> aslStatus = Arrays.asList("批准", "研发");
        List<Tuple> itemCodeAndVendorNameAlt = jpaQueryFactory.select(qErpApprovalSupplier.itemNum, qErpSupplier.vendorId,
                        qErpSupplier.vendorNameAlt)
                .from(qErpApprovalSupplier)
                .leftJoin(qErpSupplier).on(qErpApprovalSupplier.vendorId.eq(qErpSupplier.vendorId))
                .where(qErpSupplier.attribute2.isNull().or(qErpSupplier.attribute2.eq("N")))
                .where(qErpApprovalSupplier.aslStatus.in(aslStatus))
                .where(qErpApprovalSupplier.itemNum.in(itemCodes))
                .fetch();

        for (Tuple tuple : itemCodeAndVendorNameAlt) {
            Set<Long> vendorNames =
                    result.computeIfAbsent(tuple.get(qErpApprovalSupplier.itemNum), k -> new HashSet<>());
            vendorNames.add(tuple.get(qErpSupplier.vendorId));
        }

        return result;
    }

    @Override
    @Cacheable(cacheNames = "ApprovalSupplierGetItemCodesByVendorId",
            key = "#p0", unless = "#result == null")
    public List<String> getItemCodesByVendorId(String vendorId) {
        List<String> fetch = jpaQueryFactory.select(qErpApprovalSupplier.itemNum).from(qErpApprovalSupplier)
                .where(qErpApprovalSupplier.vendorId.eq(Long.parseLong(vendorId)))
                .where(qErpApprovalSupplier.aslStatus.in("批准", "研发")).fetch().stream().distinct().collect(Collectors.toList());
        return fetch;

    }

    @Override
    public Map<String, List<ErpApprovalSupplierDTO>> queryApprovalSupplier(List<String> itemNums) {
        Map<String, List<ErpApprovalSupplierDTO>> result = new HashMap<>();
        for (String itemNum : itemNums) {
            List<ErpApprovalSupplierDTO> collect = erpApprovalSupplierService.queryApprovalSupplierByItemNum(itemNum);
            result.put(itemNum, collect);
        }
        return result;
    }

    @Override
    @CacheEvict(cacheNames = {
            "ApprovalSupplier_queryApprovalSupplier",
            "ApprovalSupplierGetItemCodesByVendorId",
            "ApprovalSupplierGetBrandsByItemCode",
    }, allEntries = true)
    public void updateVendorNameByErpSupplier(List<ErpSupplierDTO> supplierDTO) {
        List<List<ErpSupplierDTO>> parts = Lists.partition(supplierDTO, 100);
        parts.stream().forEach(list -> {
            Map<Long, String> vendorIdAndVendorNameMap = list.stream().collect(Collectors.toMap(i -> i.getVendorId(), i -> i.getVendorName()));
            repository.findAll(qErpApprovalSupplier.vendorId.in(vendorIdAndVendorNameMap.keySet()))
                    .forEach(erpApprovalSupplier -> {
                        Long vendorId = erpApprovalSupplier.getVendorId();
                        String vendorName = vendorIdAndVendorNameMap.get(vendorId);
                        if (Objects.equals(erpApprovalSupplier.getVendorName(), vendorName)) {
                            return;
                        }
                        log.info("更新批准供应商名称, vendorId: {}, oldVendorName {},newVendorName: {}", vendorId, erpApprovalSupplier.getVendorName(), vendorName);
                        erpApprovalSupplier.setVendorName(vendorName);
                        repository.save(erpApprovalSupplier);
                    });

        });
    }

    @Override
    public void updateAllVendorName() {
        List<ErpSupplierDTO> allErpSupplierDTOS = erpSupplierService.getAllErpSupplierDTOS();
        updateVendorNameByErpSupplier(allErpSupplierDTOS);
    }

    @Override
    @Cacheable(cacheNames = "ApprovalSupplier_queryApprovalSupplier",
            key = "#p0", unless = "#result == null")
    public List<ErpApprovalSupplierDTO> queryApprovalSupplierByItemNum(String itemNum) {
        List<Tuple> fetch = jpaQueryFactory.selectDistinct(
                        qErpApprovalSupplier.itemId,
                        qErpApprovalSupplier.itemNum,
                        qErpApprovalSupplier.vendorId,
                        qErpApprovalSupplier.vendorName,
                        qErpSupplier.attribute1,
                        qErpSupplier.attribute2,
                        qErpApprovalSupplier.aslStatus
                )
                .from(qErpApprovalSupplier)
                .leftJoin(qErpSupplier).on(qErpApprovalSupplier.vendorId.eq(qErpSupplier.vendorId))
                .where(qErpSupplier.isDeleted.eq(0))
                .where(qErpApprovalSupplier.itemNum.eq(itemNum))
                .where(qErpApprovalSupplier.isDeleted.eq(0))
                .where(qErpApprovalSupplier.aslStatus.in("批准", "研发"))
                .fetch();
        List<ErpApprovalSupplierDTO> result = new ArrayList<>();
        for (Tuple tuple : fetch) {
            ErpApprovalSupplierDTO dto = new ErpApprovalSupplierDTO();
            dto.setItemId(tuple.get(qErpApprovalSupplier.itemId));
            dto.setItemNum(tuple.get(qErpApprovalSupplier.itemNum));
            dto.setVendorId(tuple.get(qErpApprovalSupplier.vendorId));
            dto.setVendorName(tuple.get(qErpApprovalSupplier.vendorName));
            dto.setAttribute1(tuple.get(qErpSupplier.attribute1));
            dto.setAttribute2(tuple.get(qErpSupplier.attribute2));
            dto.setAslStatus(tuple.get(qErpApprovalSupplier.aslStatus));
            result.add(dto);
        }
        return result;
    }


    private ExternalInterfaceDTO createParams(Map<String, String> data) {
        ExternalInterfaceDTO dto = new ExternalInterfaceDTO();
        dto.setUrl(erpUrl);
        dto.setPath("/la004/v1/approvedSupplier/list");
        dto.setClientId(la002ClientId);
        dto.setSecret(la002Secret);
        dto.setRequestBody(JSON.toJSONString(data));
        return dto;
    }

    @Override
    public List<ErpApprovalSupplier> getByLimit(int skip, int limit) {
        QErpApprovalSupplier qErpApprovalSupplier = QErpApprovalSupplier.erpApprovalSupplier;
        JPAQuery<ErpApprovalSupplier> where = jpaQueryFactory.select(qErpApprovalSupplier)
                .from(qErpApprovalSupplier);

        where.offset(skip);
        where.limit(limit);

        return where.fetch();
    }

    @Override
    public void dailySync(LocalDateTime fromDateTime, LocalDateTime toDateTime) {
        int pageNum = 1;
        Map<String, String> data = new HashMap<>();
        data.put("last_update_date_from", LocalDateTimeUtil.formatNormal(fromDateTime));
        data.put("last_update_date_to", LocalDateTimeUtil.formatNormal(toDateTime));
        data.put("pageSize", String.valueOf(PAGE_SIZE));
        for (; ; ) {
            data.put("pageNum", String.valueOf(pageNum++));
            ExternalInterfaceDTO params = createParams(data);
            String json = erpInterfaceService.postForString(params);
            JSONObject outJson = JSON.parseObject(json);
            JSONArray jsonArray = outJson.getJSONArray("data");
            if (jsonArray == null) {
                return;
            }
            for (int i = 0; i < jsonArray.size(); i++) {
                ErpApprovalSupplier line = jsonArray.getObject(i, ErpApprovalSupplier.class);
                saveBySync(line);
            }
        }
    }

    @Override
    public List<ErpApprovalSupplier> findByUpdatedTimeGreaterThan(LocalDateTime updateTime) {
        return repository.findByUpdatedTimeGreaterThan(updateTime);
    }

    @Override
    @Cacheable(cacheNames = "ApprovalSupplierGetItemCodesByVendorId",
            key = "#query.vendorId +'_'+#query.itemNum", unless = "#result == null")
    public ErpApprovalSupplierDTO findApprovalSupplierByCondition(ErpApprovalSupplierQuery query) {
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qErpApprovalSupplier.vendorId.eq(query.getVendorId()));
        builder.and(qErpApprovalSupplier.itemNum.eq(query.getItemNum()));
        builder.and(qErpApprovalSupplier.aslStatus.eq("批准"));
        return repository.findOne(builder).map(erpApprovalSupplierDEConvert::toDto).orElse(null);
    }
}
