package com.trinasolar.scp.system.service.service.impl;


import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.PathBuilder;
import com.trinasolar.scp.common.api.base.BaseServiceImpl;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.*;
import com.trinasolar.scp.system.domain.convert.LovLineDEConvert;
import com.trinasolar.scp.system.domain.dto.LovHeaderDTO;
import com.trinasolar.scp.system.domain.dto.LovLineDTO;
import com.trinasolar.scp.system.domain.dto.LovLineExtDTO;
import com.trinasolar.scp.system.domain.dto.ShipmentMailImportDTO;
import com.trinasolar.scp.system.domain.entity.AttrConfig;
import com.trinasolar.scp.system.domain.entity.LovHeader;
import com.trinasolar.scp.system.domain.entity.LovLine;
import com.trinasolar.scp.system.domain.entity.LovTl;
import com.trinasolar.scp.system.domain.query.LovLineQuery;
import com.trinasolar.scp.system.domain.save.LovLineSaveDTO;
import com.trinasolar.scp.system.domain.save.PutValueToLovLineSaveDTO;
import com.trinasolar.scp.system.service.enums.Constants;
import com.trinasolar.scp.system.service.enums.LovTypeEnum;
import com.trinasolar.scp.system.service.repository.LovLineRepository;
import com.trinasolar.scp.system.service.repository.LovTlRepository;
import com.trinasolar.scp.system.service.service.LovCacheService;
import com.trinasolar.scp.system.service.service.LovHeaderService;
import com.trinasolar.scp.system.service.service.LovLineService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * lov头表(LovLine)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-24 10:42:26
 */
@Service("lovLineService")
@Slf4j
public class LovLineServiceImpl extends BaseServiceImpl<LovLine, Long> implements LovLineService {
    public static final String ATTR = "ATTR";

    @Autowired
    LovLineDEConvert lovLineDEConvert;

    StringRedisTemplate stringRedisTemplate;

    LovHeaderService lovHeaderService; //防止服务交叉引用,不直接注入

    LovLineRepository lovLineRepository;

    @Autowired
    RedissonClient redissonClient;

    @Autowired
    LovCacheService lovCacheService;

    @Autowired
    LovTlRepository lovTlRepository;

    public static String genLovSqlStatement(LovLineQuery lovLineQuery, String headerCode, String currentLang, LovHeader lovHeader) {
        StringBuilder statement = new StringBuilder();

        //判断是否为动态脚本
        //动态脚本中不存在lov_header_id,需要加上as 2022-09-08
        if (lovHeader != null && LovTypeEnum.DYNAMIC_SCRIPT.getValue().equals(lovHeader.getLovTypeId())) {
            String lovScript = lovHeader.getLovScript();
            if (lovScript != null && lovScript.contains("{LANG}")) {
                lovScript = lovScript.replace("{LANG}", "'" + currentLang + "'");
            }
            statement.append("SELECT t.*,'").append(headerCode).append("' as lov_header_code, ").append(lovHeader.getLovHeaderId()).append(" as lov_header_id").append(" FROM (");
            statement.append(lovScript);
            statement.append(") AS t WHERE 1=1");

        } else {
            //否则为结果集
            //由于LOV与属性组使用存在困扰，此处把二者合并处理，以相同方式使用
            //属性值使用header_code查询,不存在于lov_header表示为属性组
            if (ATTR.equals(headerCode) || (!headerCode.isEmpty() && lovHeader == null)) {
                statement.append("SELECT t.* FROM sys_lov_attr_lines_vl t WHERE t.is_deleted=0 and t.enable_flag='Y' ");
                if (!ATTR.equals(headerCode)) {
                    statement.append(" AND " + " lov_header_code ='").append(headerCode).append("' ");
                }
            } else {
                statement.append("SELECT t.*,h.lov_code as lov_header_code FROM sys_lov_lines t " + " join sys_lov_header h on h.lov_header_id = t.lov_header_id WHERE t.is_deleted=0 and t.enable_flag='Y' ");
                if (lovHeader != null) {
                    statement.append(" AND t.lov_header_id=");
                    statement.append(lovHeader.getLovHeaderId().toString());
                }
            }
        }

        if (StringUtils.isNotEmpty(lovLineQuery.getLovValue())) {
            statement.append(" AND t.lov_value = '").append(lovLineQuery.getLovValue()).append("' ");
        }
        if (lovLineQuery.getLovLineId() != null) {
            statement.append(" AND t.lov_line_id = ").append(lovLineQuery.getLovLineId()).append(" ");
        }
        if (StringUtils.isNotEmpty(lovLineQuery.getName())) {
            statement.append(" AND t.lov_name = '").append(lovLineQuery.getName()).append("'");
        }

        if (StringUtils.isNotEmpty(lovLineQuery.getWhere())) {
            statement.append(" AND ").append(lovLineQuery.getWhere());
        }

        //弹性栏位及其它条件
        if (StringUtils.isNotEmpty(lovLineQuery.getAttributes())) {
            //  'Y,2'
            String[] split = lovLineQuery.getAttributes().split(";");
            for (int i = 0; i < split.length; i++) {
                String s = split[i];
                if (StringUtils.isNotEmpty(s)) {
                    statement.append(String.format(" AND t.attribute%d='%s' ", i + 1, s));
                }
            }
        }

        //去除付款条件在LOV里有效标识必须为是的查询结果
        if (!StringUtils.isBlank(lovLineQuery.getEnableFlag()) && Constants.ENABLE_ALL_FLAG.equals(lovLineQuery.getEnableFlag().toUpperCase())) {
            return statement.toString().replace(" and t.enable_flag='Y'", "");
        }

        return statement.toString();
    }

    @PostConstruct
    private void init() {
        stringRedisTemplate = SpringContextUtils.getBean(StringRedisTemplate.class);
        lovHeaderService = SpringContextUtils.getBean(LovHeaderService.class);
        lovLineRepository = SpringContextUtils.getBean(LovLineRepository.class);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param lovLineId 主键
     * @return 实例对象
     */

    @Override
    public LovLineDTO queryById(Long lovLineId) {
        LovLineQuery lovLineQuery = new LovLineQuery();
        lovLineQuery.setLovLineId(lovLineId);
        List<LovLineDTO> lovLineDTOS = queryByLovQuery(lovLineQuery);
        if (lovLineDTOS != null && lovLineDTOS.size() == 1) {
            return lovLineDTOS.get(0);
        }
        return null;
    }

    /**
     * 分页查询
     *
     * @param lovLineQuery 筛选条件
     * @return 查询结果
     */
    @Override
    public Page<LovLineExtDTO> queryByPage(LovLineQuery lovLineQuery) {
        Sort sort = Sort.by(Sort.Direction.ASC, "colNo");
        Pageable pageable = PageRequest.of(lovLineQuery.getPageNumber() - 1, lovLineQuery.getPageSize(), sort);
        if (lovLineQuery == null || StringUtils.isEmpty(lovLineQuery.getCode())) {
            List<LovLineExtDTO> list = new ArrayList<>();
            return new PageImpl<>(list, pageable, 0);
        }
        LovHeader lovHeader = getLovHeader(lovLineQuery.getCode());
        if (lovHeader == null) {
            List<LovLineExtDTO> list = new ArrayList<>();
            return new PageImpl<>(list, pageable, 0);
        }

        //2022-08-10 增加动态SQL分页结果返回
        if (lovHeader.getLovTypeId() != null && Objects.equals(LovTypeEnum.DYNAMIC_SCRIPT.getValue(), lovHeader.getLovTypeId())) {
            String sql = genLovSqlStatement(lovLineQuery, lovHeader.getLovCode(), getCurrentLang(), lovHeader);
            Page<LovLineExtDTO> resultPage = JpaUtils.getResultPage(getEntityManager(), LovLineExtDTO.class, sql, pageable);
            if (resultPage == null) {
                List<LovLineExtDTO> list = new ArrayList<>();
                return new PageImpl<>(list, pageable, 0);
            }
            return resultPage;
        }
        //end 2022-08-10

        BooleanBuilder booleanBuilder = new BooleanBuilder();
        PathBuilder<?> qLovLine = this.getEntityPath(LovLine.class);
        booleanBuilder.and(qLovLine.get("lovHeaderId").eq(lovHeader.getLovHeaderId()));

        if (StringUtils.isNotEmpty(lovLineQuery.getName())) {
            booleanBuilder.and(qLovLine.getString("lovName").like("%" + lovLineQuery.getName() + "%"));
        }

        if (StringUtils.isNotEmpty(lovLineQuery.getLovValue())) {
            booleanBuilder.and(qLovLine.getString("lovValue").like("%" + lovLineQuery.getLovValue() + "%"));
        }

        //动态栏位名称为条件,不直接使用QLovLine对象
        if (StringUtils.isNotEmpty(lovLineQuery.getAttributes())) {
            //'Y,2'
            String[] split = lovLineQuery.getAttributes().split(";");
            for (int i = 0; i < split.length; i++) {
                String value = split[i];
                if (StringUtils.isNotEmpty(value)) {
                    booleanBuilder.and(qLovLine.getString("attribute" + (i + 1)).like("%" + value + "%"));
                }
            }
        }
        Page<LovLine> result = this.findAll(booleanBuilder, pageable);

        Long lovHeaderId = lovHeader.getLovHeaderId();

        //弹性栏位如果为LOV,需要翻译
        List<AttrConfig> lovConfigs = this.findAll(AttrConfig.class, "attrHeaderId", lovHeaderId, "sence", "sys_lov_lines");

        List<LovLineExtDTO> lovLineExtDTOList = result.getContent().stream().map(lovLine -> {
            LovLineExtDTO lovLineExtDTO = new LovLineExtDTO();
            BeanUtils.copyProperties(lovLine, lovLineExtDTO);

            for (AttrConfig lovConfig : lovConfigs) {
                if (!StringUtils.isEmpty(lovConfig.getAttrLovName()) && StringUtils.isNotEmpty(lovConfig.getAttrColName())) {
                    try {
                        Object value = BeanUtils.getFieldValue(lovLineExtDTO, lovConfig.getAttrColName());
                        if (value != null && StringUtils.isNotEmpty(String.valueOf(value))) {
                            String lovName = null;
                            try {
                                if (StringUtils.isNumeric(String.valueOf(value))) {
                                    lovName = LovUtils.getName(Long.valueOf(value.toString()));
                                }
                                //lovName没有转出来，并且又包含逗号说明是多选的下拉框
                                if (lovName == null && String.valueOf(value).indexOf(",") > -1) {
                                    List<String> arrayList = Lists.newArrayList(String.valueOf(value).split(","));
                                    lovName = arrayList.stream().map(item -> {
                                        if (StringUtils.isNumeric(item)) {
                                            return LovUtils.getName(Long.valueOf(item));
                                        } else {
                                            return LovUtils.getName(lovConfig.getAttrLovName(), item);
                                        }
                                    }).collect(Collectors.joining(","));
                                }

                                if (lovName == null) {
                                    lovName = LovUtils.getName(lovConfig.getAttrLovName(), value.toString());
                                }

                            } catch (Exception e) {
                                lovName = value.toString();
                            }
                            if (lovName != null) {
                                BeanUtils.setFieldValue(lovLineExtDTO, lovConfig.getAttrColName() + "Name", lovName);
                            }
                        }
                    } catch (NullPointerException exception) {
                        exception.printStackTrace();
                    }
                }
            }
            return lovLineExtDTO;
        }).collect(Collectors.toList());
        return new PageImpl<>(lovLineExtDTOList, result.getPageable(), result.getTotalElements());
    }

    /**
     * 通过主键删除数据
     *
     * @param lovLineId 主键
     */
    @Override
    public void deleteById(Long lovLineId) {
        LovLineDTO lovLineDTO = queryById(lovLineId);
        if (lovLineDTO != null) {
            super.deleteById(lovLineId);
            //清除redisCache
            clearLovLinesFromRedis(Collections.singletonList(lovLineDTO));
        }
    }

    @Override
    public List<LovLine> listByLovHeaderId(Long lovHeaderId) {
        return this.findAll("lovHeaderId", lovHeaderId);
    }

    /**
     * 单条件查询,优先尝试从redis获取
     *
     * @param lovLineQuery 查询条件
     * @return 结果列表
     */

    @Override
    public List<LovLineDTO> queryByLovQuery(LovLineQuery lovLineQuery) {
        String headerCode = lovLineQuery.getCode();
        String lovValue = lovLineQuery.getLovValue();
        Long lovId = lovLineQuery.getLovLineId();
       /*  if(SqlInjectionUtils.check(headerCode) || SqlInjectionUtils.check(lovLineQuery.getWhere())){
            return null;
        } */
        //2022-09-09 增加权限过滤 eg: privilege=AREA
        if (lovLineQuery.getWhere() != null && lovLineQuery.getWhere().startsWith("privilege=")) {
            String[] parts = lovLineQuery.getWhere().split("=");
            String type = "Y".equalsIgnoreCase(parts[1]) ? headerCode : parts[1];
            lovLineQuery.setWhere(DataPrivilegeUtils.getPrivilegeFilter(type, "lov_line_id"));
        }
        //2022-09-09 END d
        //1.传ID或名称时优先从redis缓存获取
        if (lovId != null || !StringUtils.isEmpty(lovValue)) {
            LovLineDTO lovLineDTO = getFromRedis(headerCode, lovValue, lovId);
            if (lovLineDTO != null) {
                return Collections.singletonList(lovLineDTO);
            }
        }

        //2.如果只传headerCode时, 查全部明细并缓存,同时设置"整体"缓存标记
        if (StringUtils.isNotBlank(headerCode) &&
                StringUtils.isEmpty(lovLineQuery.getWhere()) &&
                StringUtils.isEmpty(lovLineQuery.getName()) &&
                StringUtils.isEmpty(lovLineQuery.getLovValue()) &&
                StringUtils.isEmpty(lovLineQuery.getAttributes()) &&
                lovLineQuery.getLovLineId() == null) {
            // 加分布式锁

            String lovHeaderUpdKey = lovCacheService.getLovHeaderUpdKey(headerCode);
            RLock lock = redissonClient.getLock(lovHeaderUpdKey);
            lock.lock(30, TimeUnit.SECONDS);
            try {
                boolean withTranslateKey = true;
                List<LovLineDTO> doubleCheckHeaderCodeLovs = lovCacheService.getByHeaderCodeFromRedis(headerCode, withTranslateKey);
                if (doubleCheckHeaderCodeLovs != null) return doubleCheckHeaderCodeLovs;

                //未缓存时从DB读取
                List<LovLineDTO> lovLineDTOS = queryFromDBWithTranslate(lovLineQuery);
                putLovLinesIntoRedis(lovLineDTOS);
                lovCacheService.putLovHeaderCache(headerCode, lovLineDTOS, withTranslateKey);
                return lovLineDTOS;
            } catch (Exception e) {
                lovCacheService.clearLovHeaderCache(headerCode);
                log.error("查询 LOV HEADER CODE 失败, headerCode: {}, lovLineQuery: {}", headerCode, lovLineQuery, e);
                throw e;
            } finally {
                lock.unlock();
            }
        }
        //2 END

        //3.以上未处理, 继续执行查询并缓存
        List<LovLineDTO> lovLineDTOS = queryFromDBWithTranslate(lovLineQuery);
        putLovLinesIntoRedis(lovLineDTOS);
        return lovLineDTOS;
    }

    private Map<String, LovLineDTO> getAllNameByHeaderCodeFromRedis(String headerCode) {
        String redisWholeKeyFlag = LovUtils.getWholeNameLovCacheKey(headerCode);
        HashMap<String, LovLineDTO> result = null;
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisWholeKeyFlag))) {
            Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(redisWholeKeyFlag);
            result = new HashMap<>();
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                result.put((String) entry.getKey(), JSON.parseObject((String) entry.getValue(), LovLineDTO.class));
            }
        }
        return result;
    }

    /**
     * 多条件查询,经过预处理以提升速度
     *
     * @param lovLineQueries 条件列表
     * @return list
     */
    @Override
    public List<LovLineDTO> queryByLovQuery(List<LovLineQuery> lovLineQueries) {
        List<LovLineQuery> newQueries = new ArrayList<>();
        List<String> lovLineIds = new ArrayList<>();
        //0.预处理,只传ID时单独处理
        lovLineQueries.forEach(lovLineQuery -> {
            if (StringUtils.isEmpty(lovLineQuery.getCode()) && lovLineQuery.getLovLineId() != null) {
                lovLineIds.add(lovLineQuery.getLovLineId().toString());
            } else {
                newQueries.add(lovLineQuery);
            }
        });

        //1.首先从缓存获取
        List<LovLineDTO> lovLineDTOS = getFromRedis(lovLineIds);

        //2.未缓存数据,收集ID批量查询
        lovLineDTOS.forEach(lovLineDTO -> lovLineIds.remove(lovLineDTO.getLovLineId().toString()));
        if (!lovLineIds.isEmpty()) {
            LovLineQuery lovLineQuery = new LovLineQuery();
            lovLineQuery.setWhere(" t.lov_line_id in (" + StringUtils.join(lovLineIds, ",") + ")");
            List<LovLineDTO> listByIds = queryFromDBWithTranslate(lovLineQuery);
            if (!listByIds.isEmpty()) {
                putLovLinesIntoRedis(listByIds);
                lovLineDTOS.addAll(listByIds);
            }
        }

        //3.继续查询未处理项
        for (LovLineQuery lovLineQuery : newQueries) {
            List<LovLineDTO> lovLines = queryByLovQuery(lovLineQuery);
            if (lovLines != null && !lovLines.isEmpty()) {
                lovLineDTOS.addAll(lovLines);
            }
        }
        return lovLineDTOS;
    }

    @Override
    public LovLineDTO save(LovLineSaveDTO lovLineSaveDTO) {
        LovLine lovLine = LovLineDEConvert.INSTANCE.toEntity(lovLineSaveDTO);

        //重复性检查
        if (this.exists(lovLine, "lovHeaderId", "lovValue") || this.exists(lovLine, "lovHeaderId", "lovName")) {
            throw new BizException(String.format("数据重复%s，%s!", lovLine.getLovValue(), lovLine.getLovName()));
        }
        LovHeaderDTO lovHeaderDTO = lovHeaderService.queryById(lovLine.getLovHeaderId());
        if (lovHeaderDTO.getLovName().equals("发货通知接收邮箱")) {
            Map<String, com.trinasolar.scp.common.api.base.LovLineDTO> tmWorkGroup = LovUtils.getAllByHeaderCode("TMWorkGroup");
            Map<String, com.trinasolar.scp.common.api.base.LovLineDTO> region = LovUtils.getAllByHeaderCode("Region");
//            Map<String, com.trinasolar.scp.common.api.base.LovLineDTO> inventoryOrganization = LovUtils.getAllByHeaderCode("inventory_organization");
            String useSpli = ",";
            if (StringUtils.isNotEmpty(lovLine.getAttribute1())) {
                String[] split = lovLine.getAttribute1().split(useSpli);
                lovLine.setAttribute1(null);
                StringBuilder resultBuilder = new StringBuilder();
                for (String item : split) {
                    String lovValue = region.get(item).getLovValue();
                    resultBuilder.append(lovValue).append(",");
                }
                String result = resultBuilder.toString();
                if (result.length() > 0) {
                    result = result.substring(0, result.length() - 1);
                    lovLine.setAttribute1(result);
                }
            }
            //账套手动输入，不考虑lov翻译了
//            if (StringUtils.isNotEmpty(lovLine.getAttribute2())){
//                String[] split = lovLine.getAttribute2().split(useSpli);
//                lovLine.setAttribute2(null);
//                StringBuilder resultBuilder = new StringBuilder();
//                for (String item : split){
//                    String lovName = inventoryOrganization.get(item).getLovName();
//                    resultBuilder.append(item).append(",");
//                }
//                String result = resultBuilder.toString();
//                if (result.length() > 0) {
//                    result = result.substring(0, result.length() - 1);
//                    lovLine.setAttribute2(result);
//                }
//            }
            if (StringUtils.isNotEmpty(lovLine.getAttribute3())) {
                String[] split = lovLine.getAttribute3().split(useSpli);
                lovLine.setAttribute3(null);
                StringBuilder resultBuilder = new StringBuilder();
                for (String item : split) {
                    String lovName = tmWorkGroup.get(item).getLovName();
                    resultBuilder.append(lovName).append(",");
                }
                String result = resultBuilder.toString();
                if (result.length() > 0) {
                    result = result.substring(0, result.length() - 1);
                    lovLine.setAttribute3(result);
                }
            }
        }
        this.save(lovLine);

        LovLineDTO lovLineDTO = this.toDto(lovLine);
        putLovLinesIntoRedis(Collections.singletonList(lovLineDTO));
        // 删除头相关的缓存
        lovCacheService.clearLovHeaderCache(lovHeaderDTO.getLovCode());

        return lovLineDTO;
    }

    /**
     * 通过 LovLineQuery  查询LovLine信息
     *
     * @param lovLineQuery 查询条件
     * @return 返回结果列表
     */
    private List<LovLineDTO> queryFromDBWithTranslate(LovLineQuery lovLineQuery) {
        List<LovLineDTO> lovLines = queryFromDB(lovLineQuery);

        // 翻译
        String currentLang = getCurrentLang();
        translate(currentLang, lovLines);

        return lovLines;
    }

    /**
     * 付款条件定时任务同步事全量所以需要查询有效和失效的进行处理，避免出现失效查不出来重新新建一条失效数据
     * 通过 LovLineQuery  查询LovLine信息
     *
     * @param lovLineQuery 查询条件
     * @return 返回结果列表
     */
    @Override
    public List<LovLineDTO> queryByCodeNew(LovLineQuery lovLineQuery) {
        List<LovLineDTO> lovLineDTOS = queryFromDB(lovLineQuery);
        lovLineDTOS.forEach(i -> i.setLovHeaderCode(lovLineQuery.getCode()));
        return lovLineDTOS;
    }

    private List<LovLineDTO> queryFromDB(LovLineQuery lovLineQuery) {
        String headerCode = lovLineQuery.getCode() == null ? "" : lovLineQuery.getCode();
        //Sort sort = Sort.by(Sort.Direction.ASC, "colNo");
        //Pageable pageable = PageRequest.of(lovLineQuery.getPageNumber() - 1, lovLineQuery.getPageSize(), sort);

        // 获取当前语言
        LovHeader lovHeader = getLovHeader(headerCode);

        String currentLang = getCurrentLang();
        // 获取本次执行的SQL语句
        String statement = genLovSqlStatement(lovLineQuery, headerCode, currentLang, lovHeader);

        // 执行语句将结果存入LovLines
        PageRequest pageRequest = PageRequest.of(0, 3000);
        List<LovLineDTO> lovLines = JpaUtils.getResultList(getEntityManager(), LovLineDTO.class, statement, pageRequest);
        return lovLines;
    }

    private void translate(String currentLang, List<LovLineDTO> lovLines) {
        //基础语言为中文，不需要翻译
        //目前直接使用SQL获取了语言，后续仍可以单独获取
        //建议直接缓存到LovLineDTO,以空间换时间，可以把不必要的栏位设置成null,减少JSON大小
        //currentLang = "en_US";

        if (!"zh_CN".equals(currentLang)) {
            List<Long> langIds = lovLines.stream().map(LovLineDTO::getLovLineId)
                    .filter(l -> l != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(langIds)) {
                return;
            }
            //后续如果id数过多，可以改为分批查询
            List<LovTl> lovTls = this.findAll(LovTl.class, "lang", currentLang, "pkId", langIds);
//            Map<Long, String> trans = lovTls.stream().collect(Collectors.toMap(LovTl::getPkId, LovTl::getName));
            Map<Long, List<LovTl>> trans = lovTls.stream().collect(Collectors.groupingBy(LovTl::getPkId));
            lovLines.forEach(line -> {
                if (trans.containsKey(line.getLovLineId())) {
                    List<LovTl> needTrans = trans.get(line.getLovLineId());
                    for (LovTl tl : needTrans) {
                        ReflectUtil.setFieldValue(line, tl.getLovField(), tl.getName());
                    }
//                    line.setLovName(trans.get(line.getLovLineId()));
                }
            });
        }
    }

    /**
     * 获取当前语言 默认为中文
     *
     * @return 语言代码
     */
    private String getCurrentLang() {
        String currentLang = MyThreadLocal.get().getLang();
        if (StringUtils.isEmpty(currentLang)) {
            currentLang = "zh_CN";
        }
        return currentLang;
    }

    @Override
    public List<LovLineDTO> queryByIds(IdsDTO idsQuery) {
        if (idsQuery == null || idsQuery.getIds() == null || idsQuery.getIds().isEmpty()) {
            return new ArrayList<>();
        }

        List<LovLineQuery> lovLineQueries = new ArrayList<>();
        idsQuery.getIds().forEach(idDTO -> {
            LovLineQuery lovLineQuery = new LovLineQuery();
            lovLineQuery.setLovLineId(Long.parseLong(idDTO.getId()));
            lovLineQueries.add(lovLineQuery);
        });

        return queryByLovQuery(lovLineQueries);
    }

    /**
     * 获取Redis保存Key值,保持一致
     *
     * @param type lov header code
     * @return String key
     */
    public String getRedisIdKey(String type) {
        return LovUtils.getRedisIdKey(type);
    }

    /**
     * 一次从redis获取多个id
     *
     * @param ids id列表
     * @return 结果信
     */
    private List<LovLineDTO> getFromRedis(List<String> ids) {
        List<LovLineDTO> lovLineDTOS = new ArrayList<>();
        //从缓存获取
        List<Object> jsonList = stringRedisTemplate.opsForHash().multiGet(getRedisIdKey(""), Arrays.asList(ids.toArray()));

        for (Object json : jsonList) {
            if (json != null && !json.toString().isEmpty()) {
                LovLineDTO lovLineDTO = JSON.parseObject(json.toString(), LovLineDTO.class);
                lovLineDTOS.add(lovLineDTO);
            }
        }
        return lovLineDTOS;
    }

    /**
     *
     */
    private LovLineDTO getFromRedis(String headerCode, String value, Long lovId) {
        String json = null;
        if (lovId != null) {
            json = (String) stringRedisTemplate.opsForHash().get(getRedisIdKey(headerCode), lovId.toString());
        }
        //传headerCode + value
        else if (StringUtils.isNotEmpty(headerCode) && StringUtils.isNotEmpty(value)) {
            json = (String) stringRedisTemplate.opsForHash().get(LovUtils.getRedisValueKey(headerCode), value);
        }
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JSON.parseObject(json, LovLineDTO.class);
    }

    @Override
    public List<LovLineDTO> listDtoByDate(String code, String lastUpdatedTime) {
        LovHeader lovHeader = getLovHeader(code);
        if (lovHeader == null) {
            return null;
        }

        LovLineQuery lovLineQuery = new LovLineQuery();
        lovLineQuery.setCode(code);
        lovLineQuery.setWhere("t.updated_time > '" + lastUpdatedTime + "'");

        return queryByLovQuery(lovLineQuery);
    }

    public List<LovLineDTO> saveAll(List<LovLineDTO> dtoList) {
        log.info(String.format("解析到lov数据:{%s}", dtoList));

        //批量检查
        for (LovLineDTO lovLineDTO : dtoList) {
            if (lovLineDTO.getLovHeaderId() == null) {
                throw new BizException("headId存在空数据");
            }
        }

        //检查通过保存
        for (LovLineDTO lovLineDTO : dtoList) {
            LovLine lovLine = LovLineDEConvert.INSTANCE.toEntity(lovLineDTO);
            if (lovLine.getEnableFlag() == null) {
                lovLine.setEnableFlag("Y");
            }

//            this.save(lovLine);
            lovLineRepository.save(lovLine);
            BeanUtils.copyProperties(lovLine, lovLineDTO);
        }
        putLovLinesIntoRedis(dtoList);

        //清除缓存 按头清除
        dtoList.stream().map(LovLineDTO::getLovHeaderId).distinct()
                .forEach(i -> {
                    LovHeaderDTO lovHeaderDTO = lovHeaderService.queryById(i);
                    if (lovHeaderDTO != null) {
                        lovCacheService.clearLovHeaderCache(lovHeaderDTO.getLovCode());
                    }
                });
        return dtoList;
    }


    @Override
    public List<LovLineDTO> importExcel(List<LovLineDTO> lovLineDTOS, String lovCode) {
        if (lovLineDTOS == null || lovLineDTOS.isEmpty()) {
            return null;
        }

        LovHeader lovHeader = getLovHeader(lovCode);
        if (lovHeader == null) {
            return null;
        }
        Long lovHeaderId = lovHeader.getLovHeaderId();

        List<AttrConfig> lovConfigs = this.findAll(AttrConfig.class, "attrHeaderId", lovHeaderId, "sence", "sys_lov_lines");
        for (LovLineDTO lovLineDTO : lovLineDTOS) {
            lovLineDTO.setLovHeaderId(lovHeader.getLovHeaderId());
            lovLineDTO.setLovHeaderCode(lovHeader.getLovCode());
            LovLine lovLine = this.findOne("lovHeaderId", lovHeaderId, "lovValue", lovLineDTO.getLovValue());
            //如果存在,取回原id
            if (lovLine != null) {
                lovLineDTO.setLovLineId(lovLine.getLovLineId());
            }

            //临时,需要改成取Lov
            if ("是".equals(lovLineDTO.getEnableFlag()) || "Y".equals(lovLineDTO.getEnableFlag())) {
                lovLineDTO.setEnableFlag("Y");
            }

            //弹性栏位处理
            for (AttrConfig lovConfigDTO : lovConfigs) {
                String lovHeaderCode = lovConfigDTO.getAttrLovName();
                //有配置LOV时需要转换
                if (!StringUtils.isEmpty(lovHeaderCode)) {
                    Object value = BeanUtils.getFieldValue(lovLineDTO, lovConfigDTO.getAttrColName());
                    if (value != null) {
                        com.trinasolar.scp.common.api.base.LovLineDTO lovObj = LovUtils.get(lovHeaderCode, value.toString());
                        if (lovObj != null) {
                            BeanUtils.setFieldValue(lovLineDTO, lovConfigDTO.getAttrColName(), lovObj.getLovLineId());
                        }
                    }
                }
            }
        }

        //保存
        return this.saveAll(lovLineDTOS);
    }

    @Override
    public List<LovLineDTO> getByLineIds(List<LovLineQuery> lovLineQuerys) {
        return LovLineDEConvert.INSTANCE.toDto(lovLineRepository.findAllById(lovLineQuerys.stream().map(LovLineQuery::getLovLineId).filter(Objects::nonNull).distinct().collect(Collectors.toList())));
    }

    @Override
    public void evict(Long lovLineId) {
        LovLineDTO lovLineDTO = queryById(lovLineId);
        if (lovLineDTO != null) {
            //清除redisCache
            clearLovLinesFromRedis(Collections.singletonList(lovLineDTO));
        }
    }

    @Override
    public void putValueToLov(PutValueToLovLineSaveDTO saveDTO) {
        LovHeader lovHeader = lovHeaderService.queryByCode(saveDTO.getLovHeaderCode());
        if (lovHeader == null) {
            throw new BizException("查找不到正确的LovCode");
        }
        List<LovLine> lovLines = listByLovHeaderId(lovHeader.getLovHeaderId());
        // 判断value是否在里面
        List<LovLine> collect = lovLines.stream().filter(item -> Objects.equals(item.getLovValue(), saveDTO.getLovValue())).collect(Collectors.toList());
        if (collect.isEmpty()) {
            //  保存
            LovLineSaveDTO lovLineSaveDTO = new LovLineSaveDTO();
            lovLineSaveDTO.setLovHeaderId(lovHeader.getLovHeaderId());
            lovLineSaveDTO.setLovValue(saveDTO.getLovValue());
            lovLineSaveDTO.setLovName(saveDTO.getLovValue());
            lovLineSaveDTO.setColNo(10);
            lovLineSaveDTO.setEnableFlag("Y");
            lovLineSaveDTO.setEffectiveStartDate(LocalDateTimeUtil.parse("1900-01-01T00:00:00").toLocalDate());
            lovLineSaveDTO.setEffectiveEndDate(LocalDateTimeUtil.parse("4712-12-31T00:00:00").toLocalDate());
            save(lovLineSaveDTO);
        }
    }

    @Override
    public List<LovLineDTO> queryAllByHeaderCode(String headerCode) {
        // 增加缓存
        String lovHeaderUpdKey = lovCacheService.getLovHeaderUpdKey(headerCode);
        RLock lock = redissonClient.getLock(lovHeaderUpdKey);
        lock.lock(30, TimeUnit.SECONDS);
        try {
            boolean withTranslateKey = false;
            List<LovLineDTO> doubleCheckHeaderCodeLovs = lovCacheService.getByHeaderCodeFromRedis(headerCode, withTranslateKey);
            if (doubleCheckHeaderCodeLovs != null) return doubleCheckHeaderCodeLovs;

            //未缓存时从DB读取
            LovLineQuery lovLineQuery = new LovLineQuery();
            lovLineQuery.setCode(headerCode);
            List<LovLineDTO> lovLineDTOS = queryFromDB(lovLineQuery);
            lovLineDTOS.forEach(i -> i.setLovHeaderCode(headerCode));

            lovCacheService.putLovHeaderCache(headerCode, lovLineDTOS, withTranslateKey);
            return lovLineDTOS;
        } catch (Exception e) {
            lovCacheService.clearLovHeaderCache(headerCode);
            throw e;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void importShipmentEmailExcel(List<ShipmentMailImportDTO> areaList, List<ShipmentMailImportDTO> tmWorkGroupList, List<ShipmentMailImportDTO> orgList, String lovCode) {
        LovHeader lovHeader = getLovHeader(lovCode);
        if (lovHeader == null) {
            return;
        }

        List<LovLine> result = new ArrayList<>();
        Long lovHeaderId = lovHeader.getLovHeaderId();
        //数据库已有数据
        List<LovLine> data = lovLineRepository.findByLovHeaderId(lovHeaderId);
        int col_no = data.size() * 10;
        Map<String, List<LovLine>> lovMap = data.stream().collect(Collectors.groupingBy(LovLine::getLovName));

        List<ShipmentMailImportDTO> allList = new ArrayList<>();
        allList.addAll(areaList);
        allList.addAll(tmWorkGroupList);
        allList.addAll(orgList);

        Map<String, List<ShipmentMailImportDTO>> importMap = allList.stream().collect(Collectors.groupingBy(ShipmentMailImportDTO::getUserName));
        for (String userName : importMap.keySet()) {
            List<ShipmentMailImportDTO> shipmentMailImportDTOS = importMap.get(userName);
            LovLine lovLine = new LovLine();
            //headerId
            lovLine.setLovHeaderId(lovHeaderId);
            //生效时间
            lovLine.setEffectiveStartDate(LocalDate.of(1970, 1, 1));
            //col_no
            col_no = col_no + 10;
            lovLine.setColNo(col_no);
            //有效标识
            lovLine.setEnableFlag("Y");

            ShipmentMailImportDTO temp = shipmentMailImportDTOS.get(0);
            //lov_name
            lovLine.setLovName(temp.getUserName());
            //lov_value
            lovLine.setLovValue(temp.getEmail());
            if (lovMap.containsKey(userName)) {
                lovLine.setLovLineId(lovMap.get(userName).get(0).getLovLineId());
                lovLine.setColNo(lovMap.get(userName).get(0).getColNo());
                col_no = col_no - 10;
            }

            for (ShipmentMailImportDTO obj : shipmentMailImportDTOS) {
                if (StringUtils.isNotEmpty(obj.getArea())) {
                    //区域
                    lovLine.setAttribute1(obj.getArea());
                    continue;
                }
                if (StringUtils.isNotEmpty(obj.getOrgCode())) {
                    //账套
                    lovLine.setAttribute2(obj.getOrgCode());
                    continue;
                }
                if (StringUtils.isNotEmpty(obj.getTmWorkGroup())) {
                    //账套
                    lovLine.setAttribute3(obj.getTmWorkGroup());
                }
            }
            result.add(lovLine);
        }
        lovLineRepository.saveAll(result);
    }

    @Override
    public LovLineDTO getByHeaderCodesAndName(LovLineQuery lovLineQuery) {
        Map<String, LovLineDTO> nameAndLovLineDTOMap = getLocalizedLovLinesMap(lovLineQuery.getCode());
        LovLineDTO lovLineDTO = nameAndLovLineDTOMap.get(lovLineQuery.getName());
        return lovLineDTO;
    }

    private Map<String, LovLineDTO> getLocalizedLovLinesMap(String headerCode) {
        // 通过lovHeaderCode 获取lov的数据,再获取相关的所有翻译,作为一个map
        Map<String, LovLineDTO> allNameByHeaderCodeFromRedis = getAllNameByHeaderCodeFromRedis(headerCode);
        if (MapUtils.isEmpty(allNameByHeaderCodeFromRedis)) {
            String lovHeaderUpdKey = lovCacheService.getLovHeaderNameUpdKey(headerCode);
            RLock lock = redissonClient.getLock(lovHeaderUpdKey);
            lock.lock(30, TimeUnit.SECONDS);
            try {
                allNameByHeaderCodeFromRedis = getAllNameByHeaderCodeFromRedis(headerCode);
                if (MapUtils.isNotEmpty(allNameByHeaderCodeFromRedis)) {
                    return allNameByHeaderCodeFromRedis;
                }

                //未缓存时从DB读取
                Map<String, LovLineDTO> queryLocalizedLovLinesFromDB = queryLocalizedLovLinesFromDB(headerCode);
                lovCacheService.putNameToRedis(headerCode, queryLocalizedLovLinesFromDB);
                return queryLocalizedLovLinesFromDB;
            } catch (Exception e) {
                lovCacheService.clearLovHeaderCache(headerCode);
                throw e;
            } finally {
                lock.unlock();
            }
        }

        return allNameByHeaderCodeFromRedis;
    }

    private Map<String, LovLineDTO> queryLocalizedLovLinesFromDB(String headerCode) {
        List<LovLineDTO> lovLineDTOS = queryAllByHeaderCode(headerCode);
        // 得到lov的所有的id
        Map<Long, LovLineDTO> lovIdAndLovMap = lovLineDTOS.stream()
                .distinct().collect(Collectors.toMap(LovLineDTO::getLovLineId,
                        i -> i, (k1, k2) -> k1));
        // 通过id查询所有的翻译
        List<LovTl> line = lovTlRepository.findAllByPkTypeAndPkIdIn("line", new ArrayList<>(lovIdAndLovMap.keySet()));

        Map<String, LovLineDTO> result = lovIdAndLovMap.values().stream().collect(Collectors.toMap(LovLineDTO::getLovName, i -> i, (k1, k2) -> k1));

        // 将翻译的Name映射为最终的Map
        result.putAll(
                line.stream()
                        .collect(Collectors.toMap(LovTl::getName, i -> {
                            LovLineDTO lovLineDTO = lovIdAndLovMap.get(i.getPkId());
                            LovLineDTO l = lovLineDEConvert.deepCopy(lovLineDTO);
                            l.setLovName(i.getName());
                            return l;
                        }, (k1, k2) -> k1)));
        return result;
    }

    /**
     * 缓存LOV列表
     *
     * @param lovLineList list
     */
    public void putLovLinesIntoRedis(List<LovLineDTO> lovLineList) {
        List<com.trinasolar.scp.common.api.base.LovLineDTO> lovLineDTOS = LovLineDEConvert.INSTANCE.toCommonDto(lovLineList);
        lovLineDTOS.forEach(lovLineDTO -> {
            LovHeader lovHeader = getLovHeader(lovLineDTO.getLovHeaderCode());
            //动态SQL的ID可能有重复，设置标志不单独缓存ID
            if (lovHeader != null && LovTypeEnum.DYNAMIC_SCRIPT.getValue().equals(lovHeader.getLovTypeId())) {
                lovLineDTO.setSourceSystemId(-9999);
            }
        });
        lovCacheService.putLovLinesIntoRedis(lovLineDTOS);
    }

    /**
     * 从缓存移除LOV列表
     *
     * @param lovLineList list
     */
    public void clearLovLinesFromRedis(List<LovLineDTO> lovLineList) {
        lovCacheService.clearLovLinesFromRedis(lovLineList);
    }

    private LovLineDTO toDto(LovLine lovLine) {
        LovLineDTO lineDTO = LovLineDEConvert.INSTANCE.toDto(lovLine);
        LovHeader lovHeader = getLovHeader(lovLine.getLovHeaderId().toString());
        if (lovHeader != null) {
            lineDTO.setLovHeaderCode(lovHeader.getLovCode());
        }
        return lineDTO;
    }

    private LovHeader getLovHeader(String code) {
        return lovHeaderService.queryByCode(code);
    }
}
