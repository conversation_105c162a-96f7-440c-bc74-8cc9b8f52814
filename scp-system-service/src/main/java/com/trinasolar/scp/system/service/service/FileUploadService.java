package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.FileTokenDTO;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

/**
 * 系统语言 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:17
 */
public interface FileUploadService {
    FileTokenDTO getToken();

    String upload(MultipartFile file) ;
}

