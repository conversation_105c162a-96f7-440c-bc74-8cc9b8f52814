package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.system.domain.dto.ApprovalSupplierDTO;
import com.trinasolar.scp.system.domain.query.ApprovalSupplierQuery;
import com.trinasolar.scp.system.domain.save.ApprovalSupplierSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 批准供应商 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-26 20:32:43
 */
public interface ApprovalSupplierService {
    /**
     * 分页获取批准供应商
     *
     * @param query 查询对象
     * @return 批准供应商分页对象
     */
    Page<ApprovalSupplierDTO> queryByPage(ApprovalSupplierQuery query);

    /**
     * 根据主键获取批准供应商详情
     *
     * @param id 主键
     * @return 批准供应商详情
     */
    ApprovalSupplierDTO queryById(Long id);

    /**
     * 保存或更新批准供应商
     *
     * @param saveDTO 批准供应商保存对象
     * @return 批准供应商对象
     */
    ApprovalSupplierDTO save(ApprovalSupplierSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除批准供应商
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query
     * @param response
     */
    void export(ApprovalSupplierQuery query, HttpServletResponse response);

    /**
     * 导入
     *
     * @param multipartFile
     * @return 批次号
     */
    String importData(MultipartFile multipartFile, ExcelPara excelPara);

    void sync();

    void syncFromLov();

    void dailySync();
}

