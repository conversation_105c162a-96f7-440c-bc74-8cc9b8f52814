package com.trinasolar.scp.system.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName: AttrHeaderCategory.java, Created by IntelliJ IDEA
 * @Description: TODO
 * @Author: wang_jun
 * @Date: 2022/7/6 14:00
 * @Version :1.0
 **/
@AllArgsConstructor
@Getter
public enum AttrHeaderCategory {

    PRODUCT_FMAILY(1, "产品族"),
    MATERIAL(2, "材料"),
    MODULE(3, "组件"),
    OTHER(4, "其他"),
    MATERIAL_CERT(5, "材料认证");

    private Integer code;
    private String desc;
}
