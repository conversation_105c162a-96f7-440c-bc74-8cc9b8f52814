package com.trinasolar.scp.system.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.enums.YesOrNoEnum;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.system.domain.convert.AttrTypeLineDEConvert;
import com.trinasolar.scp.system.domain.dto.AttrTypeHeaderDTO;
import com.trinasolar.scp.system.domain.dto.AttrTypeLineDTO;
import com.trinasolar.scp.system.domain.dto.ItemAttrLovDTO;
import com.trinasolar.scp.system.domain.entity.*;
import com.trinasolar.scp.system.domain.query.AttrTypeLineQuery;
import com.trinasolar.scp.system.domain.save.AttrTypeLineSaveDTO;
import com.trinasolar.scp.system.service.enums.AttrHeaderCategory;
import com.trinasolar.scp.system.service.enums.AttrValueTypeEnum;
import com.trinasolar.scp.system.service.feign.BomFeign;
import com.trinasolar.scp.system.service.repository.AttrLineMapRepository;
import com.trinasolar.scp.system.service.repository.AttrTypeHeaderRepository;
import com.trinasolar.scp.system.service.repository.AttrTypeLineRepository;
import com.trinasolar.scp.system.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/26
 */
@Service("attrTypeLineService")
@Slf4j
@CacheConfig(cacheManager = "scpRedisCacheManager")
public class AttrTypeLineServiceImpl implements AttrTypeLineService {
    QAttrTypeLine qAttrTypeLine = QAttrTypeLine.attrTypeLine;

    QAttrTypeHeader qAttrTypeHeader = QAttrTypeHeader.attrTypeHeader;

    @Autowired
    AttrTypeLineDEConvert attrTypeLineDEConvert;

    @Autowired
    AttrTypeLineRepository attrTypeLineRepository;

    @Autowired
    AttrTypeHeaderRepository attrTypeHeaderRepository;

    @Autowired
    @Lazy
    AttrTypeHeaderService attrTypeHeaderService;

    @Autowired
    @Lazy
    AttrTypeLineService attrTypeLineService;


    @Autowired
    AttrLineMapRepository attrLineMapRepository;

    @Autowired
    LovHeaderService lovHeaderRepository;

    @Autowired
    JPAQueryFactory jpaQueryFactory;

    @Autowired
    BomFeign bomFeign;

    @Autowired
    private LovLineService lovLineService;

    @Autowired
    private LovCacheService lovCacheService;

    @Override
    public Page<AttrTypeLine> queryByPage(AttrTypeLineQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<AttrTypeLine> all = attrTypeLineRepository.findAll(booleanBuilder, pageable);
        all.getContent().forEach(this::setSourceAttr);

        return all;
    }

    private void setSourceAttr(AttrTypeLine line) {
        if (line.getAttrSourceAttrId() == null) {
            return;
        }

        AttrTypeLine sourceLine = attrTypeLineRepository.findById(line.getAttrSourceAttrId()).orElse(null);
        if (sourceLine == null) {
            return;
        }
        line.setAttrSourceAttrName(sourceLine.getAttrCnName());

        AttrTypeHeader header = attrTypeHeaderRepository.getOne(sourceLine.getAttrTypeHeaderId());
        line.setAttrSourceHeaderName(header.getAttrTypeCnName());
    }

    @Override
    @Cacheable(cacheNames = "AttrType_queryById", key = "#id", unless = "#result == null")
    public AttrTypeLineDTO queryById(Long id) {
        AttrTypeLine queryObj = attrTypeLineRepository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }
        setSourceAttr(queryObj);
        AttrTypeLineDTO result = new AttrTypeLineDTO();
        BeanUtils.copyProperties(queryObj, result);
        return result;
    }

    @Override
    @Caching(
            evict = {
                    @CacheEvict(cacheNames = "AttrType_queryById", key = "#saveDTO.attrLineId", condition = "#saveDTO.attrLineId != null"),
                    @CacheEvict(cacheNames = "AttrType_byHeaderId", allEntries = true)
            }
    )
    public AttrTypeLineDTO save(AttrTypeLineSaveDTO saveDTO) {
        // 判断Code唯一
        AttrTypeLine byCode = attrTypeLineRepository.getByCodeAndAttrTypeHeaderId(saveDTO.getAttrCode(), saveDTO.getAttrTypeHeaderId());
        if (byCode != null) {
            if (!Objects.equals(byCode.getAttrLineId(), saveDTO.getAttrLineId())) {
                throw new BizException("AttrCode 必须唯一");
            }
        }

        AttrTypeLine newObj;
        if (saveDTO.getAttrLineId() != null) {
            newObj = attrTypeLineRepository.getOne(saveDTO.getAttrLineId());
        } else {
            newObj = new AttrTypeLine();
        }

        BeanUtils.copyProperties(saveDTO, newObj);
        attrTypeLineRepository.save(newObj);

        // 清除LovUtil的Redis缓存
        lovCacheService.clearLovHeaderCache(LovUtils.ATTR);

        return this.queryById(newObj.getAttrLineId());
    }

    @Override
    @Caching(
            evict = {
                    @CacheEvict(cacheNames = "AttrType_queryById", key = "#id"),
                    @CacheEvict(cacheNames = "AttrType_byHeaderId", allEntries = true)
            }
    )
    public void deleteById(Long id) {
        attrTypeLineRepository.deleteById(id);
        // 清除LovUtil的Redis缓存
        lovCacheService.clearLovHeaderCache(LovUtils.ATTR);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(
            evict = {
                    @CacheEvict(cacheNames = "AttrType_queryById", allEntries = true),
                    @CacheEvict(cacheNames = "AttrType_byHeaderId", allEntries = true)
            }
    )
    public void savesByHeader(List<AttrTypeLineSaveDTO> lineSaveDTOS, AttrTypeHeader attrTypeHeader) {
        // 1.获取保存更新的id
        List<Long> updateIds = lineSaveDTOS.stream().map(AttrTypeLineSaveDTO::getAttrLineId).collect(Collectors.toList());

        // 2.查询现有的信息
        List<AttrTypeLine> currentLines = attrTypeLineRepository.getByTypeHeaderId(attrTypeHeader.getAttrTypeHeaderId());

        // 3. 判断如果现有的信息，不存在于更新的id列表，则删除
        for (AttrTypeLine currentLine : currentLines) {
            if (!updateIds.contains(currentLine.getAttrLineId())) {
                attrTypeLineRepository.delete(currentLine);
            }
        }

        // 4. 保存所有
        // 验证完毕，保存或更新
        for (AttrTypeLineSaveDTO lineSaveDTO : lineSaveDTOS) {
            lineSaveDTO.setAttrTypeHeaderId(attrTypeHeader.getAttrTypeHeaderId());
            attrTypeLineService.save(lineSaveDTO);
        }
    }

    @Override
    @Cacheable(cacheNames = "AttrType_byHeaderId", key = "#attrTypeHeaderId", unless = "#result == null")
    public List<AttrTypeLineDTO> getByHeaderId(Long attrTypeHeaderId) {
        List<AttrTypeLine> currentLines = attrTypeLineRepository.getByTypeHeaderId(attrTypeHeaderId);

        return currentLines.stream().map(item -> {
            AttrTypeLineDTO attrTypeLineDTO = new AttrTypeLineDTO();
            setSourceAttr(item);
            BeanUtils.copyProperties(item, attrTypeLineDTO);
            AttrValueTypeEnum valueTypeEnum = AttrValueTypeEnum.getByValue(attrTypeLineDTO.getAttrValueTypeId());
            if (valueTypeEnum != null) {
                attrTypeLineDTO.setAttrValueTypeName(valueTypeEnum.getName());
            }
            return attrTypeLineDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AttrTypeLineDTO> queryByHeaderCode(String code) {
        AttrTypeHeader attrTypeHeader = attrTypeHeaderRepository.getByCode(code);
        if (attrTypeHeader == null) {
            return new ArrayList<>();
        }
        List<AttrTypeLine> attrTypeLines = attrTypeLineRepository.getByTypeHeaderId(attrTypeHeader.getAttrTypeHeaderId());
        return attrTypeLines.stream().map(line -> {
            AttrTypeLineDTO attrTypeLineDTO = new AttrTypeLineDTO();
            BeanUtils.copyProperties(line, attrTypeLineDTO);
            return attrTypeLineDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public AttrTypeLineDTO getLineByQuery(AttrTypeLineQuery query) {
        String attrTypeHeaderCode = null;
        if (query.getAttrLineId() != null) {
            AttrTypeLine one = attrTypeLineRepository.findById(query.getAttrLineId()).orElse(null);
            if (one == null) {
                return null;
            }
            AttrTypeLineDTO lineDTO = new AttrTypeLineDTO();
            BeanUtils.copyProperties(one, lineDTO);

            if (StringUtils.isNotBlank(query.getCode())) {
                attrTypeHeaderCode = Optional.ofNullable(attrTypeHeaderRepository.getByCode(query.getCode()))
                        .map(AttrTypeHeader::getAttrTypeCode).orElse(null);

                lineDTO.setAttrTypeHeaderCode(attrTypeHeaderCode);
            }

            return lineDTO;
        }

        Long attrTypeHeaderId = query.getAttrTypeHeaderId();
        if (attrTypeHeaderId == null && StringUtils.isBlank(query.getCode())) {
            throw new BizException("HeaderId和Code不能同时为空");
        }
        if (StringUtils.isNotBlank(query.getCode())) {
            AttrTypeHeader attrTypeHeader = attrTypeHeaderRepository.getByCode(query.getCode());
            if (attrTypeHeader == null) {
                throw new BizException("code查询错误");
            }
            attrTypeHeaderId = attrTypeHeader.getAttrTypeHeaderId();
            attrTypeHeaderCode = attrTypeHeader.getAttrTypeCode();
        }
        if (StringUtils.isBlank(query.getAttrCnName())) {
            throw new BizException("AttrCnName不能为空");
        }
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(qAttrTypeLine.attrTypeHeaderId.eq(attrTypeHeaderId));
        booleanBuilder.and(qAttrTypeLine.attrCnName.eq(query.getAttrCnName()));
        AttrTypeLine attrTypeLine = attrTypeLineRepository.findOne(booleanBuilder).orElse(null);

        if (attrTypeLine == null) {
            return null;
        }
        AttrTypeLineDTO lineDTO = new AttrTypeLineDTO();
        BeanUtils.copyProperties(attrTypeLine, lineDTO);
        lineDTO.setAttrTypeHeaderCode(attrTypeHeaderCode);
        return lineDTO;
    }

    /**
     * 1. Lov Header和Line要有更新逻辑
     * 2. 生成LovHeader时,没有判断原有的属性(数据)是不是Lov (bom_item_attr.src_attr_type = lov)
     * 3. 更新绑定逻辑需要查看, 好像只绑了OneToMany , OneToOne 有问题
     * 使用表需要用uat后缀:
     * AttrLineMap AttrTypeLine AttrTypeHeader LovLine LovHeader
     */
    @Override
    public void syncAttrTypeLov() {
        // 先调用Bom的sync同步,然后再调用
        bomFeign.itemAttrSync();
        bomFeign.itemAttrLovSync();

        List<AttrTypeLine> attrTypeLineList = getAttrTypeLines();
        if (CollectionUtils.isEmpty(attrTypeLineList)) {
            return;
        }

        // 获取需要更新的所有属性行对应的属性头
        Map<Long, AttrTypeHeader> attrTypeHeaderMap = getLongAttrTypeHeaderMap(attrTypeLineList);

        for (AttrTypeLine attrTypeLine : attrTypeLineList) {
            // 获取这个AttrTypeLine所对应的Map信息
            List<AttrLineMap> attrLineMapList = attrLineMapRepository.findByAttrLineIdEquals(attrTypeLine.getAttrLineId());
            if (CollectionUtils.isEmpty(attrLineMapList)) {
                continue;
            }

            // 如果Map信息只有一个执行一对一的操作, 否则执行一对多的操作
            if (attrLineMapList.size() == 1) {
                oneOnOneProcess(attrTypeHeaderMap, attrTypeLine, attrLineMapList);
            } else {
                oneOnManyProcess(attrTypeHeaderMap, attrTypeLine, attrLineMapList);
            }
        }
    }

    /**
     * 返回一个Map, key是AttrTypeHeader的Id,  value 是这个AttrTypeHeader
     *
     * @param attrTypeLineList
     * @return
     */
    private Map<Long, AttrTypeHeader> getLongAttrTypeHeaderMap(List<AttrTypeLine> attrTypeLineList) {
        Set<Long> attrTypeHeaderIds = attrTypeLineList.stream().map(AttrTypeLine::getAttrTypeHeaderId).collect(Collectors.toSet());
        List<AttrTypeHeader> attrTypeHeaderList = attrTypeHeaderRepository.findAllById(attrTypeHeaderIds);
        Map<Long, AttrTypeHeader> attrTypeHeaderMap = attrTypeHeaderList.stream().collect(Collectors.toMap(AttrTypeHeader::getAttrTypeHeaderId, Function.identity()));
        return attrTypeHeaderMap;
    }

    private void oneOnManyProcess(Map<Long, AttrTypeHeader> attrTypeHeaderMap, AttrTypeLine attrTypeLine, List<AttrLineMap> attrLineMapList) {
        List<ItemAttrLovDTO> itemAttrLovDTOList = new ArrayList<>();
        for (AttrLineMap attrLineMap : attrLineMapList) {
            List<ItemAttrLovDTO> itemAttrLovDTOListTemp =
                    Optional.ofNullable(bomFeign.queryListBySrcAttrId(new IdDTO(attrLineMap.getSrcAttrId())).getBody())
                            .map(Results::getData)
                            .orElse(null);
            if (CollectionUtils.isEmpty(itemAttrLovDTOListTemp)) {
                log.info("Src Attr Id 在bom中找不到 :{}", attrLineMap.getSrcAttrId());
                continue;
            }
            itemAttrLovDTOList.addAll(itemAttrLovDTOListTemp);
        }

        if (CollectionUtils.isEmpty(itemAttrLovDTOList)) {
            return;
        }
        // 按照源系统属性值集去重，生成lov
        List<ItemAttrLovDTO> distinctItemAttrLovDTOList = itemAttrLovDTOList.stream()
                .sorted(Comparator
                        .comparing(ItemAttrLovDTO::getSrcAttrId)
                ).collect(Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(ItemAttrLovDTO::getLovLineValue))
                ), ArrayList::new));

        // 生成lov
        LovHeader lovHeader = lovHeaderRepository.queryByCode(distinctItemAttrLovDTOList.get(0).getSrcAttrId());

        if (lovHeader == null) {
            createLovHeaderAndLine(attrTypeHeaderMap, attrTypeLine, distinctItemAttrLovDTOList);
        } else {
            updateLovHeaderAndLine(lovHeader, attrTypeHeaderMap, attrTypeLine, distinctItemAttrLovDTOList);
        }
    }

    private void oneOnOneProcess(Map<Long, AttrTypeHeader> attrTypeHeaderMap, AttrTypeLine attrTypeLine, List<AttrLineMap> attrLineMapList) {
        // 一对一操作只有一个
        AttrLineMap firstAttrLineMap = attrLineMapList.get(0);

        Results<List<ItemAttrLovDTO>> body = bomFeign.queryListBySrcAttrId(new IdDTO(firstAttrLineMap.getSrcAttrId())).getBody();
        List<ItemAttrLovDTO> itemAttrLovDTOList =
                Optional.ofNullable(body)
                        .map(Results::getData)
                        .orElse(null);
        if (CollectionUtils.isEmpty(itemAttrLovDTOList)) {
            log.info("Src Attr Id 在bom中找不到 :  {}", firstAttrLineMap.getSrcAttrId());
            return;
        }

        LovHeader lovHeader = lovHeaderRepository.queryByCode(itemAttrLovDTOList.get(0).getSrcAttrId());

        if (lovHeader == null) {
            // 生成lov
            createLovHeaderAndLine(attrTypeHeaderMap, attrTypeLine, itemAttrLovDTOList);
        } else {
            updateLovHeaderAndLine(lovHeader, attrTypeHeaderMap, attrTypeLine, itemAttrLovDTOList);
        }
    }

    private void updateLovHeaderAndLine(LovHeader lovHeader, Map<Long, AttrTypeHeader> attrTypeHeaderMap, AttrTypeLine attrTypeLine, List<ItemAttrLovDTO> itemAttrLovDTOList) {
        AttrTypeHeader attrTypeHeader = attrTypeHeaderMap.get(attrTypeLine.getAttrTypeHeaderId());

        // 修改头
        lovHeader.setLovCode(itemAttrLovDTOList.get(0).getSrcAttrId());
        lovHeader.setLovName(attrTypeHeader.getAttrTypeCnName().concat("_").concat(attrTypeLine.getAttrCnName()));
        lovHeader.setLovDesc(lovHeader.getLovName());
        lovHeader.setEnableFlag("Y");
        lovHeader.setLovTypeId(1);

        lovHeaderRepository.save(lovHeader);


        // 需要去比较lovline 不存在才插入
        List<LovLine> lovLines = lovLineService.listByLovHeaderId(lovHeader.getLovHeaderId());
        Set<String> currentLovValues = lovLines.stream().map(LovLine::getLovValue).collect(Collectors.toSet());

        List<ItemAttrLovDTO> filterItemAttrLovDtos = itemAttrLovDTOList.stream()
                .filter(item -> {
                    if (currentLovValues.contains(item.getLovLineValue())) {
                        return false;
                    } else {
                        // 更新现在有的值
                        currentLovValues.add(item.getLovLineValue());
                        return true;
                    }
                }).collect(Collectors.toList());

        if (filterItemAttrLovDtos.isEmpty()) {
            return;
        }

        int colNo = lovLines.size() * 10;
        LovLine lovLine;
        List<LovLine> lovLineList = new ArrayList<>(filterItemAttrLovDtos.size());

        for (ItemAttrLovDTO itemAttrLovDTO : filterItemAttrLovDtos) {
            colNo += 10;
            lovLine = new LovLine();
            lovLine.setLovHeaderId(lovHeader.getLovHeaderId());
            lovLine.setLovName(itemAttrLovDTO.getLovLineValue());
            lovLine.setColNo(colNo);
            lovLine.setLovValue(itemAttrLovDTO.getLovLineValue());
            lovLine.setEffectiveStartDate(LocalDate.now());
            lovLine.setEnableFlag(YesOrNoEnum.YES.getCode());

            lovLineList.add(lovLine);
        }

        lovLineService.saveAll(lovLineList);
        if (StringUtils.isBlank(attrTypeLine.getAttrLovName())) {
            attrTypeLine.setAttrLovName(lovHeader.getLovCode());
            attrTypeLine.setAttrUiType("select");
            attrTypeLineRepository.save(attrTypeLine);
        }
    }

    private void createLovHeaderAndLine(Map<Long, AttrTypeHeader> attrTypeHeaderMap, AttrTypeLine attrTypeLine, List<ItemAttrLovDTO> itemAttrLovDTOList) {
        LovHeader lovHeader = createLov(attrTypeLine, attrTypeHeaderMap.get(attrTypeLine.getAttrTypeHeaderId()), itemAttrLovDTOList);
        if (StringUtils.isBlank(attrTypeLine.getAttrLovName())) {
            attrTypeLine.setAttrLovName(lovHeader.getLovCode());
            attrTypeLine.setAttrUiType("select");
            attrTypeLineRepository.save(attrTypeLine);
        }
    }

    /**
     * 获取属性大类的AttrTypeLine
     *
     * @return
     */
    private List<AttrTypeLine> getAttrTypeLines() {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(qAttrTypeHeader.attrTypeCode.like("ATTR_TYPE_".concat("%")));
//        booleanBuilder.and(qAttrTypeLine.attrLovName.isEmpty().or(qAttrTypeLine.attrLovName.isNull()));
        booleanBuilder.and(qAttrTypeHeader.attrCategoryId.eq(AttrHeaderCategory.MATERIAL.getCode()));

        List<AttrTypeLine> attrTypeLineList = jpaQueryFactory
                .selectFrom(qAttrTypeLine)
                .innerJoin(qAttrTypeHeader).on(qAttrTypeLine.attrTypeHeaderId.eq(qAttrTypeHeader.attrTypeHeaderId))
                .where(booleanBuilder).fetch();
        return attrTypeLineList;
    }

    @Override
    public List<AttrTypeLineDTO> queryByAttrLineId(List<Long> ids) {
        List<AttrTypeLine> attrTypeLineList = attrTypeLineRepository.findByIsDeletedAndAttrLineIdIn(0, ids);
        return attrTypeLineDEConvert.toDto(attrTypeLineList);
    }

    @Override
    public List<AttrTypeLineDTO> queryList(AttrTypeLineQuery query) {
        JPAQuery<AttrTypeLine> jpaQuery = jpaQueryFactory
                .selectFrom(qAttrTypeLine)
                .innerJoin(qAttrTypeHeader)
                .on(qAttrTypeLine.attrTypeHeaderId.eq(qAttrTypeHeader.attrTypeHeaderId))
                .where(qAttrTypeHeader.attrTypeCode.eq(query.getCode())
                        .and(qAttrTypeHeader.isDeleted.eq(0))
                        .and(qAttrTypeLine.isDeleted.eq(0))
                );
        if (StringUtils.isNotBlank(query.getEnableFlag())) {
            jpaQuery.where(qAttrTypeLine.enableFlag.eq(query.getEnableFlag()));
        }
        List<AttrTypeLine> attrTypeLines = jpaQuery.fetch();
        return attrTypeLines.stream().map(line -> {
            AttrTypeLineDTO attrTypeLineDTO = new AttrTypeLineDTO();
            BeanUtils.copyProperties(line, attrTypeLineDTO);
            return attrTypeLineDTO;
        }).collect(Collectors.toList());
    }

    @Override
    @Cacheable(cacheNames = "AttrType_byHeaderId", key = "'itemLov_'+#categorySegment5.attrTypeHeaderId", unless = "#result == null")
    public List<AttrTypeLineDTO> findItemTransToLovAttrLines(AttrTypeHeaderDTO categorySegment5) {
        List<AttrTypeLineDTO> byHeaderId = getByHeaderId(categorySegment5.getAttrTypeHeaderId());
        return byHeaderId.stream().filter(item -> "Y".equals(item.getAttribute5())).collect(Collectors.toList());
    }

    private LovHeader createLov(AttrTypeLine attrTypeLine, AttrTypeHeader attrTypeHeader, List<ItemAttrLovDTO> itemAttrLovDTOList) {
        LovHeader lovHeader = new LovHeader();
        lovHeader.setLovCode(itemAttrLovDTOList.get(0).getSrcAttrId());
        lovHeader.setLovName(attrTypeHeader.getAttrTypeCnName().concat("_").concat(attrTypeLine.getAttrCnName()));
        lovHeader.setLovDesc(lovHeader.getLovName());
        lovHeader.setLovCategoryId(AttrHeaderCategory.MATERIAL.getCode());
        lovHeader.setEffectiveStartDate(LocalDate.now());
        lovHeader.setEnableFlag("Y");
        lovHeader.setLovTypeId(1);

        lovHeaderRepository.save(lovHeader);

        int colNo = 0;
        LovLine lovLine;
        List<LovLine> lovLineList = new ArrayList<>(itemAttrLovDTOList.size());

        for (ItemAttrLovDTO itemAttrLovDTO : itemAttrLovDTOList) {
            colNo += 10;
            lovLine = new LovLine();
            lovLine.setLovHeaderId(lovHeader.getLovHeaderId());
            lovLine.setLovName(itemAttrLovDTO.getLovLineValue());
            lovLine.setColNo(colNo);
            lovLine.setLovValue(itemAttrLovDTO.getLovLineValue());
            lovLine.setEffectiveStartDate(LocalDate.now());
            lovLine.setEnableFlag(YesOrNoEnum.YES.getCode());
            lovLine.setEnableFlag(YesOrNoEnum.YES.getCode());

            lovLineList.add(lovLine);
        }

        lovLineService.saveAll(lovLineList);

        return lovHeader;
    }
}
