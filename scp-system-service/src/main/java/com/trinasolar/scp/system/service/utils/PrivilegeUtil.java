package com.trinasolar.scp.system.service.utils;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.trinasolar.scp.common.api.base.DataPrivilegeDTO;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.DataPrivilegeUtils;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.UserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.EntityManager;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/21
 */
public class PrivilegeUtil {
    public static void chekMatType(BooleanBuilder booleanBuilder, NumberPath<Long> numberPath) {
        List<DataPrivilegeDTO> mattypeDataPrivilege = UserUtil.getMattypeDataPrivilege();
        List<Long> matTypeIds = mattypeDataPrivilege.stream().map(DataPrivilegeDTO::getDataId).collect(Collectors.toList());
        if (!matTypeIds.contains(-1L)) {
            List<Long> collect = matTypeIds.stream().map(i -> Long.parseLong(i.toString())).collect(Collectors.toList());
            BooleanExpression typeIdsIn = numberPath.in(collect);
            booleanBuilder.and(typeIdsIn);
        }
    }

    public static void checkBasePlace(EntityManager entityManager, BooleanBuilder booleanBuilder, StringPath path) {
        List<String> basePlaceCodes = DataPrivilegeUtils.getPrivilegeCodes(entityManager, "base_place");
        if (!basePlaceCodes.contains("-1")) {
            BooleanExpression booleanExpression = path.in(basePlaceCodes);
            booleanBuilder.and(booleanExpression);
        }
    }

    public static List<Long> getMatTypeIds() {
        List<DataPrivilegeDTO> mattypeDataPrivilege = UserUtil.getMattypeDataPrivilege();
        List<Long> matTypeIds = mattypeDataPrivilege.stream().map(DataPrivilegeDTO::getDataId).collect(Collectors.toList());
        if (!matTypeIds.contains(-1L)) {
            if (CollectionUtils.isEmpty(matTypeIds)) {
                return Collections.singletonList(-1L);
            } else {
                return matTypeIds;
            }
        } else {
            // 是全部返回空
            return new ArrayList<>();
        }
    }

    /**
     * 获取真实的物料名称,比如 前胶膜会返回胶膜
     *
     * @return
     */
    public static List<String> getRealMatTypeName() {
        List<DataPrivilegeDTO> mattypeDataPrivilege = UserUtil.getMattypeDataPrivilege();
        List<Long> matTypeIds = mattypeDataPrivilege.stream().map(DataPrivilegeDTO::getDataId).collect(Collectors.toList());
        if (!matTypeIds.contains(-1L)) {
            if (CollectionUtils.isEmpty(matTypeIds)) {
                return Collections.singletonList("");
            } else {
                ArrayList<String> result = new ArrayList<>();
                for (Long matTypeId : matTypeIds) {
                    LovLineDTO lovLineDTO = LovUtils.get(matTypeId);
                    if (StringUtils.isBlank(lovLineDTO.getAttribute7())) {
                        result.add(lovLineDTO.getLovName());
                    } else {
                        result.add(lovLineDTO.getAttribute7());
                    }
                }
                return result.stream().distinct().collect(Collectors.toList());
            }
        } else {
            // 是全部返回空
            return new ArrayList<>();
        }
    }


    public static List<String> getBasePlace(EntityManager entityManager) {
        List<String> basePlaceCodes = DataPrivilegeUtils.getPrivilegeCodes(entityManager, "base_place");
        if (!basePlaceCodes.contains("-1")) {
            if (CollectionUtils.isEmpty(basePlaceCodes)) {
                return Collections.singletonList("-1");
            } else {
                return basePlaceCodes;
            }
        } else {
            // 是全部返回空
            return new ArrayList<>();
        }
    }
}
