package com.trinasolar.scp.system.service.service.impl;

import cn.hutool.cache.impl.LRUCache;
import cn.hutool.cache.impl.TimedCache;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import com.trinasolar.scp.system.domain.convert.ErpApprovalSupplierFixLotQtyDEConvert;
import com.trinasolar.scp.system.domain.dto.ErpApprovalSupplierFixLotQtyDTO;
import com.trinasolar.scp.system.domain.entity.ErpApprovalSupplierFixLotQty;
import com.trinasolar.scp.system.domain.entity.QErpApprovalSupplierFixLotQty;
import com.trinasolar.scp.system.domain.excel.ErpApprovalSupplierFixLotQtyExcelDTO;
import com.trinasolar.scp.system.domain.feign.ItemsOptionQuery;
import com.trinasolar.scp.system.domain.feign.MainMaterialInfoSaveDTO;
import com.trinasolar.scp.system.domain.query.ErpApprovalSupplierFixLotQtyQuery;
import com.trinasolar.scp.system.domain.save.ErpApprovalSupplierFixLotQtySaveDTO;
import com.trinasolar.scp.system.service.enums.Constants;
import com.trinasolar.scp.system.service.feign.BomFeign;
import com.trinasolar.scp.system.service.repository.ErpApprovalSupplierFixLotQtyRepository;
import com.trinasolar.scp.system.service.service.ErpApprovalSupplierFixLotQtyService;
import com.trinasolar.scp.system.service.service.ErpSupplierService;
import com.trinasolar.scp.system.service.utils.PrivilegeUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 合格供应商整车数或整柜数表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-09 07:56:06
 */
@Slf4j
@Service("erpApprovalSupplierFixLotQtyService")
@RequiredArgsConstructor
@CacheConfig(cacheManager = "caffeineCacheManager")
public class ErpApprovalSupplierFixLotQtyServiceImpl implements ErpApprovalSupplierFixLotQtyService {
    private static final QErpApprovalSupplierFixLotQty qErpApprovalSupplierFixLotQty = QErpApprovalSupplierFixLotQty.erpApprovalSupplierFixLotQty;

    private final ErpApprovalSupplierFixLotQtyDEConvert convert;

    private final ErpApprovalSupplierFixLotQtyRepository repository;

    private final ErpSupplierService erpSupplierService;

    private final BomFeign bomFeign;

    @Autowired
    @Lazy
    private ErpApprovalSupplierFixLotQtyService erpApprovalSupplierFixLotQtyService;

    LRUCache<String, MainMaterialInfoSaveDTO> itemCache = new LRUCache<>(1000, 1000 * 10 * 60);

    TimedCache<String, Map<String, LovLineDTO>> lovCache = new TimedCache<>(1000 * 60 * 10);

    @Override
    public Page<ErpApprovalSupplierFixLotQtyDTO> queryByPage(ErpApprovalSupplierFixLotQtyQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<ErpApprovalSupplierFixLotQty> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, ErpApprovalSupplierFixLotQtyQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qErpApprovalSupplierFixLotQty.id.eq(query.getId()));
        }
        if (query.getItemId() != null) {
            booleanBuilder.and(qErpApprovalSupplierFixLotQty.itemId.eq(query.getItemId()));
        }
        if (StringUtils.isNotEmpty(query.getItemNum())) {
            booleanBuilder.and(qErpApprovalSupplierFixLotQty.itemNum.eq(query.getItemNum()));
        }
        if (StringUtils.isNotEmpty(query.getItemDescription())) {
            booleanBuilder.and(qErpApprovalSupplierFixLotQty.itemDescription.eq(query.getItemDescription()));
        }
        if (StringUtils.isNotEmpty(query.getCategorySegment5())) {
            booleanBuilder.and(qErpApprovalSupplierFixLotQty.categorySegment5.eq(query.getCategorySegment5()));
        }
        if (StringUtils.isNotEmpty(query.getPriUom())) {
            booleanBuilder.and(qErpApprovalSupplierFixLotQty.priUom.eq(query.getPriUom()));
        }
        if (query.getVendorId() != null) {
            booleanBuilder.and(qErpApprovalSupplierFixLotQty.vendorId.eq(query.getVendorId()));
        }
        if (StringUtils.isNotEmpty(query.getVendorName())) {
            booleanBuilder.and(qErpApprovalSupplierFixLotQty.vendorName.eq(query.getVendorName()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qErpApprovalSupplierFixLotQty.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qErpApprovalSupplierFixLotQty.basePlace.eq(query.getBasePlace()));
        }
        if (query.getFixLotQty() != null) {
            booleanBuilder.and(qErpApprovalSupplierFixLotQty.fixLotQty.eq(query.getFixLotQty()));
        }
        List<String> matType = PrivilegeUtil.getRealMatTypeName();
        if (!matType.isEmpty()) {
            // 只能查询当前用户有权限的物料类型
            booleanBuilder.and(qErpApprovalSupplierFixLotQty.categorySegment5.in(matType));
        }
    }

    @Override
    public ErpApprovalSupplierFixLotQtyDTO queryById(Long id) {
        ErpApprovalSupplierFixLotQty queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public ErpApprovalSupplierFixLotQtyDTO save(ErpApprovalSupplierFixLotQtySaveDTO saveDTO) {
        ErpApprovalSupplierFixLotQty newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new ErpApprovalSupplierFixLotQty());

        BeanUtils.copyProperties(saveDTO, newObj, "id");
        // 通过料号查询物料详情
        MainMaterialInfoSaveDTO materialInfoSaveDTO;
        if (itemCache.get(saveDTO.getItemNum()) != null) {
            materialInfoSaveDTO = itemCache.get(saveDTO.getItemNum());
        } else {
            materialInfoSaveDTO = bomFeign.getMaterialInfoByItemCode(ItemsOptionQuery.builder().code(saveDTO.getItemNum()).build()).getBody().getData();
            itemCache.put(saveDTO.getItemNum(), materialInfoSaveDTO);
        }
        newObj.setItemId(Long.parseLong(materialInfoSaveDTO.getItemId()));
        newObj.setItemDescription(materialInfoSaveDTO.getItemDescription());
        newObj.setPriUom(materialInfoSaveDTO.getUom());
        newObj.setCategorySegment5(materialInfoSaveDTO.getCategorySegment5());
        // 通过供应商查询供应商ID
        Optional.ofNullable(erpSupplierService.getByName(saveDTO.getVendorName()))
                .ifPresent(supplier -> {
                    newObj.setVendorId(supplier.getVendorId());
                });
        // 处理生产基地和国内海外
        Map<String, LovLineDTO> allByHeaderCode = lovCache.get(Constants.BASE_PLACE, () -> LovUtils.getAllByHeaderCode(Constants.BASE_PLACE));
        LovLineDTO lovLineDTO = allByHeaderCode.get(saveDTO.getBasePlace());
        if (lovLineDTO != null) {
            newObj.setIsOversea(LovUtils.get(Long.parseLong(lovLineDTO.getAttribute2())).getLovValue());
        } else {
            saveDTO.setIsOversea(saveDTO.getIsOversea());
        }

        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(ErpApprovalSupplierFixLotQtyQuery query, HttpServletResponse response) {
        List<ErpApprovalSupplierFixLotQtyDTO> dtos = queryByPage(query).getContent();

        List<ErpApprovalSupplierFixLotQtyExcelDTO> exportDTOS = convert.toExcelDTO(dtos);
        ExcelUtils.setExportResponseHeader(response, "合格供应商整车数或整柜数表导出_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build()) {

            WriteSheet sheet = EasyExcel.writerSheet(0, "合格供应商整车数或整柜数表").head(ErpApprovalSupplierFixLotQtyExcelDTO.class).build();
            excelWriter.write(exportDTOS, sheet);
        }
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile) {
        List<ErpApprovalSupplierFixLotQtyExcelDTO> excelDtos = new LinkedList<>();
        EasyExcel.read(multipartFile.getInputStream(), ErpApprovalSupplierFixLotQtyExcelDTO.class, new ReadListener<ErpApprovalSupplierFixLotQtyExcelDTO>() {
            @Override
            public void invoke(ErpApprovalSupplierFixLotQtyExcelDTO data, AnalysisContext context) {
                excelDtos.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet().doRead();
        log.info("ErpApprovalSupplierFixLotQtyExcelDTO:{}", excelDtos);
        doVaildImportDTO(excelDtos);
        doUpdateImportDTO(excelDtos);
    }

    private void doVaildImportDTO(List<ErpApprovalSupplierFixLotQtyExcelDTO> excelDtos) {
        excelDtos.stream().collect(Collectors.groupingBy(i -> StringUtils.join(i.getItemNum(), "_", i.getVendorName(),
                "_", i.getBasePlace(), "_", i.getIsOverseaName()))).forEach((key, dtos) -> {
            if (dtos.size() > 1) {
                throw new BizException("相同纬度【" + key + "】在导入文件中重复");
            }
        });
    }

    @Override
    public BigDecimal queryLotQty(ErpApprovalSupplierFixLotQtyQuery query) {
        ErpApprovalSupplierFixLotQtyDTO queryLot = erpApprovalSupplierFixLotQtyService.queryLot(query.getItemNum(), query.getBasePlace(), query.getVendorId());
        return Optional.ofNullable(queryLot).map(ErpApprovalSupplierFixLotQtyDTO::getFixLotQty).orElse(null);
    }

    @Override
    public BigDecimal queryMinimumOrderQuantity(ErpApprovalSupplierFixLotQtyQuery query) {
        ErpApprovalSupplierFixLotQtyDTO queryLot = erpApprovalSupplierFixLotQtyService.queryLot(query.getItemNum(), query.getBasePlace(), query.getVendorId());
        return Optional.ofNullable(queryLot).map(ErpApprovalSupplierFixLotQtyDTO::getMinimumOrderQuantity).orElse(null);
    }

    @Override
    @Cacheable(cacheNames = "ErpApprovalSupplierFixLotQtyService_queryLot", key = "#ItemNum+'_'+#basePlace+'_'+#vendorId", unless = "#result == null")
    public ErpApprovalSupplierFixLotQtyDTO queryLot(String ItemNum, String basePlace, Long vendorId) {
        // 校验参数
        if (StringUtils.isBlank(ItemNum)) {
            throw new BizException("料号不能为空");
        }
        if (StringUtils.isBlank(basePlace)) {
            throw new BizException("生产基地不能为空");
        }
        if (vendorId == null) {
            throw new BizException("供应商不能为空");
        }
        // 查询数据
        // 1. 先查询能直接匹配上的
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(qErpApprovalSupplierFixLotQty.itemNum.eq(ItemNum));
        booleanBuilder.and(qErpApprovalSupplierFixLotQty.vendorId.eq(vendorId));
        booleanBuilder.and(qErpApprovalSupplierFixLotQty.basePlace.eq(basePlace));
        Optional<ErpApprovalSupplierFixLotQty> findFixLotQty = repository.findOne(booleanBuilder);
        if (findFixLotQty.isPresent()) {
            return convert.toDto(findFixLotQty.get());
        }

        // 2. 如果没有匹配上，再查询能匹配上的
        BooleanBuilder booleanBuilder2 = new BooleanBuilder();
        booleanBuilder2.and(qErpApprovalSupplierFixLotQty.itemNum.eq(ItemNum));
        booleanBuilder2.and(qErpApprovalSupplierFixLotQty.vendorId.eq(vendorId));
        booleanBuilder2.and(qErpApprovalSupplierFixLotQty.basePlace.isNull().or(qErpApprovalSupplierFixLotQty.basePlace.isEmpty()));
        Optional<ErpApprovalSupplierFixLotQty> findFixLotQty2 = repository.findOne(booleanBuilder2);
        return convert.toDto(findFixLotQty2.get());
    }

    @Override
    public Map<String, List<ErpApprovalSupplierFixLotQtyDTO>> getItemCodeAndVendorIdMap() {
        List<ErpApprovalSupplierFixLotQty> all = repository.findAll();
        List<ErpApprovalSupplierFixLotQtyDTO> dto = convert.toDto(all);
        return dto.stream().collect(Collectors.groupingBy(i -> i.getItemNum() + "_" + i.getVendorId()));
    }

    private void doUpdateImportDTO(List<ErpApprovalSupplierFixLotQtyExcelDTO> excelDtos) {
        List<ErpApprovalSupplierFixLotQtySaveDTO> dtos = convert.excelToSaveDto(excelDtos);

        // 通过itemNum_vendorName分组数据
        Map<String, List<ErpApprovalSupplierFixLotQtySaveDTO>> groupedByItemAndVendor =
                dtos.stream().collect(Collectors.groupingBy(i -> i.getItemNum() + "_" + i.getVendorName()));
        Map<String, List<ErpApprovalSupplierFixLotQty>> curDataMap = repository.findAll().stream().collect(Collectors.groupingBy(i -> i.getItemNum() + "_" + i.getVendorName()));
        // 按分组刷新数据
        for (Map.Entry<String, List<ErpApprovalSupplierFixLotQtySaveDTO>> entry : groupedByItemAndVendor.entrySet()) {
            BooleanBuilder booleanBuilder = new BooleanBuilder();
            // 这些需要删除的数据
            List<ErpApprovalSupplierFixLotQty> erpApprovalSupplierFixLotQtyList = curDataMap.getOrDefault(entry.getKey(), new LinkedList<>());
            if (erpApprovalSupplierFixLotQtyList.size() > 0) {
                Map<String, ErpApprovalSupplierFixLotQty> currentFixLotQty = erpApprovalSupplierFixLotQtyList.stream()
                        .collect(Collectors.toMap(i -> StringUtils.join(i.getItemNum(), "_", i.getVendorName(),
                                "_", i.getIsOversea(),
                                "_", i.getBasePlace(), "_", i.getFixLotQty().stripTrailingZeros().toPlainString(),
                                "_", i.getMinimumOrderQuantity()), Function.identity(), (oldValue, newValue) -> newValue));
                AtomicBoolean changed = new AtomicBoolean(false);
                entry.getValue().forEach(i -> {
                    String identityKey = StringUtils.join(i.getItemNum(), "_", i.getVendorName(),
                            "_", i.getIsOversea(),
                            "_", i.getBasePlace(), "_", i.getFixLotQty().stripTrailingZeros().toPlainString(),
                            "_", i.getMinimumOrderQuantity()
                    );
                    if (currentFixLotQty.get(identityKey) == null) {
                        changed.set(true);
                    }
                });

                if (!changed.get()) {
                    continue;
                }
                repository.realDelete(erpApprovalSupplierFixLotQtyList.stream().map(ErpApprovalSupplierFixLotQty::getId).collect(Collectors.toList()));
            }
            entry.getValue().forEach(this::save);
        }
    }

}
