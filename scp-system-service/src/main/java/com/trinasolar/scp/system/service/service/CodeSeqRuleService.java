package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.CodeSeqRuleDTO;
import com.trinasolar.scp.system.domain.entity.CodeSeqRule;
import com.trinasolar.scp.system.domain.query.CodeSeqRuleQuery;
import com.trinasolar.scp.system.domain.save.CodeSeqRuleSaveDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 单据序列规则定义表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-09 15:05:51
 */
public interface CodeSeqRuleService {
    /**
     * 分页获取单据序列规则定义表
     *
     * @param query 查询对象
     * @return 单据序列规则定义表分页对象
     */
    Page<CodeSeqRule> queryByPage(CodeSeqRuleQuery query);

    /**
     * 根据主键获取单据序列规则定义表详情
     *
     * @param id 主键
     * @return 单据序列规则定义表详情
     */
    CodeSeqRuleDTO queryById(Long id);

    /**
     * 保存或更新单据序列规则定义表
     *
     * @param saveDTO 单据序列规则定义表保存对象
     * @return 单据序列规则定义表对象
     */
    CodeSeqRuleDTO save(CodeSeqRuleSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除单据序列规则定义表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);
}

