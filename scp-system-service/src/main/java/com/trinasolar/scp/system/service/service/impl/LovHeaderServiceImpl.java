package com.trinasolar.scp.system.service.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.common.api.base.BaseServiceImpl;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.system.domain.dto.LovHeaderDTO;
import com.trinasolar.scp.system.domain.entity.*;
import com.trinasolar.scp.system.domain.feign.ItemAttrLovDTO;
import com.trinasolar.scp.system.domain.query.LovHeaderQuery;
import com.trinasolar.scp.system.domain.query.LovLineQuery;
import com.trinasolar.scp.system.domain.save.AttrConfigSaveDTO;
import com.trinasolar.scp.system.domain.save.LovHeaderSaveDTO;
import com.trinasolar.scp.system.service.repository.LovHeaderRepository;
import com.trinasolar.scp.system.service.repository.LovLineRepository;
import com.trinasolar.scp.system.service.service.AttrConfigService;
import com.trinasolar.scp.system.service.service.LovCacheService;
import com.trinasolar.scp.system.service.service.LovHeaderService;
import com.trinasolar.scp.system.service.service.LovLineService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * lov头表(LovHeader)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-24 10:42:26
 */
@Service("lovHeaderService")
@Slf4j
public class LovHeaderServiceImpl extends BaseServiceImpl<LovHeader, Long> implements LovHeaderService {

    QAttrTypeHeader qAttrTypeHeader = QAttrTypeHeader.attrTypeHeader;

    QAttrTypeLine qAttrTypeLine = QAttrTypeLine.attrTypeLine;

    QAttrLineMap qAttrLineMap = QAttrLineMap.attrLineMap;

    QLovHeader qLovHeader = QLovHeader.lovHeader;

    QLovLine qLovLine = QLovLine.lovLine;

    @Autowired
    LovLineRepository lovLineRepository;

    @Autowired
    LovCacheService lovCacheService;

    @Autowired
    JPAQueryFactory jpaQueryFactory;

    @Autowired
    LovHeaderRepository lovHeaderRepository;

    @Autowired
    private LovLineService lovLineService;

    @Autowired
    private AttrConfigService attrConfigService;

    @Autowired
    private CacheManager caffeineCacheManager;

    /**
     * 通过ID查询单条数据
     *
     * @param lovHeaderId 主键
     * @return 实例对象
     */
    @Override
    public LovHeaderDTO queryById(Long lovHeaderId) {
        LovHeader lovHeader = this.findById(lovHeaderId);
        if (lovHeader == null) {
            return null;
        }

        LovHeaderDTO lovHeaderDTO = new LovHeaderDTO();
        BeanUtils.copyProperties(lovHeader, lovHeaderDTO);
        lovHeaderDTO.setLovConfigDTOS(attrConfigService.findBySenceAndHeaderId("sys_lov_lines", lovHeader.getLovHeaderId()));

        return lovHeaderDTO;
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @Override
    public Page<LovHeader> queryByPage(LovHeaderQuery lovHeaderQuery) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        if (lovHeaderQuery != null) {
            if (StringUtils.isNotEmpty(lovHeaderQuery.getCode())) {
                booleanBuilder.and(qLovHeader.lovCode.likeIgnoreCase("%" + lovHeaderQuery.getCode() + "%"));
            }
            if (StringUtils.isNotEmpty(lovHeaderQuery.getName())) {
                booleanBuilder.and(qLovHeader.lovName.likeIgnoreCase("%" + lovHeaderQuery.getName() + "%"));
            }
            if (StringUtils.isNotEmpty(lovHeaderQuery.getEnableFlag())) {
                booleanBuilder.and(qLovHeader.enableFlag.eq(lovHeaderQuery.getEnableFlag()));
            }
            if (lovHeaderQuery.getLovCategoryId() != null) {
                booleanBuilder.and(qLovHeader.lovCategoryId.eq(lovHeaderQuery.getLovCategoryId()));
            }
        }

        //2022-09-10 增加维护权限过滤
        // booleanBuilder.and(DataPrivilegeUtils.getPrivilegeFilter("SYS_LOV_HEADER",qLovHeader.lovHeaderId));
        //END 2022-09-10

        Pageable pageable = PageRequest.of(lovHeaderQuery.getPageNumber() - 1, lovHeaderQuery.getPageSize());

        return this.findAll(booleanBuilder, pageable);
    }

    @Override
    public LovHeaderDTO queryByLovQuery(LovLineQuery lovLineQuery) {
        LovHeader lovHeader = queryByCode(lovLineQuery.getCode());

        if (lovHeader == null) {
            throw new NullPointerException("lovHeader is null");
        }

        LovHeaderDTO lovHeaderDTO = new LovHeaderDTO();
        BeanUtils.copyProperties(lovHeader, lovHeaderDTO);

        lovHeaderDTO.setLineDTOS(lovLineService.queryByLovQuery(lovLineQuery));
        return lovHeaderDTO;
    }

    @Override
    @Transactional
    public LovHeaderDTO save(LovHeaderSaveDTO lovHeaderSaveDTO) throws BizException {
        LovHeader lovHeader;
        lovHeader = this.queryByCode(lovHeaderSaveDTO.getLovCode());
        if (lovHeader != null) {
            if (!Objects.equals(lovHeader.getLovHeaderId(), lovHeaderSaveDTO.getLovHeaderId())) {
                throw new BizException("定义的(code)已存在");
            }
        } else if (lovHeaderSaveDTO.getLovHeaderId() != null) {
            // 修改code
            lovHeader = this.getOne(lovHeaderSaveDTO.getLovHeaderId());
        } else {
            lovHeader = new LovHeader();
        }

        BeanUtils.copyProperties(lovHeaderSaveDTO, lovHeader);
        lovHeader = this.save(lovHeader);
        // 而后保存lovConfigList

        List<AttrConfigSaveDTO> lovConfigSaveDTOList = Optional.ofNullable(lovHeaderSaveDTO.getLovConfigSaveDTOList())
                .orElse(Lists.newArrayList());
        LovHeader finalLovHeader = lovHeader;
        lovConfigSaveDTOList.forEach(saveDto -> {
            saveDto.setAttrHeaderId(finalLovHeader.getLovHeaderId());
            saveDto.setSence("sys_lov_lines");
        });

        attrConfigService.saves("sys_lov_lines", lovHeader.getLovHeaderId(), lovConfigSaveDTOList);
        // 删除头相关的Redis缓存
        lovCacheService.clearLovHeaderCache(lovHeader.getLovCode());

        //更新缓存
        Cache lovHeaderCache = caffeineCacheManager.getCache("lovHeader");

        lovHeaderCache.evict(lovHeader.getLovCode());
        lovHeaderCache.evict(lovHeader.getLovHeaderId().toString());

        lovHeaderCache.put(lovHeader.getLovCode(), JSON.toJSONString(lovHeader));
        lovHeaderCache.put(lovHeader.getLovHeaderId().toString(), JSON.toJSONString(lovHeader));

        return this.queryById(lovHeader.getLovHeaderId());
    }

    /**
     * 获取LovHeader,优先从缓存获取
     *
     * @param code 头code
     * @return 返回头信息
     */
    @Override
    public LovHeader queryByCode(String code) {
        // 应用内缓存LovHeader
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        Cache lovHeaderCache = caffeineCacheManager.getCache("lovHeader");

        LovHeader lovHeader = null;
        try {
            String lovHeaderJSON = lovHeaderCache.get(code, String.class);
            lovHeader = JSON.parseObject(lovHeaderJSON, LovHeader.class);
        } catch (Exception e) {
            log.warn("get lovHeader from cache error", e);
            lovHeader = null;
        }

        if (lovHeader == null) {
            if (StringUtils.isNumeric(code)) {
                lovHeader = this.findOne(Long.valueOf(code));
            }
            if (lovHeader == null) {
//                lovHeader = this.findOne("lovCode", code);
                lovHeader = lovHeaderRepository.findByLovCodeIgnoreCase(code);
            }

            if (lovHeader != null) {
                lovHeaderCache.put(code, JSON.toJSONString(lovHeader));
                lovHeaderCache.put(lovHeader.getLovHeaderId().toString(), JSON.toJSONString(lovHeader));
            } else {
                lovHeaderCache.put(code, JSON.toJSONString(new LovHeader()));
            }
        }

        return (lovHeader == null || lovHeader.getLovHeaderId() == null) ? null : lovHeader;
    }

    @Override
    public void syncItemAttrLov(ItemAttrLovDTO itemAttrLovDTO) {
        List<AttrTypeLine> attrTypeLines = jpaQueryFactory.select(qAttrTypeLine)
                .from(qAttrTypeLine)
                .innerJoin(qAttrTypeHeader)
                .on(qAttrTypeLine.attrTypeHeaderId.eq(qAttrTypeHeader.attrTypeHeaderId)
                        .and(qAttrTypeHeader.isDeleted.eq(0))
                )
                .innerJoin(qAttrLineMap)
                .on(qAttrLineMap.attrLineId.eq(qAttrTypeLine.attrLineId).and(qAttrLineMap.isDeleted.eq(0)))
                .where(qAttrTypeHeader.attrTypeCode.like("ATTR_TYPE%"))
                .where(qAttrLineMap.srcAttrId.eq(itemAttrLovDTO.getSrcAttrId()))
                .fetch();
        for (AttrTypeLine attrTypeLine : attrTypeLines) {
            long count = jpaQueryFactory.select(qLovLine)
                    .from(qLovHeader)
                    .leftJoin(qLovLine)
                    .on(qLovLine.lovHeaderId.eq(qLovHeader.lovHeaderId).and(qLovLine.isDeleted.eq(0)))
                    .where(qLovHeader.lovCode.eq(attrTypeLine.getAttrLovName()))
                    .where(qLovLine.lovName.upper().eq(Optional.ofNullable(itemAttrLovDTO.getLovLineValue()).orElse("")
                            .toUpperCase()))
                    .fetchCount();
            if (count == 0) {
                // 找不到开始插入
                log.info("找不到开始插入");
                // 先获取LovHaeder
                LovHeader lovHeader = jpaQueryFactory.selectFrom(qLovHeader)
                        .where(qLovHeader.lovCode.eq(attrTypeLine.getAttrLovName()))
                        .fetchFirst();
                LovLine lovLine = new LovLine();
                lovLine.setLovHeaderId(lovHeader.getLovHeaderId());
                lovLine.setLovName(itemAttrLovDTO.getLovLineValue());
                lovLine.setLovValue(itemAttrLovDTO.getLovLineValue());
                lovLine.setColNo(10);
                lovLine.setEnableFlag("Y");
                lovLine.setEffectiveStartDate(LocalDateTimeUtil.parse("1900-01-01T00:00:00").toLocalDate());
                lovLine.setEffectiveEndDate(LocalDateTimeUtil.parse("4712-12-31T00:00:00").toLocalDate());
                lovLineRepository.save(lovLine);
            }
        }
    }

    @Override
    public void syncItemAttrLovByBattery(ItemAttrLovDTO itemAttrLovDTO) {
        List<AttrTypeLine> attrTypeLines = jpaQueryFactory.select(qAttrTypeLine)
                .from(qAttrTypeLine)
                .innerJoin(qAttrTypeHeader)
                .on(qAttrTypeLine.attrTypeHeaderId.eq(qAttrTypeHeader.attrTypeHeaderId)
                        .and(qAttrTypeHeader.isDeleted.eq(0))
                )
                .innerJoin(qAttrLineMap)
                .on(qAttrLineMap.attrLineId.eq(qAttrTypeLine.attrLineId).and(qAttrLineMap.isDeleted.eq(0)))
                .where(qAttrTypeHeader.attrTypeCode.like("BATTR_TYPE%"))
                .where(qAttrLineMap.srcAttrId.eq(itemAttrLovDTO.getSrcAttrId()))
                .fetch();
        for (AttrTypeLine attrTypeLine : attrTypeLines) {
            long count = jpaQueryFactory.select(qLovLine)
                    .from(qLovHeader)
                    .leftJoin(qLovLine)
                    .on(qLovLine.lovHeaderId.eq(qLovHeader.lovHeaderId).and(qLovLine.isDeleted.eq(0)))
                    .where(qLovHeader.lovCode.eq(attrTypeLine.getAttrLovName()))
                    .where(qLovLine.lovName.upper().eq(Optional.ofNullable(itemAttrLovDTO.getLovLineValue()).orElse("")
                            .toUpperCase()))
                    .fetchCount();
            if (count == 0) {
                // 找不到开始插入
                log.info("找不到开始插入");
                // 先获取LovHaeder
                LovHeader lovHeader = jpaQueryFactory.selectFrom(qLovHeader)
                        .where(qLovHeader.lovCode.eq(attrTypeLine.getAttrLovName()))
                        .fetchFirst();
                LovLine lovLine = new LovLine();
                lovLine.setLovHeaderId(lovHeader.getLovHeaderId());
                lovLine.setLovName(itemAttrLovDTO.getLovLineValue());
                lovLine.setLovValue(itemAttrLovDTO.getLovLineValue());
                lovLine.setColNo(10);
                lovLine.setEnableFlag("Y");
                lovLine.setEffectiveStartDate(LocalDateTimeUtil.parse("1900-01-01T00:00:00").toLocalDate());
                lovLine.setEffectiveEndDate(LocalDateTimeUtil.parse("4712-12-31T00:00:00").toLocalDate());
                lovLineRepository.save(lovLine);
            }
        }
    }

    @Override
    public void clearCache(String code) {
        Cache lovHeaderCache = caffeineCacheManager.getCache("lovHeader");
        lovHeaderCache.clear();
        lovCacheService.clearLovHeaderCache(code);
    }
}
