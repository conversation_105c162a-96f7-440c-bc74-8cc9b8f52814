package com.trinasolar.scp.system.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.UserUtil;
import com.trinasolar.scp.system.domain.convert.DataPrivilegeDEConvert;
import com.trinasolar.scp.system.domain.dto.DataPrivilegeDTO;
import com.trinasolar.scp.system.domain.entity.DataPrivilege;
import com.trinasolar.scp.system.domain.entity.LovLine;
import com.trinasolar.scp.system.domain.entity.QDataPrivilege;
import com.trinasolar.scp.system.domain.query.DataPrivilegeQuery;
import com.trinasolar.scp.system.domain.save.DataPrivilegeSaveDTO;
import com.trinasolar.scp.system.service.repository.DataPrivilegeRepository;
import com.trinasolar.scp.system.service.service.DataPrivilegeService;
import com.trinasolar.scp.system.service.service.LovLineService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据权限表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-17 15:39:12
 */
@Slf4j
@Service("dataPrivilegeService")
public class DataPrivilegeServiceImpl implements DataPrivilegeService {
    @Autowired
    DataPrivilegeRepository repository;
    @Autowired
    LovLineService lovLineService;
    @Autowired
    DataPrivilegeDEConvert dataPrivilegeDEConvert;

    @Override
    public Page<DataPrivilegeDTO> queryByPage(DataPrivilegeQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<DataPrivilege> dataPrivilegePage = repository.findAll(booleanBuilder, pageable);
        return new PageImpl<>(dataPrivilegeDEConvert.toDto(dataPrivilegePage.getContent()), pageable, dataPrivilegePage.getTotalElements());
    }

    /**
     * 构建查询条件
     *
     * @param query
     * @return
     */
    private BooleanBuilder buildWhere(DataPrivilegeQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QDataPrivilege dataPrivilege = QDataPrivilege.dataPrivilege;

        if (StringUtils.isNotBlank(query.getPrivilegeType())) {
            booleanBuilder.and(dataPrivilege.privilegeType.eq(query.getPrivilegeType()));
        }
        if (StringUtils.isNotBlank(query.getUserId())) {
            booleanBuilder.and(dataPrivilege.userId.eq(query.getUserId()));
        }
        if (!Objects.isNull(query.getDataId())) {
            booleanBuilder.and(dataPrivilege.dataId.eq(query.getDataId()).or(dataPrivilege.dataId.eq(-1L)));
        }
        return booleanBuilder;
    }

    @Override
    public DataPrivilegeDTO queryById(Long id) {
        DataPrivilege queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        DataPrivilegeDTO result = new DataPrivilegeDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DataPrivilegeDTO save(DataPrivilegeSaveDTO saveDTO) {
        DataPrivilege newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new DataPrivilege());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void batchSave(List<DataPrivilegeSaveDTO> dtos) {
        repository.saveAll(dataPrivilegeDEConvert.toDataPrivilege(dtos));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @SneakyThrows
    @Override
    public void export(DataPrivilegeQuery query, HttpServletResponse response) {
        List<DataPrivilegeDTO> dtos = queryByPage(query).getContent();
        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "数据权限表", "数据权限表", excelPara.getSimpleHeader(), excelData);
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String importData(MultipartFile multipartFile, ExcelPara excelPara) {
        String batchNo = UUID.randomUUID().toString();
        List<DataPrivilegeDTO> excelData =
                ExcelUtils.readExcel(multipartFile.getInputStream(), null, DataPrivilegeDTO.class, excelPara);

        log.info("{}条数据，开始存储数据库！", excelData.size());
        //校验 todo
        repository.saveAll(dataPrivilegeDEConvert.toEntity(excelData));
        log.info("存储数据库成功！");

        return batchNo;
    }

    @Override
    public List<DataPrivilegeDTO> queryByList(DataPrivilegeQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        List<DataPrivilege> dataPrivileges = IterableUtils.toList(repository.findAll(booleanBuilder));
        List<DataPrivilegeDTO> resultList = dataPrivilegeDEConvert.toDto(dataPrivileges);
        List<Long> ids = resultList.stream().map(DataPrivilegeDTO::getDataId).distinct().collect(Collectors.toList());
        Map<Long, LovLine> lovMap = lovLineService.findAllById(ids).stream().collect(Collectors.toMap(LovLine::getLovLineId, Function.identity()));
        resultList.forEach(data -> {
            if (lovMap.containsKey(data.getDataId())) {
                LovLine lovLine = lovMap.get(data.getDataId());
                data.copyData(lovLine);
            }
        });
        return resultList;
    }

    @Override
    public List<String> getPrivilegeCodes(String privilegeType) {
        List<DataPrivilege> list = repository.findAllByUserIdAndPrivilegeType(UserUtil.getUser().getId(), privilegeType);
        return list.stream().map(DataPrivilege::getDataCode).collect(Collectors.toList());
    }
}
