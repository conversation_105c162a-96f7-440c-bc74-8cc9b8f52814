package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.DailyRates;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 汇率表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-29 09:01:50
 */
@Repository
public interface DailyRatesRepository extends JpaRepository<DailyRates, Long>, QuerydslPredicateExecutor<DailyRates> {
}
