package com.trinasolar.scp.system.service.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.google.common.collect.Lists;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.DataColumn;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.system.domain.dto.ErpApprovalSupplierDTO;

import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import static com.trinasolar.scp.common.api.util.ExcelUtils.getEntityColumns;
import static com.trinasolar.scp.common.api.util.ExcelUtils.getList;

public class ReadExcelAndCheckUtils {
    public static List<ErpApprovalSupplierDTO> readExcelData(InputStream inputStream, String sheetName, Class<ErpApprovalSupplierDTO> clazz, ExcelPara excelPara) {
        List<LinkedHashMap<Integer, Object>> list = null;
        ReadSheet readSheet = new ReadSheet();
        List<ReadSheet> sheetList = new ArrayList();
        if (sheetName != null && !"0".equals(sheetName)) {
            readSheet.setSheetName(sheetName);
        } else {
            readSheet.setSheetNo(0);
        }

        sheetList.add(readSheet);
        SyncManySheetNameReadListener syncReadListener = new SyncManySheetNameReadListener();
        ((ExcelReaderBuilder)((ExcelReaderBuilder) EasyExcel.read(inputStream).headRowNumber(1)).registerReadListener(syncReadListener)).autoCloseStream(true).build().read(sheetList).finish();
        Map<String, List<LinkedHashMap<Integer, Object>>> data = syncReadListener.getMap();
        excelPara.setImportedHeader(syncReadListener.getHeader());
        //比较是否存在错误的字段
        List<DataColumn> column = Arrays.asList(excelPara.getColumnArray());
        List<String> titles = column.stream().map(DataColumn::getTitle).distinct().collect(Collectors.toList());
        for (String importTitle:excelPara.getImportedHeader().values()){
            if (!titles.contains(importTitle)){
               throw new BizException(String.format("导入文件标题{%s}错误，请核查标题名称",importTitle));
            }
        }

        if (data != null && !data.isEmpty()) {
            list = (List)data.values().stream().findFirst().orElse((List<LinkedHashMap<Integer, Object>>)null);
            if (excelPara == null) {
                ExcelPara tmpPara = new ExcelPara();
                tmpPara.setColumns(getEntityColumns(clazz));
                return getList(list, clazz, tmpPara);
            } else {
                return getList(list, clazz, excelPara);
            }
        } else {
            return Lists.newArrayList();
        }
    }

    protected static class SyncManySheetNameReadListener extends AnalysisEventListener<Object> {
        private Map<String, List<LinkedHashMap<Integer, Object>>> map = new HashMap();
        private Map<Integer, String> header = new HashMap();

        public SyncManySheetNameReadListener() {
        }

        @Override
        public void invoke(Object object, AnalysisContext context) {
            ((List)this.map.computeIfAbsent(context.readSheetHolder().getSheetName(), (a) -> {
                return new ArrayList();
            })).add((LinkedHashMap)object);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
        }

        public Map<String, List<LinkedHashMap<Integer, Object>>> getMap() {
            return this.map;
        }

        public Map<Integer, String> getHeader() {
            return this.header;
        }

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            this.header.clear();
            this.header.putAll(headMap);
        }
    }
}
