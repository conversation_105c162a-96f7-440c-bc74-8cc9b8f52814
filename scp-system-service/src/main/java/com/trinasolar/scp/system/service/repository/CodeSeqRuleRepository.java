package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.CodeSeqRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 单据序列规则定义表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-09 15:05:51
 */
@Repository
public interface CodeSeqRuleRepository extends JpaRepository<CodeSeqRule, Long>, QuerydslPredicateExecutor<CodeSeqRule> {

    List<CodeSeqRule> findAllBySeqIdOrderByRuleOrderAsc(Long id);
}
