package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.SupplierDTO;
import com.trinasolar.scp.system.domain.query.SupplierQuery;
import com.trinasolar.scp.system.domain.save.SupplierSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 供应商信息 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-28 15:29:29
 */
public interface SupplierService {
    /**
     * 分页获取供应商信息
     *
     * @param query 查询对象
     * @return 供应商信息分页对象
     */
    Page<SupplierDTO> queryByPage(SupplierQuery query);

    /**
     * 分页获取供应商信息
     *
     * @param query 查询对象
     * @return 供应商信息分页对象
     */
    List<SupplierDTO> queryByList(SupplierQuery query);

    /**
     * 根据主键获取供应商信息详情
     *
     * @param id 主键
     * @return 供应商信息详情
     */
    SupplierDTO queryById(Long id);

    /**
     * 保存或更新供应商信息
     *
     * @param saveDTO 供应商信息保存对象
     * @return 供应商信息对象
     */
    SupplierDTO save(SupplierSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除供应商信息
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query
     * @param response
     */
    void export(SupplierQuery query, HttpServletResponse response);

    /**
     * 导入
     *
     * @param multipartFile
     * @return 批次号
     */
    String importData(MultipartFile multipartFile);
}

