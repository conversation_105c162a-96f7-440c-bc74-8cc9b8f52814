package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.DataPrivilege;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数据权限表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-17 15:39:12
 */
@Repository
public interface DataPrivilegeRepository extends JpaRepository<DataPrivilege, Long>, QuerydslPredicateExecutor<DataPrivilege> {
    List<DataPrivilege> findAllByUserIdAndPrivilegeType(String userId, String privilegeType);
}
