package com.trinasolar.scp.system.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.system.domain.dto.CodeSeqDTO;
import com.trinasolar.scp.system.domain.entity.CodeSeq;
import com.trinasolar.scp.system.domain.entity.CodeSeqRule;
import com.trinasolar.scp.system.domain.query.CodeSeqQuery;
import com.trinasolar.scp.system.domain.save.CodeSeqSaveDTO;
import com.trinasolar.scp.system.service.enums.Constants;
import com.trinasolar.scp.system.service.repository.CodeSeqRepository;
import com.trinasolar.scp.system.service.repository.CodeSeqRuleRepository;
import com.trinasolar.scp.system.service.service.CodeSeqService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 单据序列主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-09 15:05:46
 */
@Slf4j
@Service("codeSeqService")
public class CodeSeqServiceImpl implements CodeSeqService {
    @Autowired
    CodeSeqRepository repository;

    @Autowired
    CodeSeqRuleRepository codeSeqRuleRepository;

    @Autowired
    StringRedisTemplate redisTemplate;

    @Override
    public Page<CodeSeq> queryByPage(CodeSeqQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        return repository.findAll(booleanBuilder, pageable);
    }

    @Override
    public CodeSeqDTO queryById(Long id) {
        CodeSeq queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        CodeSeqDTO result = new CodeSeqDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CodeSeqDTO save(CodeSeqSaveDTO saveDTO) {
        CodeSeq newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new CodeSeq());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    public String createCodeSeq(CodeSeqQuery query) {

        CodeSeq codeSeq = repository.findBySeqCode(query.getSeqCode());
        if (codeSeq == null) {
            throw new BizException("无此编码");
        }

        List<CodeSeqRule> rules = codeSeqRuleRepository.findAllBySeqIdOrderByRuleOrderAsc(codeSeq.getId());
        List<String> paramList = new ArrayList<>();
        if (query.getCodes() != null) {
            paramList = Arrays.asList(query.getCodes().split(","));
        }

        String codeNo = "";

        int paramCodeIndx = 0;
        for (CodeSeqRule rule : rules) {
            codeNo += getCodeValue(rule, codeNo, query.getIncludeDate());
            if (Constants.paramCode.equals(rule.getRuleField()) && !paramList.isEmpty()) {
                codeNo += paramList.get(paramCodeIndx);
                paramCodeIndx++;
            }
        }
        return codeNo;
    }

    public String getCodeValue(CodeSeqRule rule, String codeNo, LocalDateTime includeDate) {
        switch (rule.getRuleField()) {
            case Constants.fixedChar:
                return rule.getRuleFieldValue();
            case Constants.dateFormat:
                if (includeDate == null) {
                    includeDate = LocalDateTime.now();
                }
                return includeDate.format(DateTimeFormatter.ofPattern(rule.getDatePattern()));
            case Constants.seq:
                return getSeqNo(rule, codeNo);
            default:
                return "";
        }
    }

    private String getSeqNo(CodeSeqRule seqEntity, String codeNo) {
        String redisSeqNoKey = codeNo.concat(":seq");
        int seqIncrement = seqEntity.getSeqIncrement() == null ? 1 : seqEntity.getSeqIncrement();
        Long increment = redisTemplate.opsForValue().increment(redisSeqNoKey, seqIncrement);
        return String.format("%0" + Optional.ofNullable(seqEntity.getSeqLength()).orElse(1) + "d", increment);
    }

}
