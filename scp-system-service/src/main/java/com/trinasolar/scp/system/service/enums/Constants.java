package com.trinasolar.scp.system.service.enums;

public class Constants {
    public static final String seq = "seq";//序列号

    public static final String dateFormat = "dateFormat";//日期格式

    public static final String fixedChar = "fixedChar";//固定字符

    public static final String paramCode = "paramCode";

    public static final String defaultTenantCode = "liuhe";

    public static final String isAvailableFlag = "Y";

    public static final String dictKey = "dictKey";

    public static final String YES = "Y";

    public static final String NO = "N";

    public static final Integer unDelete = 0;

    public static final Integer deleted = 1;

    public static final String uploadOk = "ok";

    public static final String uploadError = "error";

    public static final String redisVaildCodePrefix = "vaildCode-";

    public static final String redisTokenPrefix = "token-";

    public static final Integer PAGE_SIZE = 600;

    public static final String BASE_PLACE = "base_place";

    /**
     * BOM服务
     */
    public static final String BOM_SERVICE = "scp-bom-api";

    /**
     *
     */
    public static final String REGEX_NUMBER = "^[1-9]+[0-9]*$";

    /**
     * 正则表达式：验证手机号
     */
    public static final String REGEX_MOBILE = "^((13[0-9])|(14[0-9])|(15[^4,\\D])|(16[3-6])|(17[0-9])|(18[0-9])|(19[89]))\\d{8}$";

    /**
     * 正则表达式：验证邮箱
     */
    public static final String REGEX_EMAIL = "^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$";

    public static final String ROLE_RES_MANAGE = "RES_MANAGE";//总负责人

    public static final String NOTICE_ESTATERE = "estatereSources";//地产原料采购员分配待办

    // 从erp同步汇率redis-KEY
    public static final String ERP_DAILY_RATES_SYNC_KEY = "ERP_DAILY_RATES_b6702fb19";

    public static final String ENABLE_ALL_FLAG = "ALL";
}
