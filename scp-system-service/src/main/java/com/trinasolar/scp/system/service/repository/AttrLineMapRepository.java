package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.AttrLineMap;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 属性行来源映射表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-05 19:21:39
 */
@Repository
public interface AttrLineMapRepository extends JpaRepository<AttrLineMap, Long>, QuerydslPredicateExecutor<AttrLineMap> {

    List<AttrLineMap> findBySrcAttrIdIn(List<String> srcAttrIds);

    List<AttrLineMap> findByAttrLineIdEquals(Long attrLineId);

    List<AttrLineMap> findByAttrLineIdInAndIsDeletedEquals(List<Long> attrLineIds, Integer isDeleted);
}
