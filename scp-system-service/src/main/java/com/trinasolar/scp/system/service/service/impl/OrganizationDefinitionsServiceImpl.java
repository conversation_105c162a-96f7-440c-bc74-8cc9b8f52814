package com.trinasolar.scp.system.service.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.system.domain.dto.ExternalInterfaceDTO;
import com.trinasolar.scp.system.domain.dto.OperatingUnitsDTO;
import com.trinasolar.scp.system.domain.dto.OrganizationDefinitionsDTO;
import com.trinasolar.scp.system.domain.entity.OrganizationDefinitions;
import com.trinasolar.scp.system.domain.entity.QOrganizationDefinitions;
import com.trinasolar.scp.system.domain.query.OrganizationDefinitionsQuery;
import com.trinasolar.scp.system.domain.save.OrganizationDefinitionsSaveDTO;
import com.trinasolar.scp.system.service.repository.OrganizationDefinitionsRepository;
import com.trinasolar.scp.system.service.service.ErpInterfaceService;
import com.trinasolar.scp.system.service.service.OperatingUnitsService;
import com.trinasolar.scp.system.service.service.OrganizationDefinitionsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-28 11:38:58
 */
@Slf4j
@Service("organizationDefinitionsService")
@CacheConfig(cacheNames = "OrganizationDefinitions", cacheManager = "caffeineCacheManager")
@RefreshScope
public class OrganizationDefinitionsServiceImpl implements OrganizationDefinitionsService {
    private static final int PAGE_SIZE = 600;

    @Autowired
    OrganizationDefinitionsRepository organizationDefinitionsRepository;

    @Autowired
    OperatingUnitsService operatingUnitsService;

    @Autowired
    ErpInterfaceService erpInterfaceService;

    @Value("${erp.interface.platform.url}")
    private String erpUrl;

    @Value("${erp.interface.la001.clientid}")
    private String la001ClientId;

    @Value("${erp.interface.la001.secret}")
    private String la001Secret;

    @Override
    public Page<OrganizationDefinitionsDTO> queryByPage(OrganizationDefinitionsQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<OrganizationDefinitions> all = organizationDefinitionsRepository.findAll(booleanBuilder, pageable);
        List<OrganizationDefinitionsDTO> collect = all.stream().map(item -> {
            OrganizationDefinitionsDTO dto = new OrganizationDefinitionsDTO();
            BeanUtils.copyProperties(item, dto);
            OperatingUnitsDTO operatingUnitsDTO = operatingUnitsService.queryById(Long.valueOf(dto.getOperatingUnit()));
            dto.setOperatingUnitName(operatingUnitsDTO.getOrgName());
            return dto;
        }).collect(Collectors.toList());
        return new PageImpl(collect, pageable, all.getTotalElements());
    }

    @Override
    @Cacheable(key = "#id", unless = "#result == null")
    public OrganizationDefinitionsDTO queryById(Long id) {
        OrganizationDefinitions queryObj = organizationDefinitionsRepository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        OrganizationDefinitionsDTO result = new OrganizationDefinitionsDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Override
    @CacheEvict(allEntries = true)
    public OrganizationDefinitionsDTO save(OrganizationDefinitionsSaveDTO saveDTO) {
        OrganizationDefinitions newObj;
        if (saveDTO.getOrganizationId() != null) {
            newObj = organizationDefinitionsRepository.getOne(saveDTO.getOrganizationId());
        } else {
            newObj = new OrganizationDefinitions();
        }

        BeanUtils.copyProperties(saveDTO, newObj);
        organizationDefinitionsRepository.save(newObj);

        return this.queryById(newObj.getOrganizationId());
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteById(Long id) {
        organizationDefinitionsRepository.deleteById(id);
    }

    @Override
    @Cacheable(key = "#code", unless = "#result == null")
    public OrganizationDefinitionsDTO queryByCode(String code) {
        OrganizationDefinitions queryObj = organizationDefinitionsRepository.findByOrganizationCode(code);
        if (queryObj == null) {
            return null;
        }

        OrganizationDefinitionsDTO result = new OrganizationDefinitionsDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Override
    public List<OrganizationDefinitionsDTO> queryByList(OrganizationDefinitionsQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        Iterable<OrganizationDefinitions> all = organizationDefinitionsRepository.findAll(booleanBuilder);
        List<OrganizationDefinitionsDTO> result = new ArrayList<>();
        for (OrganizationDefinitions organizationDefinitions : all) {
            OrganizationDefinitionsDTO dto = new OrganizationDefinitionsDTO();
            BeanUtils.copyProperties(organizationDefinitions, dto);
            result.add(dto);
        }
        return result;
    }


    @Override
    public List<OrganizationDefinitionsDTO> listForAps(OrganizationDefinitionsQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QOrganizationDefinitions q = QOrganizationDefinitions.organizationDefinitions;
        booleanBuilder.and(q.scpFlag.eq("Y").or(q.cellsScpFlag.eq("Y")));
        Iterable<OrganizationDefinitions> all = organizationDefinitionsRepository.findAll(booleanBuilder);
        List<OrganizationDefinitionsDTO> result = new ArrayList<>();
        for (OrganizationDefinitions organizationDefinitions : all) {
            OrganizationDefinitionsDTO dto = new OrganizationDefinitionsDTO();
            BeanUtils.copyProperties(organizationDefinitions, dto);
            result.add(dto);
        }
        return result;
    }

    @Override
    public void updateFlag(OrganizationDefinitionsSaveDTO saveDTO) {
        OrganizationDefinitions one = organizationDefinitionsRepository.getOne(saveDTO.getOrganizationId());
        if (one == null) {
            throw new BizException("未找到正确的组织");
        }
        one.setScpFlag(saveDTO.getScpFlag());
        one.setCellsScpFlag(saveDTO.getCellsScpFlag());
        organizationDefinitionsRepository.save(one);
    }

    @Override
    @CacheEvict(allEntries = true)
    public void sync() {
        // 获取最后更新时间
        JSONObject param = new JSONObject();
        param.put("last_update_date_from", "2001-01-01 00:00:00");
        param.put("last_update_date_to", LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
        param.put("pageSize", PAGE_SIZE);
        int pageNum = 1;

        for (; ; ) {
            param.put("pageNum", pageNum++);

            ExternalInterfaceDTO dto = new ExternalInterfaceDTO();
            dto.setUrl(erpUrl);
            dto.setPath("/la001/v1/orgainizationalUnit/list");
            dto.setClientId(la001ClientId);
            dto.setSecret(la001Secret);
            dto.setRequestBody(param.toJSONString());

            String json = erpInterfaceService.postForString(dto);
            JSONObject outJson = JSON.parseObject(json);
            JSONArray datas = outJson.getJSONArray("data");
            if (datas == null) {
                break;
            }
            List<OrganizationDefinitions> list = new ArrayList<>();
            for (int i = 0; i < datas.size(); i++) {
                JSONObject jsonObject = datas.getJSONObject(i);
                OrganizationDefinitions organizationDefinitions = jsonObject.toJavaObject(OrganizationDefinitions.class);
                String userDefinitionEnableDate = jsonObject.getString("userDefinitionEnableDate");
                if (StringUtils.isNotBlank(userDefinitionEnableDate)) {
                    organizationDefinitions.setUserDefinitionEnableDate(LocalDateTimeUtil.parseDate(
                            userDefinitionEnableDate.substring(0, 10)
                    ));
                }
                String disableDate = jsonObject.getString("disableDate");
                if (StringUtils.isNotBlank(disableDate)) {
                    organizationDefinitions.setDisableDate(LocalDateTimeUtil.parseDate(
                            disableDate.substring(0, 10)
                    ));
                }

                list.add(organizationDefinitions);
            }

            for (OrganizationDefinitions obj : list) {
                // 查找有没有相同的
                OrganizationDefinitions findObj = organizationDefinitionsRepository.findById(obj.getOrganizationId()).orElse(null);
                if (findObj == null || findObj.getOrganizationId() == null) {
                    organizationDefinitionsRepository.save(obj);
                } else {
                    BeanUtils.copyProperties(obj, findObj, "orgId", "scpFlag", "cellsScpFlag");
                    organizationDefinitionsRepository.save(findObj);
                }
            }
        }
    }

    /**
     * 构建查询条件
     *
     * @param query
     * @return
     */
    private BooleanBuilder buildWhere(OrganizationDefinitionsQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QOrganizationDefinitions qOrganizationDefinitions = QOrganizationDefinitions.organizationDefinitions;
        if (CollectionUtils.isNotEmpty(query.getOrganizationIds())) {
            booleanBuilder.and(qOrganizationDefinitions.organizationId.in(query.getOrganizationIds()));
        }
        if (query.getOrganizationId() != null) {
            booleanBuilder.and(qOrganizationDefinitions.organizationId.eq(query.getOrganizationId()));
        }
        if (StringUtils.isNotBlank(query.getOrganizationCode())) {
            booleanBuilder.and(qOrganizationDefinitions.organizationCode.likeIgnoreCase("%" + query.getOrganizationCode() + "%"));
        }
        if (StringUtils.isNotBlank(query.getOrganizationName())) {
            booleanBuilder.and(qOrganizationDefinitions.organizationName.likeIgnoreCase("%" + query.getOrganizationName() + "%"));
        }
        if (query.getOperatingUnit() != null) {
            booleanBuilder.and(qOrganizationDefinitions.operatingUnit.eq(query.getOperatingUnit()));
        }
        if (StringUtils.isNotBlank(query.getScpFlag())) {
            booleanBuilder.and(qOrganizationDefinitions.scpFlag.eq(query.getScpFlag()));
        }
        if (StringUtils.isNotBlank(query.getCellsScpFlag())) {
            booleanBuilder.and(qOrganizationDefinitions.cellsScpFlag.eq(query.getCellsScpFlag()));
        }

        return booleanBuilder;
    }
}
