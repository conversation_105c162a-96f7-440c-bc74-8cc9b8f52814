package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.system.domain.dto.LanguageDTO;
import com.trinasolar.scp.system.domain.entity.Language;
import com.trinasolar.scp.system.domain.query.LanguageQuery;
import com.trinasolar.scp.system.domain.save.LanguageSaveDTO;
import org.springframework.data.domain.Page;

/**
 * 系统语言 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:17
 */
public interface LanguageService {
    Page<Language> queryByPage(LanguageQuery query);

    LanguageDTO queryById(Long id);

    LanguageDTO save(LanguageSaveDTO saveDTO);

    void deleteById(Long id);
}

