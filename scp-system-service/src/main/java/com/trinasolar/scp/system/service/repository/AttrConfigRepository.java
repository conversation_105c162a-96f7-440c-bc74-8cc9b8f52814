package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.AttrConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 系统扩展字段配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 13:58:46
 */
@Repository
public interface AttrConfigRepository extends JpaRepository<AttrConfig, Long>, QuerydslPredicateExecutor<AttrConfig> {
    List<AttrConfig> findBySenceAndAttrHeaderId(String sence, Long lovHeaderId);

    List<AttrConfig> findByAttrHeaderId(Long attrHeaderId);
}
