package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.ErpApprovalSupplierFixLotQty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 合格供应商整车数或整柜数表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-09 07:56:06
 */
@Repository
public interface ErpApprovalSupplierFixLotQtyRepository extends JpaRepository<ErpApprovalSupplierFixLotQty, Long>, QuerydslPredicateExecutor<ErpApprovalSupplierFixLotQty> {
    @Query(nativeQuery = true, value = "delete from sys_erp_approval_supplier_fix_lot_qty where  id IN  (:ids)")
    @Modifying
    void realDelete(@Param("ids") List<Long> ids);
}
