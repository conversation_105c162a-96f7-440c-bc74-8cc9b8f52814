package com.trinasolar.scp.system.service.service;


import com.trinasolar.scp.system.domain.dto.AttrTypeHeaderDTO;
import com.trinasolar.scp.system.domain.dto.AttrTypeLineDTO;
import com.trinasolar.scp.system.domain.entity.AttrTypeHeader;
import com.trinasolar.scp.system.domain.entity.AttrTypeLine;
import com.trinasolar.scp.system.domain.query.AttrTypeLineQuery;
import com.trinasolar.scp.system.domain.save.AttrTypeLineSaveDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * lov头表(LovLine)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-24 10:42:26
 */
public interface AttrTypeLineService {

    Page<AttrTypeLine> queryByPage(AttrTypeLineQuery query);

    AttrTypeLineDTO queryById(Long id);

    AttrTypeLineDTO save(AttrTypeLineSaveDTO saveDTO);

    void deleteById(Long id);

    void savesByHeader(List<AttrTypeLineSaveDTO> lineSaveDTOS, AttrTypeHeader attrTypeHeader);

    List<AttrTypeLineDTO> getByHeaderId(Long attrTypeHeaderId);

    List<AttrTypeLineDTO> queryByHeaderCode(String code);

    AttrTypeLineDTO getLineByQuery(AttrTypeLineQuery query);

    void syncAttrTypeLov();

    List<AttrTypeLineDTO> queryByAttrLineId(List<Long> ids);

    List<AttrTypeLineDTO> queryList(AttrTypeLineQuery query);

    List<AttrTypeLineDTO> findItemTransToLovAttrLines(AttrTypeHeaderDTO categorySegment5);
}
