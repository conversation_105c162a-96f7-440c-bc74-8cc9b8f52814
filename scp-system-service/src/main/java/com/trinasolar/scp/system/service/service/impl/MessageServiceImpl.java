package com.trinasolar.scp.system.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.system.domain.dto.MessageDTO;
import com.trinasolar.scp.system.domain.entity.Message;
import com.trinasolar.scp.system.domain.entity.QMessage;
import com.trinasolar.scp.system.domain.query.MessageQuery;
import com.trinasolar.scp.system.domain.save.MessageSaveDTO;
import com.trinasolar.scp.system.service.repository.MessageRepository;
import com.trinasolar.scp.system.service.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

/**
 * 系统消息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
@Slf4j
@Service("messageService")
public class MessageServiceImpl implements MessageService {
    QMessage q = QMessage.message;
    @Autowired
    MessageRepository messageRepository;

    @Override
    public Page<Message> queryByPage(MessageQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        if (query != null) {
            if (StringUtils.isNotEmpty(query.getLang())) {
                booleanBuilder.and(q.lang.like("%" + query.getLang() + "%"));
            }
            if (StringUtils.isNotEmpty(query.getCode())) {
                booleanBuilder.and(q.code.like("%" + query.getCode() + "%"));
            }
        }

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        return messageRepository.findAll(booleanBuilder, pageable);
    }

    @Override
    public MessageDTO queryById(Long id) {
        Message queryObj = messageRepository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        MessageDTO result = new MessageDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Override
    public MessageDTO save(MessageSaveDTO saveDTO) {
        Message getByLangAndCode = messageRepository.getByLangAndCode(saveDTO.getLang(), saveDTO.getCode());

        Message newObj;
        if (getByLangAndCode != null) {
            newObj = getByLangAndCode;
        } else {
            newObj = new Message();
        }

        BeanUtils.copyProperties(saveDTO, newObj, "messageId");
        messageRepository.save(newObj);

        return this.queryById(newObj.getMessageId());
    }

    @Override
    public void deleteById(Long id) {
        messageRepository.deleteById(id);
    }

    @Override
    public String getDescriptinoByLangAndCode(String lang, String code) {
        Message byLangAndCode = messageRepository.getByLangAndCode(lang, code);
        if (byLangAndCode == null) {
            return code;
        }
        return byLangAndCode.getDescription();
    }
}
