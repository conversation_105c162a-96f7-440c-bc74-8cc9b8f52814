package com.trinasolar.scp.system.service.repository;

import com.trinasolar.scp.system.domain.entity.LovLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * lov行表(LovLine)数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-24 10:42:24
 */
@Repository
public interface LovLineRepository
        extends JpaRepository<LovLine, Long>, QuerydslPredicateExecutor<LovLine> {
    @Query("select l from LovLine l where l.lovHeaderId = ?1 and l.isDeleted = 0")
    List<LovLine> findByLovHeaderId(Long lovHeaderId);
}

