package com.trinasolar.scp.system.service.service;

import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.system.domain.dto.LovLineTlTableLineDTO;
import com.trinasolar.scp.system.domain.dto.LovTlDTO;
import com.trinasolar.scp.system.domain.entity.LovTl;
import com.trinasolar.scp.system.domain.query.LovTlQuery;
import com.trinasolar.scp.system.domain.save.LovLineTlTableLinesSaveDTO;
import com.trinasolar.scp.system.domain.save.LovTlSaveDTO;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

/**
 * 系统LOV多语言表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-29 10:33:16
 */
public interface LovTlService {
    Page<LovTl> queryByPage(LovTlQuery query);

    LovTlDTO queryById(Long id);

    LovTlDTO save(LovTlSaveDTO saveDTO);

    void deleteById(Long id);

    Map<Long, String> mapByLangAndPkTypeAndPkIds(String lang, String pkType, List<Long> pkIds);

    List<LovTlDTO> findAllByPkTypeAndPkIdIn(LovTlQuery query);

    List<LovLineTlTableLineDTO> getLovLineTlTableLines(IdDTO idDTO);

    void saveLovLineTlTableLines(LovLineTlTableLinesSaveDTO lines);
}

