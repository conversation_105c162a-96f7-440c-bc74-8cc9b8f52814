package com.trinasolar.scp.system.service.service;


import com.trinasolar.scp.system.domain.dto.AttrTypeMapDTO;
import com.trinasolar.scp.system.domain.entity.AttrTypeMap;
import com.trinasolar.scp.system.domain.query.AttrTypeMapQuery;
import com.trinasolar.scp.system.domain.save.AttrTypeMapSaveDTO;
import org.springframework.data.domain.Page;

/**
 * LovMap表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-24 10:42:26
 */
public interface AttrTypeMapService {

    Page<AttrTypeMap> queryByPage(AttrTypeMapQuery query);

    AttrTypeMapDTO queryById(Long id);

    AttrTypeMapDTO save(AttrTypeMapSaveDTO saveDTO);

    void deleteById(Long id);
}
